name: Create Initial Branch Prerelease

on:
  push:
    branches-ignore:
      - main

env:
  GH_PAT: ${{ secrets.DEVOPS_GITHUB_TOKEN }}
  FT_TOKEN: ${{ secrets.FT_TOKEN }}
  ORG_NAME: wealthcom

jobs:
  build-and-prerelease:
    if: ${{ !contains(github.event.head_commit.message, '[skip-pre-merge-ci]') }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/
    permissions:
      contents: write
      packages: write
      pull-requests: write
      actions: read
    defaults:
      run:
        working-directory: ${{ github.workspace }}
    outputs:
      pr_number: ${{ steps.get_pr_number.outputs.pr_number }}
      prerelease_version: ${{ steps.output_version.outputs.prerelease_version }}
      changes: ${{ steps.git-check.outputs.changes }}
    steps:
      - name: Checkout PR branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.head_ref || github.ref }}

      - uses: nrwl/nx-set-shas@v3
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Configure npm for GitHub Packages
        run: |
          echo "@wealthcom:registry=https://npm.pkg.github.com" >> .npmrc
          echo "//npm.pkg.github.com/:_authToken=$GH_PAT" >> .npmrc
          echo "@fortawesome:registry=https://npm.fontawesome.com/" >> .npmrc
          echo "//npm.fontawesome.com/:_authToken=$FT_TOKEN" >> .npmrc

      - run: yarn install --immutable
        env:
          GH_PAT: $GH_PAT
          FT_TOKEN: ${{ secrets.FT_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.DEVOPS_GITHUB_TOKEN }} # Add this line

      # Deprecated https://nx.dev/deprecated/workspace-lint#workspacelint
      # - run: yarn nx workspace-lint
      # - run: yarn nx format:check
      # - run: yarn nx affected --target=lint --parallel=3 --exclude=demo-app --exclude=storybook-host --verbose

      # - name: Build affected projects
      #   run: yarn nx affected --target=build --parallel=3

      - name: Configure npm for GitHub Packages and Font Awesome
        run: |
          echo "@wealthcom:registry=https://npm.pkg.github.com" > .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.DEVOPS_GITHUB_TOKEN }}" >> .npmrc
          echo "@fortawesome:registry=https://npm.fontawesome.com/" >> .npmrc
          echo "//npm.fontawesome.com/:_authToken=${{ secrets.FT_TOKEN }}" >> .npmrc
          cat .npmrc # Debug step to verify content (remove in production)
        env:
          GH_PAT: ${{ secrets.DEVOPS_GITHUB_TOKEN }}
          FT_TOKEN: ${{ secrets.FT_TOKEN }}

      - name: Debug - Show changed files
        run: git diff --name-only origin/main...HEAD

      - name: Check for significant changes
        id: check_changes
        run: |
          git fetch origin main
          SIGNIFICANT_CHANGES=$(git diff --name-only origin/main...HEAD -- packages/ | grep -v "package.json" | wc -l)
          echo "significant_changes=$SIGNIFICANT_CHANGES" >> $GITHUB_OUTPUT

      - name: Make script executable
        run: chmod +x ./process-single-app.sh

      - name: Process affected apps
        if: steps.check_changes.outputs.significant_changes != '0'
        run: |
          set -e
          git fetch origin main:main

          echo "Checking for changes in the packages/ directory"
          PACKAGES_CHANGED=$(git diff --name-only origin/main...HEAD -- packages/ | wc -l)

          if [ "$PACKAGES_CHANGED" -eq "0" ]; then
            echo "No changes detected in packages/ directory. Skipping further processing."
            exit 0
          fi

          echo "Changes detected in packages/ directory. Proceeding with processing."

          directly_changed_packages=$(git diff --name-only origin/main...HEAD -- packages/ | 
            grep -v "package.json" | 
            awk -F'/' '{print $2}' | 
            sort -u)

          echo "Directly changed packages based on file paths: $directly_changed_packages"

          if [ -z "$directly_changed_packages" ]; then
            echo "No changes detected in specific package directories. Skipping further processing."
            exit 0
          fi

          BRANCH=${GITHUB_REF#refs/heads/}
          for pkg in $directly_changed_packages; do
              pkg=$(echo "$pkg" | xargs) # Trim whitespace
              if [ ! -z "$pkg" ] && [ "$pkg" != "demo-app" ]; then
                  echo "Processing package: $pkg"
                  if [ -d "packages/$pkg" ]; then
                      source ./process-single-app.sh "$pkg" publish "$BRANCH"
                  else
                      echo "Warning: Directory packages/$pkg does not exist, skipping"
                  fi
              else
                  echo "Skipping package: $pkg"
              fi
          done
        env:
          GH_PAT: ${{ secrets.DEVOPS_GITHUB_TOKEN }}
          FT_TOKEN: ${{ secrets.FT_TOKEN }}
          ORG_NAME: wealthcom

      - name: Check for changes
        id: git-check
        run: |
          git status
          git add packages/*/package.json
          git diff --staged --quiet || echo "::set-output name=changes::true"

      - name: Get PR Number from Event
        id: get_pr_number
        run: |
          # Extract current branch name - handle both push and PR events
          if [ -n "${GITHUB_HEAD_REF}" ]; then
            branch="${GITHUB_HEAD_REF}"
          else
            branch=${GITHUB_REF#refs/heads/}
          fi
          echo "Looking for PR associated with branch: ${branch}"

          # Try to find PR using GitHub CLI (open PRs first)
          prs=$(gh pr list --state open --json number,headRefName --repo ${{ github.repository }})
          pr_number=$(echo "${prs}" | jq -r ".[] | select(.headRefName==\"${branch}\") | .number")

          # If no open PR found, try all states
          if [ -z "${pr_number}" ] || [ "${pr_number}" = "null" ]; then
            echo "No open PR found, checking all PR states..."
            all_prs=$(gh pr list --state all --json number,headRefName --repo ${{ github.repository }})
            pr_number=$(echo "${all_prs}" | jq -r ".[] | select(.headRefName==\"${branch}\") | .number" | head -1)
          fi
          # Fallback to GitHub API if CLI didn't find the PR
          if [ -z "${pr_number}" ] || [ "${pr_number}" = "null" ]; then
            echo "GitHub CLI didn't find PR, trying API fallback..."
            api_response=$(curl -s -H "Authorization: token ${GITHUB_TOKEN}" \
              "https://api.github.com/repos/${{ github.repository }}/pulls?state=all&head=${{ github.repository_owner }}:${branch}")
            pr_number=$(echo "${api_response}" | jq -r '.[0].number // empty')
          fi
          # Clean up null values
          if [ "${pr_number}" = "null" ]; then
            pr_number=""
          fi
          if [ -n "${pr_number}" ]; then
            echo "Found PR #${pr_number} for branch ${branch}"
          else
            echo "No PR found for branch ${branch}"
          fi

          # Set output variable for PR number
          echo "pr_number=${pr_number}" >> $GITHUB_OUTPUT
        env:
          GITHUB_TOKEN: ${{ secrets.DEVOPS_GITHUB_TOKEN }}

      - name: Add a comment if there are changes
        if: steps.git-check.outputs.changes == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            const newVersion = process.env.NEW_VERSIONS;
            const prNumber = "${{ steps.get_pr_number.outputs.pr_number }}";

            if (prNumber) {
              await github.rest.issues.createComment({
                issue_number: prNumber,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `\`\`\`\n${newVersion}\n\`\`\``
              });
            } else {
              console.log('No pull request associated with this push.');
            }

      - name: Output version if there are changes
        id: output_version
        if: steps.git-check.outputs.changes == 'true'
        run: |
          echo "prerelease_version=$NEW_VERSIONS" >> $GITHUB_OUTPUT

  create-web-pr-with-prerelease:
    runs-on: ubuntu-latest
    needs: build-and-prerelease
    steps:
      - uses: 8BitJonny/gh-get-current-pr@3.0.0
        id: PR

      - name: Check if a new version or common-component PR exists
        run: |
          if [ "${{ needs.build-and-prerelease.outputs.changes }}" == "true" ]; then
            echo "VERSION=${{ needs.build-and-prerelease.outputs.prerelease_version }}" >> $GITHUB_ENV
          else
            echo "No new version found, exiting..."
            exit 1
          fi

          if [ -z "${{ needs.build-and-prerelease.outputs.pr_number }}" ]; then
            echo "No PR number found, exiting..."
            exit 1
          fi

      - name: Clone web repository
        env:
          GITHUB_TOKEN: ${{ secrets.DEVOPS_CREATE_PR }}
        run: |
          git clone https://x-access-token:${GITHUB_TOKEN}@github.com/wealthcom/wealth-web.git

      - name: Set branch name, PR number and PR title
        run: |
          echo "BRANCH_NAME=common-components-${{ needs.build-and-prerelease.outputs.pr_number }}" >> $GITHUB_ENV
          echo "PR_NUMBER=${{ needs.build-and-prerelease.outputs.pr_number }}" >> $GITHUB_ENV
          echo "PR_TITLE=${{ steps.PR.outputs.pr_title }}" >> $GITHUB_ENV

      - name: Configure git
        run: |
          cd wealth-web
          git config user.name "${{ github.actor }}"
          git config user.email "${{ github.actor_id }}+${{ github.actor }}@users.noreply.github.com"

      - name: Create PR and install prerelease version
        id: create_pr
        env:
          GITHUB_TOKEN: ${{ secrets.DEVOPS_CREATE_PR }}
          FT_TOKEN: ${{ secrets.FT_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.DEVOPS_CREATE_PR }} # Add this line
        run: |
          cd wealth-web
          if [ "$(git ls-remote --heads origin $BRANCH_NAME )" != "" ]; then
            git checkout $BRANCH_NAME
          else
            git checkout -b $BRANCH_NAME
          fi 
          yarn add $VERSION
          git add .
          rm .husky/prepare-commit-msg
          git commit --no-verify -m "install prerelease version"
          git remote set-url origin https://x-access-token:${GITHUB_TOKEN}@github.com/wealthcom/wealth-web.git
          git push origin $BRANCH_NAME && echo "pushed commit to branch" || echo "Pushing failed, please resolve the issue manually" 
          pr_url="$(gh pr create --base main --head "$BRANCH_NAME" --title "$PR_TITLE" --body "new common component version installed, wealth-common-components PR [#$PR_NUMBER](https://github.com/wealthcom/wealth-common-components/pull/$PR_NUMBER)
          PR owner: ${{ github.actor }}")" || echo "PR already exists, exiting..." 
          echo "pr_url=$pr_url" >> $GITHUB_ENV

      - name: Add a comment if PR is created
        if: ${{ env.pr_url != '' }}
        uses: actions/github-script@v6
        with:
          script: |
            const url = process.env.pr_url;
            const prNumber = process.env.PR_NUMBER;
            await github.rest.issues.createComment({
              issue_number: prNumber,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: url
            });
