name: Main Build and Stable Release

on:
  push:
    branches:
      - main

env:
  GH_PAT: ${{ secrets.SEMANTIC_RELEASE_TOKEN_GH }}
  FT_TOKEN: ${{ secrets.FT_TOKEN }}

jobs:
  build-and-release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write

    defaults:
      run:
        working-directory: ${{ github.workspace }}
    outputs:
      new_version: ${{ steps.git-check.outputs.new_version }}
      changes: ${{ steps.git-check.outputs.changes }}
    steps:
      - name: Checkout branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.head_ref || github.ref }}

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@wealthcom'

      - name: Configure npm for GitHub Packages
        run: |
          echo "@wealthcom:registry=https://npm.pkg.github.com" >> .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.SEMANTIC_RELEASE_TOKEN_GH }}" >> .npmrc
          echo "@fortawesome:registry=https://npm.fontawesome.com/" >> .npmrc
          echo "//npm.fontawesome.com/:_authToken=${{ secrets.FT_TOKEN }}" >> .npmrc
          cat .npmrc # Debug step to verify content (remove in production)
        env:
          GH_PAT: ${{ secrets.SEMANTIC_RELEASE_TOKEN_GH }}
          FT_TOKEN: ${{ secrets.FT_TOKEN }}

      - run: yarn install --immutable
        env:
          GH_PAT: ${{ secrets.SEMANTIC_RELEASE_TOKEN_GH }}
          FT_TOKEN: ${{ secrets.FT_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.SEMANTIC_RELEASE_TOKEN_GH }}

      - name: Build projects
        run: |
          set -e
          yarn nx affected --target=build --parallel=3 --base=origin/main~1 --head=origin/main --exclude=demo-app --exclude=storybook-host
        env:
          GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_TOKEN_GH }}

      - name: Setup Node 20 for release
        uses: actions/setup-node@v4
        with:
          node-version: '20.8.1'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@wealthcom'

      - name: Release projects
        run: |
          set -e
          yarn nx affected --target=release --parallel=false --base=origin/main~1 --head=origin/main --exclude=demo-app --exclude=storybook-host
        env:
          GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_TOKEN_GH }}

      - name: Check for changes
        id: git-check
        env:
          MESSAGE: ${{ github.event.head_commit.message }}
        run: |
          cd ${{ github.workspace }}
          git status
          git add packages/*/package.json
          if git diff --staged --quiet; then
            echo "changes=false" >> $GITHUB_OUTPUT
          else
            echo "Updated packages and their versions:"
            new_version=""
            for package in $(git diff --name-only origin/main -- packages/ | grep 'package.json'); do
              package_name=$(jq -r '.name' $package)
              version=$(jq -r '.version' $package)
              echo "Found Version: ${package_name}@${version}"
              new_version="${new_version}\n${package_name}@${version}"
            done
            
            commit_message="$MESSAGE"
            commit_message="$(echo "$commit_message" | head -1)"

            echo "changes=true" >> $GITHUB_OUTPUT
            echo "new_version=${new_version}" >> $GITHUB_OUTPUT
            echo "commit_message=${commit_message}" >> $GITHUB_OUTPUT
          fi

      - name: Send a message to slack with the stable release version
        if: steps.git-check.outputs.changes == 'true'
        id: slack
        uses: slackapi/slack-github-action@v1.27.0
        with:
          payload: |
            {
              "blocks": [{
                      "type": "section",
                      "text": {
                          "type": "mrkdwn",
                          "text": "*New Stable Release*"
                      }
                  }, {
                      "type": "divider"
                  }, {
                      "type": "section",
                      "text": {
                          "type": "mrkdwn",
                          "text": "*Packages:* ${{ steps.git-check.outputs.new_version }}"
                      },
                      "accessory": {
                          "type": "image",
                          "image_url": "https://cdn.pixabay.com/photo/2022/01/30/13/33/github-6980894_1280.png",
                          "alt_text": "github logo"
                      }
                  }, {
                      "type": "divider"
                  }, {
                      "type": "context",
                      "elements": [{
                              "type": "mrkdwn",
                              "text": "*Latest Commit:*\n<${{ github.event.head_commit.url }}|${{ steps.git-check.outputs.commit_message }}>"
                          }
                      ]
                  }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

  create-web-pr-with-stable:
    runs-on: ubuntu-latest
    needs: build-and-release
    outputs:
      visualizer: ${{ steps.check-if-visualizer.outputs.visualizer }}
      pr_number: ${{ steps.set-branch-and-pr-number.outputs.pr_number }}
      new_version: ${{ steps.check-if-new-version.outputs.new_version }}
    steps:
      - uses: 8BitJonny/gh-get-current-pr@3.0.0
        id: PR

      - name: Check if a new version exists
        id: check-if-new-version
        run: |
          if [ "${{ needs.build-and-release.outputs.changes }}" == "true" ]; then
            VERSION=$(echo -e "${{ needs.build-and-release.outputs.new_version }}" | tr '\n' ' ')
            echo "VERSION=$VERSION" >> $GITHUB_ENV
            echo "new_version=$VERSION" >> $GITHUB_OUTPUT
          else
            echo "No new version found, exiting..."
            exit 1
          fi

      - name: Check if version is a visualizer version
        id: check-if-visualizer
        run: |
          if [[ $VERSION == *"visualizer"* ]]; then
            echo "visualizer=true" >> $GITHUB_OUTPUT
          else
            echo "visualizer=false" >> $GITHUB_OUTPUT
          fi

      - name: Clone web repository
        env:
          GITHUB_TOKEN: ${{ secrets.DEVOPS_CREATE_PR }}
        run: |
          git clone https://x-access-token:${GITHUB_TOKEN}@github.com/wealthcom/wealth-web.git

      - name: Set branch name and PR number
        id: set-branch-and-pr-number
        run: |
          echo "BRANCH_NAME=common-components-${{ steps.PR.outputs.number }}" >> $GITHUB_ENV
          echo "PR_NUMBER=${{ steps.PR.outputs.number  }}" >> $GITHUB_ENV
          echo "pr_number=${{ steps.PR.outputs.number }}" >> $GITHUB_OUTPUT
          echo "PR_TITLE=${{ steps.PR.outputs.pr_title }}" >> $GITHUB_ENV

      - name: Configure git
        run: |
          cd wealth-web
          git config user.name "${{ github.actor }}"
          git config user.email "${{ github.actor_id }}+${{ github.actor }}@users.noreply.github.com"

      - name: install stable version
        env:
          GITHUB_TOKEN: ${{ secrets.DEVOPS_CREATE_PR }}
          FT_TOKEN: ${{ secrets.FT_TOKEN }}
        run: |
          cd wealth-web
          if [ "$(git ls-remote --heads origin $BRANCH_NAME )" != "" ]; then
            git checkout $BRANCH_NAME
          else
            git checkout -b $BRANCH_NAME
          fi 
          yarn add $VERSION
          git add .
          rm .husky/prepare-commit-msg
          git commit --no-verify -m "install stable version"
          git remote set-url origin https://x-access-token:${GITHUB_TOKEN}@github.com/wealthcom/wealth-web.git
          git push origin $BRANCH_NAME && echo "pushed commit to branch" || echo "Pushing failed, please resolve the issue manually" 
          gh pr create --base main --head $BRANCH_NAME --title "$PR_TITLE" --body "new stable common component version installed, wealth-common-components PR [#$PR_NUMBER](https://github.com/wealthcom/wealth-common-components/pull/$PR_NUMBER)
          PR owner: ${{ github.actor }}" && echo "PR created" || echo "PR already exists, exiting..."

  create-visualizer-pdf-pr:
    runs-on: ubuntu-latest
    needs: create-web-pr-with-stable
    if: needs.create-web-pr-with-stable.outputs.visualizer == 'true'
    steps:
      - name: Clone visualizer pdf repository
        env:
          GITHUB_TOKEN: ${{ secrets.DEVOPS_CREATE_PR }}
        run: |
          git clone https://x-access-token:${GITHUB_TOKEN}@github.com/wealthcom/visualizer-pdf-api.git

      - name: Extract version number from package
        run: |
          VISUALIZER_PACKAGE=$(echo -e "${{ needs.create-web-pr-with-stable.outputs.new_version }}" | tr ' ' '\n' | grep 'visualizer')
          VERSION=${VISUALIZER_PACKAGE##*@}
          echo "VISUALIZER_PACKAGE=$VISUALIZER_PACKAGE" >> $GITHUB_ENV
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "BRANCH_NAME=viz-$VERSION" >> $GITHUB_ENV
          echo "PR_NUMBER=${{ needs.create-web-pr-with-stable.outputs.pr_number }}" >> $GITHUB_ENV

      - name: Configure git
        run: |
          cd visualizer-pdf-api
          git config user.name "${{ github.actor }}"
          git config user.email "${{ github.actor_id }}+${{ github.actor }}@users.noreply.github.com"

      - name: Setup Node.js and enable Corepack
        uses: actions/setup-node@v4
        with:
          node-version: '20.8.1'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@wealthcom'

      - name: Enable Corepack and setup Yarn
        run: |
          corepack enable
          corepack prepare yarn@4.9.2 --activate

      - name: install stable version
        env:
          GITHUB_TOKEN: ${{ secrets.DEVOPS_CREATE_PR }}
          FT_TOKEN: ${{ secrets.FT_TOKEN }}
        run: |
          cd visualizer-pdf-api/src/ecs/visualizer-html
          if [ "$(git ls-remote --heads origin $BRANCH_NAME )" != "" ]; then
            git checkout $BRANCH_NAME
          else
            git checkout -b $BRANCH_NAME
          fi 
          yarn add $VISUALIZER_PACKAGE
          git add .
          git commit -m "install new viz version"
          git remote set-url origin https://x-access-token:${GITHUB_TOKEN}@github.com/wealthcom/visualizer-pdf-api.git
          git push origin $BRANCH_NAME && echo "pushed commit to branch" || echo "Pushing failed, please resolve the issue manually" 
          gh pr create --base main --head $BRANCH_NAME --title "$BRANCH_NAME" --body "new stable viz version installed, wealth-common-components PR [#$PR_NUMBER](https://github.com/wealthcom/wealth-common-components/pull/$PR_NUMBER)
          PR owner: ${{ github.actor }}" && echo "PR created" || echo "PR already exists, exiting..."
