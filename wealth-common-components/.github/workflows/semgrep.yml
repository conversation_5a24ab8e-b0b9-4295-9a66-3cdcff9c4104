# Name of this GitHub Actions workflow.
name: Semgrep

on:
  # <PERSON><PERSON> changed files in PRs (diff-aware scanning):
  pull_request: {}
  # Scan mainline branches and report all findings:
  push:
    branches: ['main']

jobs:
  semgrep:
    # User definable name of this GitHub Actions job.
    name: <PERSON>an
    # If you are self-hosting, change the following `runs-on` value:
    runs-on: ubuntu-latest

    container:
      # A Docker image with Semgrep installed. Do not change this.
      image: returntocorp/semgrep

    # Skip any PR created by dependabot to avoid permission issues:
    if: (github.actor != 'dependabot[bot]')

    steps:
      # Fetch project source with GitHub Actions Checkout.
      - uses: actions/checkout@v3
      # Run the "semgrep ci" command on the command line of the docker image.
      - run: semgrep ci
        env:
          # Connect to Semgrep Cloud Platform through your SEMGREP_APP_TOKEN.
          # Generate a token from Semgrep Cloud Platform > Settings
          # and add it to your GitHub secrets.
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
