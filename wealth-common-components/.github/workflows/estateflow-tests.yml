# This workflow will do a clean install of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Estateflow Unit Tests

on:
  pull_request:
    branches: [main]
    paths:
      - 'packages/visualizer/src/lib/util/estateflow/**'
      - .github/workflows/estateflow-tests.yml
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
env:
  GH_PAT: ${{ secrets.DEVOPS_GITHUB_TOKEN }}
  FT_TOKEN: ${{ secrets.FT_TOKEN }}

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/
    name: Estateflow Unit Tests
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - run: yarn install --immutable
      - run: yarn estateflow-test
      - name: Publish Test Results
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          check_name: Estateflow Unit Tests
          files: |
            junit.xml
