export enum ContactCardType {
  Charity = 'Charity',
  Entity = 'Entity',
  Individual = 'Individual',
  Trust = 'Trust',
  Will = 'Will',
}

export enum MaritalStatus {
  CivilUnion = 'CivilUnion',
  DomesticPartnership = 'DomesticPartnership',
  Engaged = 'Engaged',
  Married = 'Married',
  OtherCommittedRelationship = 'OtherCommittedRelationship',
  Single = 'Single',
  Widowed = 'Widowed',
}

export const assets = [
  {
    assetDescription: 'Chase Mortgage - 0038-0038',
    assetId: 'sb5IBYfeRk2PfpKicwLnOw',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '0038',
    assetName: 'Chase Mortgage - 0038',
    assetOwnerName: 'MABLE',
    balanceAsOf: '2023-08-15T20:14:28+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: -487866.84,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: false,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:28+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Liability',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":-487866.84,"balanceAsOf":"2023-08-15T20:14:28Z","displayName":"MABLE"}',
    vendorResponseType: 'InstitutionLoan',
    wealthAssetType: 'Loan',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'Lendin Club personal loan - x7608-7608',
    assetId: 'xN1y3n6bk0u8kGb72r9Tjw',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '7608',
    assetName: 'Lendin Club personal loan - x7608',
    assetOwnerName: 'ELIZABET',
    balanceAsOf: '2023-08-15T20:14:28+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: -23156.71,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: false,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:28+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Liability',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":-23156.71,"balanceAsOf":"2023-08-15T20:14:28Z","displayName":"ELIZABET"}',
    vendorResponseType: 'InstitutionLoan',
    wealthAssetType: 'Loan',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'Joint Checking - 9060-9060',
    assetId: 'SqmLJytdi0OiSJL7Yb6Nkg',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '9060',
    assetName: 'Joint Checking - 9060',
    assetOwnerName: 'LYDIA',
    balanceAsOf: '2023-08-15T20:14:28+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 168561.81,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:28+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Cash',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":168561.81,"balanceAsOf":"2023-08-15T20:14:28Z","displayName":"LYDIA"}',
    vendorResponseType: 'InstitutionBank',
    wealthAssetType: 'Checking',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'Joint Savings - 7159-7159',
    assetId: 'XTfdjS9UvkuPJEoaRO9P9w',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '7159',
    assetName: 'Joint Savings - 7159',
    assetOwnerName: 'LYDIA',
    balanceAsOf: '2023-08-15T20:14:28+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 161801.27,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:28+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Cash',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":161801.27,"balanceAsOf":"2023-08-15T20:14:28Z","displayName":"LYDIA"}',
    vendorResponseType: 'InstitutionBank',
    wealthAssetType: 'Saving',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'ROTH IRA - 4619-4619',
    assetId: 'dUevb9PioE6KaACFPsg8Ww',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '4619',
    assetName: 'ROTH IRA - 4619',
    assetOwnerName: 'AMY GLADYS',
    balanceAsOf: '2023-08-15T20:14:28+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 697978.56,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:28+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Retirement',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":697978.56,"balanceAsOf":"2023-08-15T20:14:28Z","displayName":"AMY GLADYS"}',
    vendorResponseType: 'InstitutionInvestment',
    wealthAssetType: 'IRA',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'Citi Automobile Loan - x1563-1563',
    assetId: 'MhtG9QPrekeJS8mv8kqKMg',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '1563',
    assetName: 'Citi Automobile Loan - x1563',
    assetOwnerName: 'JEAN',
    balanceAsOf: '2023-08-15T20:14:28+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: -21847.55,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: false,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:28+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Liability',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":-21847.55,"balanceAsOf":"2023-08-15T20:14:28Z","displayName":"JEAN"}',
    vendorResponseType: 'InstitutionLoan',
    wealthAssetType: 'Loan',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'CMA -Joint Brokerage - 3547-3547',
    assetId: 'VjuEr61gGECMZ3Fc2cBxLA',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '3547',
    assetName: 'CMA -Joint Brokerage - 3547',
    assetOwnerName: 'MARY',
    balanceAsOf: '2023-08-15T20:14:30+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 330101.25,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:30+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Cash',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":330101.25,"balanceAsOf":"2023-08-15T20:14:30Z","displayName":"MARY"}',
    vendorResponseType: 'InstitutionInvestment',
    wealthAssetType: 'Checking',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'My CD - 8878-8878',
    assetId: '2Ozm6Fmj30Wyubf1wiFnqg',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '8878',
    assetName: 'My CD - 8878',
    assetOwnerName: 'LORETTA',
    balanceAsOf: '2023-08-15T20:14:28+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 49778.07,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:28+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Cash',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":49778.07,"balanceAsOf":"2023-08-15T20:14:28Z","displayName":"LORETTA"}',
    vendorResponseType: 'InstitutionBank',
    wealthAssetType: 'Saving',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'College Loan - x8946-8946',
    assetId: 'buwmmCqITEGxegX4tRuDOg',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '8946',
    assetName: 'College Loan - x8946',
    assetOwnerName: 'HATTIE',
    balanceAsOf: '2023-08-15T20:14:28+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: -120180.5,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: false,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:28+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Liability',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":-120180.5,"balanceAsOf":"2023-08-15T20:14:28Z","displayName":"HATTIE"}',
    vendorResponseType: 'InstitutionLoan',
    wealthAssetType: 'Loan',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'CashRewardOld - 0784-0784',
    assetId: 'AxZQ0voZ7ECAEX18QvjD0w',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '0784',
    assetName: 'CashRewardOld - 0784',
    assetOwnerName: null,
    balanceAsOf: '2023-08-15T20:14:29+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: -9793.63,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: false,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:29+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Liability',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":-9793.63,"balanceAsOf":"2023-08-15T20:14:29Z"}',
    vendorResponseType: 'InstitutionCredit',
    wealthAssetType: 'CreditCard',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
  {
    assetDescription: 'Retirement Savings Plan - 4258-4258',
    assetId: 'TBPbBBLDXUmCz18eimgMVQ',
    assetInfo: null,
    assetInfoType: 'Other',
    assetMask: '4258',
    assetName: 'Retirement Savings Plan - 4258',
    assetOwnerName: 'KAY MARSHA',
    balanceAsOf: '2023-08-15T20:14:28+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 94582.58,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
    creationDate: '2023-08-15T20:14:33+00:00',
    currencyCode: 'USD',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: ********,
    institutionName: 'Dag Site',
    integration: null,
    integrationAccountId: '********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2023-08-15T20:14:28+00:00',
    lastUpdateAttempt: '2023-08-15T20:14:33+00:00',
    logoName: 'institution/favicon/********.svg',
    modificationDate: '2023-08-15T20:14:33+00:00',
    nextUpdate: null,
    nickname: null,
    ownership: null,
    primaryAssetCategory: 'Retirement',
    status: 'ACCT_SUMMARY_RECEIVED',
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'dn8GFD3y2km6KTkUhFzsdA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"balance":94582.58,"balanceAsOf":"2023-08-15T20:14:28Z","displayName":"KAY MARSHA"}',
    vendorResponseType: 'InstitutionInvestment',
    wealthAssetType: 'OtherRetirement',
    wid: '619ad876-8976-4ccf-98a3-b6061f2b95c4',
  },
];

export const pcc = {
  id: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
  wid: 'c808cbdc-2262-4c14-bc5b-930a9934a3be',
  addressLine1: '437 Madison Avenue',
  addressLine2: '',
  addressCity: 'New York',
  addressState: 'New York',
  addressStateShort: 'NY',
  addressZipCode: '10022',
  addressFormatted: '437 Madison Avenue, New York, New York, 10022',
  dob: '1999-01-01T00:00:00.000Z',
  firstName: 'a',
  lastName: 'b',
  middleName: '',
  nickname: '',
  email: '<EMAIL>',
  phoneNumber: '',
  hasChildren: false,
  childrenIds: [],
  hasFamilyMemberWithSpecialNeeds: false,
  hasPets: false,
  maritalStatus: MaritalStatus.Single,
  partnerId: null,
  citizenship: null,
  aliases: [],
  notes: '',
  isPrimary: true,
  geodata:
    '{"types": ["premise"], "geometry": {"bounds": {"east": -73.9749626, "west": -73.9757843, "north": 40.7576571, "south": 40.757056}, "location": {"lat": 40.7573533, "lng": -73.97524349999999}, "viewport": {"east": -73.9740296697085, "west": -73.97672763029152, "north": 40.7587055302915, "south": 40.7560075697085}, "location_type": "ROOFTOP"}, "place_id": "ChIJr8O4RvxYwokR06DPVPxymuc", "formatted_address": "437 Madison Ave, New York, NY 10022, USA", "address_components": [{"types": ["street_number"], "long_name": "437", "short_name": "437"}, {"types": ["route"], "long_name": "Madison Avenue", "short_name": "Madison Ave"}, {"types": ["political", "sublocality", "sublocality_level_1"], "long_name": "Manhattan", "short_name": "Manhattan"}, {"types": ["locality", "political"], "long_name": "New York", "short_name": "New York"}, {"types": ["administrative_area_level_2", "political"], "long_name": "New York County", "short_name": "New York County"}, {"types": ["administrative_area_level_1", "political"], "long_name": "New York", "short_name": "NY"}, {"types": ["country", "political"], "long_name": "United States", "short_name": "US"}, {"types": ["postal_code"], "long_name": "10022", "short_name": "10022"}, {"types": ["postal_code_suffix"], "long_name": "7001", "short_name": "7001"}]}',
  isLegalNameConfirmed: true,
  __typename: 'ContactCard' as const,
};

export const getNetWorthPredictionDataLiability = {
  data: {
    getNetWorthPrediction: {
      error: null,
      payload: [
        {
          estateTax: 0.0,
          estateTaxBasedOnInputRates: 0.0,
          netWorth: 724547.67,
          netWorthBasedOnInputRates: 681435.94,
          year: 2025,
          __typename: 'NetWorth',
        },
        {
          estateTax: 0.0,
          estateTaxBasedOnInputRates: 0.0,
          netWorth: 1540924.19,
          netWorthBasedOnInputRates: 1325298.04,
          year: 2030,
          __typename: 'NetWorth',
        },
        {
          estateTax: 0.0,
          estateTaxBasedOnInputRates: 0.0,
          netWorth: 2543382.67,
          netWorthBasedOnInputRates: 2074977.41,
          year: 2035,
          __typename: 'NetWorth',
        },
        {
          estateTax: 0.0,
          estateTaxBasedOnInputRates: 0.0,
          netWorth: 3731923.1,
          netWorthBasedOnInputRates: 2947864.87,
          year: 2040,
          __typename: 'NetWorth',
        },
        {
          estateTax: 0.0,
          estateTaxBasedOnInputRates: 0.0,
          netWorth: 5106545.47,
          netWorthBasedOnInputRates: 3964209.4,
          year: 2045,
          __typename: 'NetWorth',
        },
        {
          estateTax: 0.0,
          estateTaxBasedOnInputRates: 0.0,
          netWorth: 6667249.79,
          netWorthBasedOnInputRates: 5147587.84,
          year: 2050,
          __typename: 'NetWorth',
        },
        {
          estateTax: 0.0,
          estateTaxBasedOnInputRates: 0.0,
          netWorth: 1.034690428e7,
          netWorthBasedOnInputRates: 8129764.64,
          year: 2060,
          __typename: 'NetWorth',
        },
        {
          estateTax: 0.0,
          estateTaxBasedOnInputRates: 0.0,
          netWorth: 1.477088656e7,
          netWorthBasedOnInputRates: 1.217271848e7,
          year: 2070,
          __typename: 'NetWorth',
        },
        {
          estateTax: 0.0,
          estateTaxBasedOnInputRates: 0.0,
          netWorth: 1.726200062e7,
          netWorthBasedOnInputRates: 1.470514669e7,
          year: 2075,
          __typename: 'NetWorth',
        },
      ],
      rates: {
        investment: 3.51,
        investmentWeight: 0.0,
        other: 3.09,
        otherWeight: 1.0,
        realEstate: 3.01,
        realEstateWeight: 0.0,
        retirement: 3.45,
        retirementWeight: 0.0,
        total: 3.09,
        __typename: 'CalculatedRates',
      },
      success: true,
      __typename: 'NetWorthPredictionResponse',
    },
  },
};

export const getNetWorthPredictionData = {
  data: {
    getNetWorthPrediction: {
      error: null,
      payload: [
        {
          estateTax: 4526777.75,
          estateTaxBasedOnInputRates: 4830686.49,
          netWorth: 2.437244437e7,
          netWorthBasedOnInputRates: 2.513221621e7,
          year: 2025,
          __typename: 'NetWorth',
        },
        {
          estateTax: 8146626.87,
          estateTaxBasedOnInputRates: 1.131556854e7,
          netWorth: 3.419839124e7,
          netWorthBasedOnInputRates: 4.212074543e7,
          year: 2030,
          __typename: 'NetWorth',
        },
        {
          estateTax: 1.171379448e7,
          estateTaxBasedOnInputRates: 2.23412544e7,
          netWorth: 4.402433811e7,
          netWorthBasedOnInputRates: 7.059298789e7,
          year: 2035,
          __typename: 'NetWorth',
        },
        {
          estateTax: 1.52809621e7,
          estateTaxBasedOnInputRates: 4.106548026e7,
          netWorth: 5.385028498e7,
          netWorthBasedOnInputRates: 1.1831158037e8,
          year: 2040,
          __typename: 'NetWorth',
        },
        {
          estateTax: 1.884812972e7,
          estateTaxBasedOnInputRates: 7.26922227e7,
          netWorth: 6.367623185e7,
          netWorthBasedOnInputRates: 1.982864643e8,
          year: 2045,
          __typename: 'NetWorth',
        },
        {
          estateTax: 2.241529733e7,
          estateTaxBasedOnInputRates: 1.2594318708e8,
          netWorth: 7.350217872e7,
          netWorthBasedOnInputRates: 3.3232190309e8,
          year: 2050,
          __typename: 'NetWorth',
        },
        {
          estateTax: 2.954963257e7,
          estateTaxBasedOnInputRates: 3.6566788137e8,
          netWorth: 9.315407246e7,
          netWorthBasedOnInputRates: 9.3344969446e8,
          year: 2060,
          __typename: 'NetWorth',
        },
        {
          estateTax: 3.66839678e7,
          estateTaxBasedOnInputRates: 1.0403381504e9,
          netWorth: 1.128059662e8,
          netWorthBasedOnInputRates: 2.62194142269e9,
          year: 2070,
          __typename: 'NetWorth',
        },
        {
          estateTax: 4.239143599e7,
          estateTaxBasedOnInputRates: 2.38710103331e9,
          netWorth: 1.2852748119e8,
          netWorthBasedOnInputRates: 5.99030147448e9,
          year: 2078,
          __typename: 'NetWorth',
        },
      ],
      rates: {
        investment: 3.4,
        investmentWeight: 0.99999,
        other: 3.0,
        otherWeight: 1.0e-5,
        realEstate: 2.93,
        realEstateWeight: 0.0,
        retirement: 3.34,
        retirementWeight: 0.0,
        total: 3.4,
        __typename: 'CalculatedRates',
      },
      success: true,
      __typename: 'NetWorthPredictionResponse',
    },
  },
};

export const projectionClients = [
  {
    contactCard: {
      addressCity: 'Kermit',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Texas',
      addressStateShort: 'TX',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: '1998-11-02T00:00:00.000Z',
      ein: null,
      email: '<EMAIL>',
      firstName: 'Paul',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-103.0948974,"west":-103.0951519,"north":31.8571962,"south":31.8568156},"location":{"lat":31.8570215,"lng":-103.0950057},"viewport":{"east":-103.0936756697085,"west":-103.0963736302915,"north":31.8584002302915,"south":31.8557022697085},"location_type":"ROOFTOP"},"place_id":"ChIJp9J2dFgX-4YRk7fAbwKSNYg","formatted_address":"123 W Austin St, Kermit, TX 79745, USA","address_components":[{"types":["street_number"],"long_name":"123","short_name":"123"},{"types":["route"],"long_name":"West Austin Street","short_name":"W Austin St"},{"types":["locality","political"],"long_name":"Kermit","short_name":"Kermit"},{"types":["administrative_area_level_2","political"],"long_name":"Winkler County","short_name":"Winkler County"},{"types":["administrative_area_level_1","political"],"long_name":"Texas","short_name":"TX"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"79745","short_name":"79745"},{"types":["postal_code_suffix"],"long_name":"2803","short_name":"2803"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'b5da78ab-db83-490a-82c6-b29a4ee5716e',
      wid: 'b5da78ab-db83-490a-82c6-b29a4ee5716e',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Beckham',
      legalName: null,
      maritalStatus: MaritalStatus.Engaged,
      middleName: '',
      nickname: '',
      notes: '',
      partner: {
        firstName: 'a',
        id: '5d8c7f72-4d4e-426a-a8ab-64557612d0cf',
        lastName: 'a',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '+11233211234',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '9a943c7b-4e12-4108-a6d7-c0964ec1e199',
  },
  {
    contactCard: {
      addressCity: 'Seattle',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Washington',
      addressStateShort: 'WA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: '1999-01-01T00:00:00.000Z',
      ein: null,
      email: '<EMAIL>',
      firstName: 'Peter2',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":47.6023418,"lng":-122.3185052},"viewport":{"east":-122.3171562197085,"west":-122.3198541802915,"north":47.6036907802915,"south":47.6009928197085},"location_type":"RANGE_INTERPOLATED"},"place_id":"EiUxMzAgQm9yZW4gQXZlLCBTZWF0dGxlLCBXQSA5ODEyMiwgVVNBIjESLwoUChIJOSSctLhqkFQR2ejAZIPAplcQggEqFAoSCR8X2__JapBUEd7i27ww6Mmz","formatted_address":"130 Boren Ave, Seattle, WA 98122, USA","address_components":[{"types":["street_number"],"long_name":"130","short_name":"130"},{"types":["route"],"long_name":"Boren Avenue","short_name":"Boren Ave"},{"types":["neighborhood","political"],"long_name":"Yesler Terrace","short_name":"Yesler Terrace"},{"types":["locality","political"],"long_name":"Seattle","short_name":"Seattle"},{"types":["administrative_area_level_2","political"],"long_name":"King County","short_name":"King County"},{"types":["administrative_area_level_1","political"],"long_name":"Washington","short_name":"WA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"98122","short_name":"98122"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '6f580fc2-45c6-4f16-9ba9-9e5da3ea61bf',
      wid: '6f580fc2-45c6-4f16-9ba9-9e5da3ea61bf',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Kim2',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: '',
      nickname: '',
      notes: '',
      partner: {
        firstName: 'sadf',
        id: '2acb12b1-f5ee-431e-9721-c30800d3da8c',
        lastName: 'aadsfasfasfsf',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '6c9817fd-7577-4197-8232-c3020aa992e1',
  },
  {
    contactCard: {
      addressCity: 'Lynnwood',
      addressCountry: null,
      addressCountryShort: null,
      addressCounty: null,
      addressFormatted: '21321 Locust Way12, Lynnwood, Washington, 98036',
      addressState: 'Washington',
      addressStateShort: 'WA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'P',
      formOfIncorporation: null,
      geodata: '{}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '4e699d3f-7abf-4ab4-895f-14bad68c17a4',
      wid: '4e699d3f-7abf-4ab4-895f-14bad68c17a4',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'K2',
      legalName: null,
      maritalStatus: null,
      middleName: '',
      nickname: '',
      notes: '',
      partner: null,
      phoneNumber: '',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'de9a94a0-6896-4c01-abd7-8ed2c311b8f9',
  },
  {
    contactCard: {
      addressCity: 'Seattle',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Washington',
      addressStateShort: 'WA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Peter',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":47.6021524,"lng":-122.3210392},"viewport":{"east":-122.3195579697085,"west":-122.3222559302915,"north":47.6035049802915,"south":47.6008070197085},"location_type":"ROOFTOP"},"place_id":"ChIJ3Sx4ef5rkFQRCAxT4GnaDX8","plus_code":{"global_code":"84VVJM2H+VH","compound_code":"JM2H+VH Seattle, WA"},"formatted_address":"123 Broadway, Seattle, WA 98122, USA","address_components":[{"types":["street_number"],"long_name":"123","short_name":"123"},{"types":["route"],"long_name":"Broadway","short_name":"Broadway"},{"types":["neighborhood","political"],"long_name":"Yesler Terrace","short_name":"Yesler Terrace"},{"types":["locality","political"],"long_name":"Seattle","short_name":"Seattle"},{"types":["administrative_area_level_2","political"],"long_name":"King County","short_name":"King County"},{"types":["administrative_area_level_1","political"],"long_name":"Washington","short_name":"WA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"98122","short_name":"98122"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'da55da6e-5c51-4083-9fcf-4bd24996ee2f',
      wid: 'da55da6e-5c51-4083-9fcf-4bd24996ee2f',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Kim',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '3498d806-10e6-4c1b-9c6b-ee84122e9d71',
  },
  {
    contactCard: {
      addressCity: 'Seattle',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Washington',
      addressStateShort: 'WA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'James',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":47.6162214,"lng":-122.3397841},"viewport":{"east":-122.3383817697085,"west":-122.3410797302915,"north":47.6176444302915,"south":47.6149464697085},"location_type":"ROOFTOP"},"place_id":"ChIJlU410EsVkFQR5S8R77FnTcM","plus_code":{"global_code":"84VVJM86+F3","compound_code":"JM86+F3 Seattle, WA"},"formatted_address":"2131 7th Ave, Seattle, WA 98121, USA","address_components":[{"types":["street_number"],"long_name":"2131","short_name":"2131"},{"types":["route"],"long_name":"7th Avenue","short_name":"7th Ave"},{"types":["neighborhood","political"],"long_name":"Downtown Seattle","short_name":"Downtown Seattle"},{"types":["locality","political"],"long_name":"Seattle","short_name":"Seattle"},{"types":["administrative_area_level_2","political"],"long_name":"King County","short_name":"King County"},{"types":["administrative_area_level_1","political"],"long_name":"Washington","short_name":"WA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"98121","short_name":"98121"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '89c699fb-c5eb-4e28-98aa-88cf45e9ccbf',
      wid: '89c699fb-c5eb-4e28-98aa-88cf45e9ccbf',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bond',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Tracy',
        id: 'f0aadc7f-9041-4091-9fdc-64a06c9365ed',
        lastName: 'Bond',
        middleName: '',
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '2f323842-4d2a-4283-bdb2-b3bdada58320',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-74.0033499,"west":-74.0048814,"north":40.71596530000001,"south":40.714584},"location":{"lat":40.7153737,"lng":-74.0042596},"viewport":{"east":-74.0026382197085,"west":-74.00533618029151,"north":40.7166236302915,"south":40.7139256697085},"location_type":"ROOFTOP"},"place_id":"ChIJ3QXdsCFawokRZ25DjHdd2qM","formatted_address":"26 Federal Plaza, New York, NY 10278, USA","address_components":[{"types":["street_number"],"long_name":"26","short_name":"26"},{"types":["route"],"long_name":"Federal Plaza","short_name":"Federal Plaza"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10278","short_name":"10278"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'c91eded1-ae5a-48f0-86ce-84bcc68e334e',
      wid: 'c91eded1-ae5a-48f0-86ce-84bcc68e334e',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'b',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '39846f1c-777b-4b33-a909-27152f60453a',
  },
  {
    contactCard: {
      addressCity: 'Yucca Valley',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Alex',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-116.4527766,"west":-116.4530688,"north":34.0997608,"south":34.0995347},"location":{"lat":34.0996676,"lng":-116.4529292},"viewport":{"east":-116.*************,"west":-116.*************,"north":34.1010394302915,"south":34.0983414697085},"location_type":"ROOFTOP"},"place_id":"ChIJSQl-RLon24ARcKtuoXgnqRQ","formatted_address":"55543 El Dorado Dr, Yucca Valley, CA 92284, USA","address_components":[{"types":["street_number"],"long_name":"55543","short_name":"55543"},{"types":["route"],"long_name":"El Dorado Drive","short_name":"El Dorado Dr"},{"types":["locality","political"],"long_name":"Yucca Valley","short_name":"Yucca Valley"},{"types":["administrative_area_level_2","political"],"long_name":"San Bernardino County","short_name":"San Bernardino County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"92284","short_name":"92284"},{"types":["postal_code_suffix"],"long_name":"3593","short_name":"3593"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '79827ee9-4c84-470e-affa-08bd4b6c477d',
      wid: '79827ee9-4c84-470e-affa-08bd4b6c477d',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bortnik',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '52caf397-fa1b-4a78-a85d-e930ebbd6790',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Ari',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":40.712002,"lng":-74.01511889999999},"viewport":{"east":-74.01396781970848,"west":-74.01666578029149,"north":40.7130782302915,"south":40.7103802697085},"location_type":"ROOFTOP"},"place_id":"ChIJ_yksWBpawokRnBA5ggBavyo","plus_code":{"global_code":"87G7PX6M+RX","compound_code":"PX6M+RX New York, NY"},"formatted_address":"225 Liberty St, New York, NY 10281, USA","address_components":[{"types":["street_number"],"long_name":"225","short_name":"225"},{"types":["route"],"long_name":"Liberty Street","short_name":"Liberty St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10281","short_name":"10281"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '3138f6bc-5e85-4e41-9d74-396083da9deb',
      wid: '3138f6bc-5e85-4e41-9d74-396083da9deb',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'TestingNov1',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'fe490268-9bda-4743-99d1-6615b4bd01f7',
  },
  {
    contactCard: {
      addressCity: 'Seattle',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Washington',
      addressStateShort: 'WA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'P',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":47.6021524,"lng":-122.3210392},"viewport":{"east":-122.3195579697085,"west":-122.3222559302915,"north":47.6035049802915,"south":47.6008070197085},"location_type":"ROOFTOP"},"place_id":"ChIJ3Sx4ef5rkFQRCAxT4GnaDX8","plus_code":{"global_code":"84VVJM2H+VH","compound_code":"JM2H+VH Seattle, WA"},"formatted_address":"123 Broadway, Seattle, WA 98122, USA","address_components":[{"types":["street_number"],"long_name":"123","short_name":"123"},{"types":["route"],"long_name":"Broadway","short_name":"Broadway"},{"types":["neighborhood","political"],"long_name":"Yesler Terrace","short_name":"Yesler Terrace"},{"types":["locality","political"],"long_name":"Seattle","short_name":"Seattle"},{"types":["administrative_area_level_2","political"],"long_name":"King County","short_name":"King County"},{"types":["administrative_area_level_1","political"],"long_name":"Washington","short_name":"WA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"98122","short_name":"98122"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: true,
      id: 'b4683c93-38af-43a7-8288-2f87e6a2a082',
      wid: 'b4683c93-38af-43a7-8288-2f87e6a2a082',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'K',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Foo',
        id: '9f5fd7b8-135b-4d03-864f-4eda29838374',
        lastName: 'bar',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '8506bc42-d85c-415b-bf80-2f17a3b05906',
  },
  {
    contactCard: {
      addressCity: 'Seattle',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Washington',
      addressStateShort: 'WA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Foo123',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":47.6162214,"lng":-122.3397841},"viewport":{"east":-122.3383817697085,"west":-122.3410797302915,"north":47.6176444302915,"south":47.6149464697085},"location_type":"ROOFTOP"},"place_id":"ChIJlU410EsVkFQR5S8R77FnTcM","plus_code":{"global_code":"84VVJM86+F3","compound_code":"JM86+F3 Seattle, WA"},"formatted_address":"2131 7th Ave, Seattle, WA 98121, USA","address_components":[{"types":["street_number"],"long_name":"2131","short_name":"2131"},{"types":["route"],"long_name":"7th Avenue","short_name":"7th Ave"},{"types":["neighborhood","political"],"long_name":"Downtown Seattle","short_name":"Downtown Seattle"},{"types":["locality","political"],"long_name":"Seattle","short_name":"Seattle"},{"types":["administrative_area_level_2","political"],"long_name":"King County","short_name":"King County"},{"types":["administrative_area_level_1","political"],"long_name":"Washington","short_name":"WA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"98121","short_name":"98121"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '9fd07369-7f75-4a0d-9612-a29c9fdf8c3c',
      wid: '9fd07369-7f75-4a0d-9612-a29c9fdf8c3c',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bar',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '973dc410-1793-48aa-bb82-bf7a6ce36829',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-74.0033499,"west":-74.0048814,"north":40.71596530000001,"south":40.714584},"location":{"lat":40.7153737,"lng":-74.0042596},"viewport":{"east":-74.0026382197085,"west":-74.00533618029151,"north":40.7166236302915,"south":40.7139256697085},"location_type":"ROOFTOP"},"place_id":"ChIJ3QXdsCFawokRZ25DjHdd2qM","formatted_address":"26 Federal Plaza, New York, NY 10278, USA","address_components":[{"types":["street_number"],"long_name":"26","short_name":"26"},{"types":["route"],"long_name":"Federal Plaza","short_name":"Federal Plaza"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10278","short_name":"10278"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '0f80e466-7b84-47ee-b5e6-a8f88a3e4d68',
      wid: '0f80e466-7b84-47ee-b5e6-a8f88a3e4d68',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'b',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '570dc8c0-c7b1-4436-ae97-6dca6b3bfe9f',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":40.712002,"lng":-74.01511889999999},"viewport":{"east":-74.01396781970848,"west":-74.01666578029149,"north":40.7130782302915,"south":40.7103802697085},"location_type":"ROOFTOP"},"place_id":"ChIJ_yksWBpawokRnBA5ggBavyo","plus_code":{"global_code":"87G7PX6M+RX","compound_code":"PX6M+RX New York, NY"},"formatted_address":"225 Liberty St, New York, NY 10281, USA","address_components":[{"types":["street_number"],"long_name":"225","short_name":"225"},{"types":["route"],"long_name":"Liberty Street","short_name":"Liberty St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10281","short_name":"10281"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'da0891e5-5953-45b2-bed9-5005d83508f8',
      wid: 'da0891e5-5953-45b2-bed9-5005d83508f8',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'b',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'cc097399-1619-489a-a314-f312b511a513',
  },
  {
    contactCard: {
      addressCity: 'Chicago',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Illinois',
      addressStateShort: 'IL',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'alex',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":41.9783512,"lng":-87.84326539999999},"viewport":{"east":-87.84191766970851,"west":-87.84461563029151,"north":41.9797674802915,"south":41.9770695197085},"location_type":"ROOFTOP"},"place_id":"ChIJr1ldY67LD4gRcb_JYj_LK7o","plus_code":{"global_code":"86HJX5H4+8M","compound_code":"X5H4+8M Chicago, IL"},"formatted_address":"5441 N East River Rd, Chicago, IL 60656, USA","address_components":[{"types":["street_number"],"long_name":"5441","short_name":"5441"},{"types":["route"],"long_name":"North East River Road","short_name":"N East River Rd"},{"types":["neighborhood","political"],"long_name":"O\'Hare","short_name":"O\'Hare"},{"types":["locality","political"],"long_name":"Chicago","short_name":"Chicago"},{"types":["administrative_area_level_2","political"],"long_name":"Cook County","short_name":"Cook County"},{"types":["administrative_area_level_1","political"],"long_name":"Illinois","short_name":"IL"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"60656","short_name":"60656"},{"types":["postal_code_suffix"],"long_name":"1199","short_name":"1199"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '1cb4923a-afc9-4af9-8030-b33f9f6fb986',
      wid: '1cb4923a-afc9-4af9-8030-b33f9f6fb986',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'bortnik-cl455',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '3cfe8eff-e596-4879-9981-ca880bca9651',
  },
  {
    contactCard: {
      addressCity: 'Akron',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Ohio',
      addressStateShort: 'OH',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'alex',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":41.168501,"lng":-81.65973500000001},"viewport":{"east":-81.6583870697085,"west":-81.66108503029152,"north":41.1700813802915,"south":41.1673834197085},"location_type":"ROOFTOP"},"place_id":"ChIJqde-ku_aMIgRZi9ajqHTnj4","plus_code":{"global_code":"86HW589R+C4","compound_code":"589R+C4 Akron, OH"},"formatted_address":"4480 W Bath Rd, Akron, OH 44333, USA","address_components":[{"types":["street_number"],"long_name":"4480","short_name":"4480"},{"types":["route"],"long_name":"West Bath Road","short_name":"W Bath Rd"},{"types":["locality","political"],"long_name":"Akron","short_name":"Akron"},{"types":["administrative_area_level_3","political"],"long_name":"Bath Township","short_name":"Bath Township"},{"types":["administrative_area_level_2","political"],"long_name":"Summit County","short_name":"Summit County"},{"types":["administrative_area_level_1","political"],"long_name":"Ohio","short_name":"OH"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"44333","short_name":"44333"},{"types":["postal_code_suffix"],"long_name":"1116","short_name":"1116"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '747e61db-d631-4e73-97e4-c08fc3bca76d',
      wid: '747e61db-d631-4e73-97e4-c08fc3bca76d',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'bortnik+cl767',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'd6804583-4cfc-4f7c-ac20-ea3422360659',
  },
  {
    contactCard: {
      addressCity: 'Boston',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Massachusetts',
      addressStateShort: 'MA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Clyde',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":42.3398168,"lng":-71.1044651},"viewport":{"east":-71.1036227697085,"west":-71.1063207302915,"north":42.34122448029149,"south":42.3385265197085},"location_type":"ROOFTOP"},"place_id":"ChIJ7ZON7Ix544kRr90JdPG6Fn0","plus_code":{"global_code":"87JC8VQW+W6","compound_code":"8VQW+W6 Boston, MA"},"formatted_address":"330 Brookline Ave, Boston, MA 02215, USA","address_components":[{"types":["street_number"],"long_name":"330","short_name":"330"},{"types":["route"],"long_name":"Brookline Avenue","short_name":"Brookline Ave"},{"types":["neighborhood","political"],"long_name":"Longwood Medical and Academic Area","short_name":"LMA"},{"types":["locality","political"],"long_name":"Boston","short_name":"Boston"},{"types":["administrative_area_level_2","political"],"long_name":"Suffolk County","short_name":"Suffolk County"},{"types":["administrative_area_level_1","political"],"long_name":"Massachusetts","short_name":"MA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"02215","short_name":"02215"},{"types":["postal_code_suffix"],"long_name":"5400","short_name":"5400"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'e8ad5aa9-8d3b-4813-8909-f9039fa7bfa2',
      wid: 'e8ad5aa9-8d3b-4813-8909-f9039fa7bfa2',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Barrow',
      legalName: null,
      maritalStatus: MaritalStatus.DomesticPartnership,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Bonnie',
        id: 'e8089751-1f3c-4b5a-b826-ceded7c6a231',
        lastName: 'Parker',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '+13232434343',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'ac6a822a-841d-4803-be3a-024c5b69027d',
  },
  {
    contactCard: {
      addressCity: 'Seattle',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Washington',
      addressStateShort: 'WA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-122.3457237,"west":-122.3461409,"north":47.6308813,"south":47.6306605},"location":{"lat":47.6307426,"lng":-122.3458998},"viewport":{"east":-122.3446455697085,"west":-122.3473435302915,"north":47.6321198802915,"south":47.6294219197085},"location_type":"ROOFTOP"},"place_id":"ChIJwUhBPT4VkFQRe23kbyTxvJM","formatted_address":"1234 Taylor Ave N, Seattle, WA 98109, USA","address_components":[{"types":["street_number"],"long_name":"1234","short_name":"1234"},{"types":["route"],"long_name":"Taylor Avenue North","short_name":"Taylor Ave N"},{"types":["neighborhood","political"],"long_name":"East Queen Anne","short_name":"East Queen Anne"},{"types":["locality","political"],"long_name":"Seattle","short_name":"Seattle"},{"types":["administrative_area_level_2","political"],"long_name":"King County","short_name":"King County"},{"types":["administrative_area_level_1","political"],"long_name":"Washington","short_name":"WA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"98109","short_name":"98109"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: true,
      id: '3a1d8a3d-dca0-425f-b48d-2f323cf9aa8a',
      wid: '3a1d8a3d-dca0-425f-b48d-2f323cf9aa8a',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'b',
      legalName: null,
      maritalStatus: MaritalStatus.CivilUnion,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'a',
        id: '86e2a261-8d55-48d3-81e5-7aa14fc1f82f',
        lastName: 'a',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'd040a424-de7b-4833-af28-416cc16bd038',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":40.712002,"lng":-74.01511889999999},"viewport":{"east":-74.01396781970848,"west":-74.01666578029149,"north":40.7130782302915,"south":40.7103802697085},"location_type":"ROOFTOP"},"place_id":"ChIJ_yksWBpawokRnBA5ggBavyo","plus_code":{"global_code":"87G7PX6M+RX","compound_code":"PX6M+RX New York, NY, USA"},"formatted_address":"225 Liberty St, New York, NY 10281, USA","address_components":[{"types":["street_number"],"long_name":"225","short_name":"225"},{"types":["route"],"long_name":"Liberty Street","short_name":"Liberty St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10281","short_name":"10281"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'fe43881e-ee55-4707-81f5-3159e5405d27',
      wid: 'fe43881e-ee55-4707-81f5-3159e5405d27',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'v',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'd540f226-31c3-4bbd-890e-036698fee39a',
  },
  {
    contactCard: {
      addressCity: 'Grand Canyon Village',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Kate',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":35.970376,"lng":-112.1277504},"viewport":{"east":-112.1264014197085,"west":-112.1290993802915,"north":35.9717249802915,"south":35.9690270197085},"location_type":"RANGE_INTERPOLATED"},"place_id":"Ei0xMjMgQXJpem9uYSA2NCwgR3JhbmQgQ2FueW9uIFZpbGxhZ2UsIEFaLCBVU0EiMBIuChQKEglFly_j7hszhxFvv7KITVK6MhB7KhQKEgmfKjzP5F4yhxHAr9UPOKpqvg","formatted_address":"123 AZ-64, Grand Canyon Village, AZ 86023, USA","address_components":[{"types":["street_number"],"long_name":"123","short_name":"123"},{"types":["route"],"long_name":"Arizona 64","short_name":"AZ-64"},{"types":["locality","political"],"long_name":"Grand Canyon Village","short_name":"Grand Canyon Village"},{"types":["administrative_area_level_2","political"],"long_name":"Coconino County","short_name":"Coconino County"},{"types":["administrative_area_level_1","political"],"long_name":"Arizona","short_name":"AZ"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"86023","short_name":"86023"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'e3b1b5fe-3803-4dcc-bc8b-570d8dba75d1',
      wid: 'e3b1b5fe-3803-4dcc-bc8b-570d8dba75d1',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Purchel',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '1c4ca0f8-2163-494f-b1cf-7567360d8e10',
  },
  {
    contactCard: {
      addressCity: 'Hackensack',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New Jersey',
      addressStateShort: 'NJ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-74.0563682,"west":-74.0579186,"north":40.8841147,"south":40.8826786},"location":{"lat":40.883251,"lng":-74.0570006},"viewport":{"east":-74.05579441970849,"west":-74.0584923802915,"north":40.8847456302915,"south":40.8820476697085},"location_type":"ROOFTOP"},"place_id":"ChIJay3ngYr5wokRNBY4BUevU_E","formatted_address":"20 Prospect Ave, Hackensack, NJ 07601, USA","address_components":[{"types":["street_number"],"long_name":"20","short_name":"20"},{"types":["route"],"long_name":"Prospect Avenue","short_name":"Prospect Ave"},{"types":["locality","political"],"long_name":"Hackensack","short_name":"Hackensack"},{"types":["administrative_area_level_2","political"],"long_name":"Bergen County","short_name":"Bergen County"},{"types":["administrative_area_level_1","political"],"long_name":"New Jersey","short_name":"NJ"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"07601","short_name":"07601"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'b1872c84-bcda-44ca-88f4-7cdbad05e9f7',
      wid: 'b1872c84-bcda-44ca-88f4-7cdbad05e9f7',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'b',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '180a5c5d-81e6-482f-bf2e-51fc3d6aa186',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":40.7562542,"lng":-73.9908734},"viewport":{"east":-73.9893828197085,"west":-73.9920807802915,"north":40.7575463302915,"south":40.7548483697085},"location_type":"ROOFTOP"},"place_id":"ChIJk9ueRVNYwokRG9UDzC94mhs","plus_code":{"global_code":"87G8Q245+GM","compound_code":"Q245+GM New York, NY, USA"},"formatted_address":"625 8th Ave, New York, NY 10018, USA","address_components":[{"types":["street_number"],"long_name":"625","short_name":"625"},{"types":["route"],"long_name":"8th Avenue","short_name":"8th Ave"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10018","short_name":"10018"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'b2ff4676-a768-4844-a9a2-a94fffcbcf1c',
      wid: 'b2ff4676-a768-4844-a9a2-a94fffcbcf1c',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'b',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '54abd003-c1cd-4499-a7af-02cccc08aa3e',
  },
  {
    contactCard: {
      addressCity: 'Orlando',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Florida',
      addressStateShort: 'FL',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Alex-client-1899',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":28.582364,"lng":-81.**************},"viewport":{"east":-81.**************,"west":-81.*************,"north":28.5836225302915,"south":28.58092456970849},"location_type":"ROOFTOP"},"place_id":"ChIJ-SfZU_t554gRKp2wxDbqoTI","plus_code":{"global_code":"76WWHHJ5+W9","compound_code":"HHJ5+W9 Orlando, FL, USA"},"formatted_address":"4401 Seaboard Rd, Orlando, FL 32808, USA","address_components":[{"types":["street_number"],"long_name":"4401","short_name":"4401"},{"types":["route"],"long_name":"Seaboard Road","short_name":"Seaboard Rd"},{"types":["neighborhood","political"],"long_name":"Seaboard Industrial","short_name":"Seaboard Industrial"},{"types":["locality","political"],"long_name":"Orlando","short_name":"Orlando"},{"types":["administrative_area_level_2","political"],"long_name":"Orange County","short_name":"Orange County"},{"types":["administrative_area_level_1","political"],"long_name":"Florida","short_name":"FL"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"32808","short_name":"32808"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '5687d3fe-b296-4eb9-9dbd-6f1bca524c39',
      wid: '5687d3fe-b296-4eb9-9dbd-6f1bca524c39',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bortnik',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '5f408aff-7337-4de3-af83-3fc071ba1e4a',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'P',
      formOfIncorporation: null,
      geodata:
        '{"types":["route"],"geometry":{"bounds":{"east":-118.286993,"west":-118.3045586,"north":33.92119330000002,"south":33.92113369999998},"location":{"lat":33.9211933,"lng":-118.2983196},"viewport":{"east":-118.286993,"west":-118.3045586,"north":33.92251248029149,"south":33.9198145197085},"location_type":"GEOMETRIC_CENTER"},"place_id":"EidXZXN0IDEyM3JkIFN0cmVldCwgTG9zIEFuZ2VsZXMsIENBLCBVU0EiLiosChQKEgkpu7aaCMrCgBHX-Ex1HX6UphIUChIJE9on3F3HwoAR9AhGJW_fL-I","formatted_address":"W 123rd St, Los Angeles, CA, USA","address_components":[{"types":["route"],"long_name":"West 123rd Street","short_name":"W 123rd St"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: true,
      id: 'b6ee8f60-1cd6-4d68-b5a4-92d0b8299c92',
      wid: 'b6ee8f60-1cd6-4d68-b5a4-92d0b8299c92',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'K',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: '',
      nickname: '',
      notes: '',
      partner: {
        firstName: 'Wife',
        id: '7ab3b66c-4dcc-401b-af16-c7976d6028ee',
        lastName: 'Kim',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '+11231231234',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '285969c6-9a39-4ea1-955c-0bedad19b6cf',
  },
  {
    contactCard: {
      addressCity: 'San Francisco',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A665',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":37.7959278,"lng":-122.4013308},"viewport":{"east":-122.3999682197085,"west":-122.*************,"north":37.7972050302915,"south":37.7945070697085},"location_type":"ROOFTOP"},"place_id":"ChIJ-Q3ZHWCAhYARvQT-2IUs5-k","plus_code":{"global_code":"849VQHWX+9F","compound_code":"QHWX+9F North Beach, San Francisco, CA, USA"},"formatted_address":"444 Washington St, San Francisco, CA 94111, USA","address_components":[{"types":["street_number"],"long_name":"444","short_name":"444"},{"types":["route"],"long_name":"Washington Street","short_name":"Washington St"},{"types":["neighborhood","political"],"long_name":"North Beach","short_name":"North Beach"},{"types":["locality","political"],"long_name":"San Francisco","short_name":"SF"},{"types":["administrative_area_level_2","political"],"long_name":"San Francisco County","short_name":"San Francisco County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"94111","short_name":"94111"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '35c13721-aa04-41f0-9aa5-ca32d73a04fd',
      wid: '35c13721-aa04-41f0-9aa5-ca32d73a04fd',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Alina665',
        id: 'dcc0537a-5459-4ab7-914d-64cb7dedd124',
        lastName: 'Bortnik',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '+15454545455',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '7c3a9622-a4ce-4869-a8a0-aff1542dbcdb',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a_803',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":40.7647771,"lng":-73.95480069999999},"viewport":{"east":-73.95321996970848,"west":-73.9559179302915,"north":40.7658240302915,"south":40.7631260697085},"location_type":"ROOFTOP"},"place_id":"ChIJ9wQMjsNYwokRs0JnymlXYG4","plus_code":{"global_code":"87G8Q27W+W3","compound_code":"Q27W+W3 New York, NY, USA"},"formatted_address":"525 E 68th St, New York, NY 10065, USA","address_components":[{"types":["street_number"],"long_name":"525","short_name":"525"},{"types":["route"],"long_name":"East 68th Street","short_name":"E 68th St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10065","short_name":"10065"},{"types":["postal_code_suffix"],"long_name":"4870","short_name":"4870"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '6bcfb406-83ec-4b48-9b09-e590e68e7e4c',
      wid: '6bcfb406-83ec-4b48-9b09-e590e68e7e4c',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'b',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'dd737d8f-1379-47c3-8565-6a32ca6e5034',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a804',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":40.70348910000001,"lng":-74.00962899999999},"viewport":{"east":-74.00833716970848,"west":-74.0110351302915,"north":40.7048871302915,"south":40.7021891697085},"location_type":"ROOFTOP"},"place_id":"ChIJpzp4kYNbwokRGi8KQbC2-KI","plus_code":{"global_code":"87G7PX3R+94","compound_code":"PX3R+94 New York, NY, USA"},"formatted_address":"55 Water St, New York, NY 10041, USA","address_components":[{"types":["street_number"],"long_name":"55","short_name":"55"},{"types":["route"],"long_name":"Water Street","short_name":"Water St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10041","short_name":"10041"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '95305967-ab6a-4d29-9e6a-f73f3f7dfa29',
      wid: '95305967-ab6a-4d29-9e6a-f73f3f7dfa29',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'b',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '791c7a76-e104-4b93-9a80-d8d48b072ff6',
  },
  {
    contactCard: {
      addressCity: 'Atlanta',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Georgia',
      addressStateShort: 'GA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a805',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":33.7685215,"lng":-84.386152},"viewport":{"east":-84.38432121970848,"west":-84.3870191802915,"north":33.7702992302915,"south":33.7676012697085},"location_type":"ROOFTOP"},"place_id":"ChIJqRRsfEUF9YgR-nHLAyBwxDs","plus_code":{"global_code":"865QQJ97+CG","compound_code":"QJ97+CG Atlanta, GA, USA"},"formatted_address":"550 Peachtree St NE, Atlanta, GA 30308, USA","address_components":[{"types":["street_number"],"long_name":"550","short_name":"550"},{"types":["route"],"long_name":"Peachtree Street Northeast","short_name":"Peachtree St NE"},{"types":["neighborhood","political"],"long_name":"SoNo","short_name":"SoNo"},{"types":["locality","political"],"long_name":"Atlanta","short_name":"Atlanta"},{"types":["administrative_area_level_2","political"],"long_name":"Fulton County","short_name":"Fulton County"},{"types":["administrative_area_level_1","political"],"long_name":"Georgia","short_name":"GA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"30308","short_name":"30308"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'a3c4ee0c-4a14-4d03-b9f7-c8dd270254cb',
      wid: 'a3c4ee0c-4a14-4d03-b9f7-c8dd270254cb',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'cd',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '025ee7c3-9175-4ae9-9747-bace23a6995f',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'a1600',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":40.70348910000001,"lng":-74.00962899999999},"viewport":{"east":-74.00833716970848,"west":-74.0110351302915,"north":40.7048871302915,"south":40.7021891697085},"location_type":"ROOFTOP"},"place_id":"ChIJpzp4kYNbwokRGi8KQbC2-KI","plus_code":{"global_code":"87G7PX3R+94","compound_code":"PX3R+94 New York, NY, USA"},"formatted_address":"55 Water St, New York, NY 10041, USA","address_components":[{"types":["street_number"],"long_name":"55","short_name":"55"},{"types":["route"],"long_name":"Water Street","short_name":"Water St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10041","short_name":"10041"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'ff0ad3b3-f17b-4221-910d-f32714800b37',
      wid: 'ff0ad3b3-f17b-4221-910d-f32714800b37',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'b',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '72e34753-0015-429d-9f36-681460cd6678',
  },
  {
    contactCard: {
      addressCity: 'Orlando',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Florida',
      addressStateShort: 'FL',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A3300',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":28.5825777,"lng":-81.**************},"viewport":{"east":-81.*************,"west":-81.**************,"north":28.5837307802915,"south":28.5810328197085},"location_type":"ROOFTOP"},"place_id":"ChIJ-SfZU_t554gRKp2wxDbqoTI","plus_code":{"global_code":"76WWHHM5+2F","compound_code":"HHM5+2F Orlando, FL, USA"},"formatted_address":"4401 Seaboard Rd, Orlando, FL 32808, USA","address_components":[{"types":["street_number"],"long_name":"4401","short_name":"4401"},{"types":["route"],"long_name":"Seaboard Road","short_name":"Seaboard Rd"},{"types":["neighborhood","political"],"long_name":"Seaboard Industrial","short_name":"Seaboard Industrial"},{"types":["locality","political"],"long_name":"Orlando","short_name":"Orlando"},{"types":["administrative_area_level_2","political"],"long_name":"Orange County","short_name":"Orange County"},{"types":["administrative_area_level_1","political"],"long_name":"Florida","short_name":"FL"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"32808","short_name":"32808"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'e486d5d1-c545-4eb2-b27c-f12641b6ba9c',
      wid: 'e486d5d1-c545-4eb2-b27c-f12641b6ba9c',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '0bc309c1-b1fa-4439-9012-43e097453806',
  },
  {
    contactCard: {
      addressCity: null,
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'P',
      formOfIncorporation: null,
      geodata:
        '{"types":["administrative_area_level_1","political"],"geometry":{"bounds":{"east":-114.131211,"west":-124.482003,"north":42.0095169,"south":32.528832},"location":{"lat":36.778261,"lng":-119.4179324},"viewport":{"east":-114.131211,"west":-124.482003,"north":42.0095169,"south":32.528832},"location_type":"APPROXIMATE"},"place_id":"ChIJPV4oX_65j4ARVW8IJ6IJUYs","formatted_address":"California, USA","address_components":[{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'c5eb0dbf-7885-42a7-b62e-9322ce02d433',
      wid: 'c5eb0dbf-7885-42a7-b62e-9322ce02d433',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'L',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: '',
      nickname: '',
      notes: '',
      partner: null,
      phoneNumber: '',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'e8a0e843-b728-4696-87ad-d2d78846b629',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A_rd1',
      formOfIncorporation: null,
      geodata:
        '{"types":["subpremise"],"geometry":{"location":{"lat":40.7229769,"lng":-74.0093524},"viewport":{"east":-74.00813666970849,"west":-74.01083463029151,"north":40.72433993029151,"south":40.72164196970851},"location_type":"ROOFTOP"},"place_id":"ChIJTcU39fRZwokRl-qIpUpQq9k","formatted_address":"443 Greenwich St, New York, NY 10013, USA","address_components":[{"types":["street_number"],"long_name":"443","short_name":"443"},{"types":["route"],"long_name":"Greenwich Street","short_name":"Greenwich St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10013","short_name":"10013"},{"types":["postal_code_suffix"],"long_name":"1702","short_name":"1702"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'aa0f3d2c-02bd-415d-bb01-4d76d506cb48',
      wid: 'aa0f3d2c-02bd-415d-bb01-4d76d506cb48',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B_rd2',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'd70421df-2483-4b05-a178-1debede2a89f',
  },
  {
    contactCard: {
      addressCity: 'Commerce',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A2',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-118.1903961,"west":-118.1905352,"north":34.0111494,"south":34.01102470000001},"location":{"lat":34.0110687,"lng":-118.190454},"viewport":{"east":-118.1891619697085,"west":-118.1918599302915,"north":34.0122981802915,"south":34.0096002197085},"location_type":"ROOFTOP"},"place_id":"ChIJOfuwEDvPwoARl2k-nvJrt3Q","formatted_address":"4341 E Washington Blvd, Commerce, CA 90023, USA","address_components":[{"types":["street_number"],"long_name":"4341","short_name":"4341"},{"types":["route"],"long_name":"East Washington Boulevard","short_name":"E Washington Blvd"},{"types":["locality","political"],"long_name":"Commerce","short_name":"Commerce"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90023","short_name":"90023"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '35da1c24-37c8-4478-9e86-93531b711ab2',
      wid: '35da1c24-37c8-4478-9e86-93531b711ab2',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B2',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'a76e3e34-c5ef-4149-aeaa-a5ca9acf9857',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A3',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-118.343678,"west":-118.3438969,"north":34.0793532,"south":34.0791176},"location":{"lat":34.0792272,"lng":-118.3437748},"viewport":{"east":-118.3423878697085,"west":-118.3450858302915,"north":34.0805843802915,"south":34.0778864197085},"location_type":"ROOFTOP"},"place_id":"ChIJ-wYR0dW4woARja649Ay_PAI","formatted_address":"434 N La Brea Ave, Los Angeles, CA 90036, USA","address_components":[{"types":["street_number"],"long_name":"434","short_name":"434"},{"types":["route"],"long_name":"North La Brea Avenue","short_name":"N La Brea Ave"},{"types":["neighborhood","political"],"long_name":"Central LA","short_name":"Central LA"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90036","short_name":"90036"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '2ad2d4f5-658e-4253-853a-a842ab653442',
      wid: '2ad2d4f5-658e-4253-853a-a842ab653442',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B3',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'da35f5d0-1454-4759-926a-98fc9b5e6081',
  },
  {
    contactCard: {
      addressCity: 'Minneapolis',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Minnesota',
      addressStateShort: 'MN',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A_11',
      formOfIncorporation: null,
      geodata:
        '{"types":["postal_code"],"geometry":{"bounds":{"east":-93.2455059,"west":-93.27549,"north":44.992864,"south":44.9684369},"location":{"lat":44.97583909999999,"lng":-93.2571034},"viewport":{"east":-93.2455059,"west":-93.27549,"north":44.992864,"south":44.9684369},"location_type":"APPROXIMATE"},"place_id":"ChIJYedxTmAts1IR-hW5QlZNep0","formatted_address":"Minneapolis, MN 55415, USA","address_components":[{"types":["postal_code"],"long_name":"55415","short_name":"55415"},{"types":["locality","political"],"long_name":"Minneapolis","short_name":"Minneapolis"},{"types":["administrative_area_level_2","political"],"long_name":"Hennepin County","short_name":"Hennepin County"},{"types":["administrative_area_level_1","political"],"long_name":"Minnesota","short_name":"MN"},{"types":["country","political"],"long_name":"United States","short_name":"US"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'ef1c5f2e-8ed9-488a-8225-15453301cde4',
      wid: 'ef1c5f2e-8ed9-488a-8225-15453301cde4',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B_11',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '7df20c4f-49be-4c26-b10c-c7a2f5404ce7',
  },
  {
    contactCard: {
      addressCity: 'Orlando',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Florida',
      addressStateShort: 'FL',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Guoping',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-81.2810224,"west":-81.28130089999999,"north":28.5387897,"south":28.538192},"location":{"lat":28.5384663,"lng":-81.2811755},"viewport":{"east":-81.27975776970848,"west":-81.28245573029149,"north":28.53983983029151,"south":28.5371418697085},"location_type":"ROOFTOP"},"place_id":"ChIJZbOo2LNl54gRopN70nWoEc8","formatted_address":"7780 Lake Underhill Rd, Orlando, FL 32822, USA","address_components":[{"types":["street_number"],"long_name":"7780","short_name":"7780"},{"types":["route"],"long_name":"Lake Underhill Road","short_name":"Lake Underhill Rd"},{"types":["locality","political"],"long_name":"Orlando","short_name":"Orlando"},{"types":["administrative_area_level_2","political"],"long_name":"Orange County","short_name":"Orange County"},{"types":["administrative_area_level_1","political"],"long_name":"Florida","short_name":"FL"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"32822","short_name":"32822"},{"types":["postal_code_suffix"],"long_name":"8780","short_name":"8780"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '5b0b6e5c-a775-451f-8b4a-94246fb9e198',
      wid: '5b0b6e5c-a775-451f-8b4a-94246fb9e198',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Li',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'adcab009-bed5-44a3-b91a-897edd83da4a',
  },
  {
    contactCard: {
      addressCity: 'Trumbull',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Connecticut',
      addressStateShort: 'CT',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A-test-1',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-73.242694,"west":-73.24371599999999,"north":41.22771170000001,"south":41.2271267},"location":{"lat":41.2273667,"lng":-73.24305369999999},"viewport":{"east":-73.2418560197085,"west":-73.2445539802915,"north":41.2287207302915,"south":41.2260227697085},"location_type":"ROOFTOP"},"place_id":"ChIJhaUedzAP6IkRxa5r3NW2S7c","formatted_address":"5520 Park Ave, Trumbull, CT 06611, USA","address_components":[{"types":["street_number"],"long_name":"5520","short_name":"5520"},{"types":["route"],"long_name":"Park Avenue","short_name":"Park Ave"},{"types":["locality","political"],"long_name":"Trumbull","short_name":"Trumbull"},{"types":["administrative_area_level_2","political"],"long_name":"Fairfield County","short_name":"Fairfield County"},{"types":["administrative_area_level_1","political"],"long_name":"Connecticut","short_name":"CT"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"06611","short_name":"06611"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '5e92ea9f-479a-4687-9218-23e25e6a5f09',
      wid: '5e92ea9f-479a-4687-9218-23e25e6a5f09',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B-test-cl-1',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '164c8af0-298f-40d9-80cb-b93d2a5b9f4e',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A_f2',
      formOfIncorporation: null,
      geodata:
        '{"types":["establishment","point_of_interest"],"geometry":{"location":{"lat":40.7228796,"lng":-74.00948},"viewport":{"east":-74.00818006970849,"west":-74.01087803029151,"north":40.7243613802915,"south":40.72166341970851},"location_type":"ROOFTOP"},"place_id":"ChIJVVVVVfRZwokRJRNVNIUgZfg","plus_code":{"global_code":"87G7PXFR+56","compound_code":"PXFR+56 New York, NY, USA"},"formatted_address":"443 Greenwich St, New York, NY 10013, USA","address_components":[{"types":["street_number"],"long_name":"443","short_name":"443"},{"types":["route"],"long_name":"Greenwich Street","short_name":"Greenwich St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10013","short_name":"10013"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '9b4282f8-9265-4702-a475-af1dad079c3e',
      wid: '9b4282f8-9265-4702-a475-af1dad079c3e',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B_f2',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '0e44c873-5426-4dd3-8fb3-228d750b8861',
  },
  {
    contactCard: {
      addressCity: 'Houston',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'Texas',
      addressStateShort: 'TX',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A_3',
      formOfIncorporation: null,
      geodata:
        '{"types":["postal_code"],"geometry":{"bounds":{"east":-95.57585480000002,"west":-95.72045,"north":29.8799411,"south":29.784243},"location":{"lat":29.8296506,"lng":-95.6668306},"viewport":{"east":-95.57585480000002,"west":-95.72045,"north":29.8799411,"south":29.784243},"location_type":"APPROXIMATE"},"place_id":"ChIJCT-dbyLYQIYRqQ7B4SEa4ak","formatted_address":"Houston, TX 77084, USA","address_components":[{"types":["postal_code"],"long_name":"77084","short_name":"77084"},{"types":["locality","political"],"long_name":"Houston","short_name":"Houston"},{"types":["administrative_area_level_2","political"],"long_name":"Harris County","short_name":"Harris County"},{"types":["administrative_area_level_1","political"],"long_name":"Texas","short_name":"TX"},{"types":["country","political"],"long_name":"United States","short_name":"US"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '7ce2d2a4-80c5-4e3c-b74f-e0ea51881844',
      wid: '7ce2d2a4-80c5-4e3c-b74f-e0ea51881844',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B_3',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '115bc544-9073-47e0-b276-987f29848a11',
  },
  {
    contactCard: {
      addressCity: 'Buffalo',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A_4',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":42.894258,"lng":-78.87134320000001},"viewport":{"east":-78.87009226970852,"west":-78.87279023029153,"north":42.8956256802915,"south":42.8929277197085},"location_type":"ROOFTOP"},"place_id":"ChIJByoCl1oS04kR58l5JvuolOM","plus_code":{"global_code":"87J3V4VH+PF","compound_code":"V4VH+PF Buffalo, NY, USA"},"formatted_address":"745 Main St, Buffalo, NY 14203, USA","address_components":[{"types":["street_number"],"long_name":"745","short_name":"745"},{"types":["route"],"long_name":"Main Street","short_name":"Main St"},{"types":["neighborhood","political"],"long_name":"Allentown","short_name":"Allentown"},{"types":["locality","political"],"long_name":"Buffalo","short_name":"Buffalo"},{"types":["administrative_area_level_2","political"],"long_name":"Erie County","short_name":"Erie County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"14203","short_name":"14203"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '1341af36-4ff0-45cd-9611-ead01d94b2ab',
      wid: '1341af36-4ff0-45cd-9611-ead01d94b2ab',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B_4',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Alina',
        id: 'a2f05d12-8b2d-4ff1-9882-a0affae10431',
        lastName: 'Bortnik',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '566b960f-7c60-4194-836d-7c7165eec380',
  },
  {
    contactCard: {
      addressCity: 'Monterey Park',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: null,
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A_tu2',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-118.1342778,"west":-118.1344524,"north":34.0567313,"south":34.0565557},"location":{"lat":34.0566578,"lng":-118.1343481},"viewport":{"east":-118.1330778697085,"west":-118.1357758302915,"north":34.0580015302915,"south":34.0553035697085},"location_type":"ROOFTOP"},"place_id":"ChIJH0vCyl_FwoARLIkQ5Y0b_VA","formatted_address":"440 S Atlantic Blvd, Monterey Park, CA 91754, USA","address_components":[{"types":["street_number"],"long_name":"440","short_name":"440"},{"types":["route"],"long_name":"South Atlantic Boulevard","short_name":"S Atlantic Blvd"},{"types":["locality","political"],"long_name":"Monterey Park","short_name":"Monterey Park"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"91754","short_name":"91754"},{"types":["postal_code_suffix"],"long_name":"3269","short_name":"3269"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'e893f6d4-d649-49e9-868c-d0bd5c305391',
      wid: 'e893f6d4-d649-49e9-868c-d0bd5c305391',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B_tu2',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'a3c080c3-e9b9-4d77-b082-ecbb76990ee7',
  },
  {
    contactCard: {
      addressCity: 'Minneapolis',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: ', Minneapolis, Minnesota, 55403, United States',
      addressState: 'Minnesota',
      addressStateShort: 'MN',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Mark',
      formOfIncorporation: null,
      geodata:
        '{"types":["postal_code"],"geometry":{"bounds":{"east":-93.2473138,"west":-93.30455,"north":44.99369000000001,"south":44.9626919},"location":{"lat":44.9691279,"lng":-93.2878982},"viewport":{"east":-93.2473138,"west":-93.30455,"north":44.99369000000001,"south":44.9626919},"location_type":"APPROXIMATE"},"place_id":"ChIJKR8TIsIys1IRKXV3alxqfac","formatted_address":"Minneapolis, MN 55403, USA","address_components":[{"types":["postal_code"],"long_name":"55403","short_name":"55403"},{"types":["locality","political"],"long_name":"Minneapolis","short_name":"Minneapolis"},{"types":["administrative_area_level_2","political"],"long_name":"Hennepin County","short_name":"Hennepin County"},{"types":["administrative_area_level_1","political"],"long_name":"Minnesota","short_name":"MN"},{"types":["country","political"],"long_name":"United States","short_name":"US"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '351b8778-8bf5-420c-a43d-5ab1f3034b72',
      wid: '351b8778-8bf5-420c-a43d-5ab1f3034b72',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Vandalay',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '27edd29c-4e5b-4310-bb9d-c15cc85f3863',
  },
  {
    contactCard: {
      addressCity: 'Tempe',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '3321 South Kachina Drive, Tempe, Arizona, 85282, United States',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Martin',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-111.9064557,"west":-111.9067488,"north":33.3922178,"south":33.3919413},"location":{"lat":33.392081,"lng":-111.9065802},"viewport":{"east":-111.9052878197085,"west":-111.9079857802915,"north":33.3934285302915,"south":33.3907305697085},"location_type":"ROOFTOP"},"place_id":"ChIJ19NXWIAIK4cRcCgFdo_S_WA","formatted_address":"3321 S Kachina Dr, Tempe, AZ 85282, USA","address_components":[{"types":["street_number"],"long_name":"3321","short_name":"3321"},{"types":["route"],"long_name":"South Kachina Drive","short_name":"S Kachina Dr"},{"types":["neighborhood","political"],"long_name":"McClintock Manor","short_name":"McClintock Manor"},{"types":["locality","political"],"long_name":"Tempe","short_name":"Tempe"},{"types":["administrative_area_level_2","political"],"long_name":"Maricopa County","short_name":"Maricopa County"},{"types":["administrative_area_level_1","political"],"long_name":"Arizona","short_name":"AZ"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"85282","short_name":"85282"},{"types":["postal_code_suffix"],"long_name":"5839","short_name":"5839"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '13fd9326-fa5f-4907-95fa-89402d2e23ab',
      wid: '13fd9326-fa5f-4907-95fa-89402d2e23ab',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Duncan',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '0c13f04e-403a-4476-81e3-9289b92ab842',
  },
  {
    contactCard: {
      addressCity: 'San Francisco',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '44 Montgomery Street, San Francisco, California, 94104, United States',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Martin',
      formOfIncorporation: null,
      geodata:
        '{"types":["establishment","point_of_interest"],"geometry":{"location":{"lat":37.7897312,"lng":-122.401906},"viewport":{"east":-122.*************,"west":-122.*************,"north":37.7910917302915,"south":37.7883937697085},"location_type":"ROOFTOP"},"place_id":"ChIJ__9v0YmAhYARC1hbPG9GfhA","plus_code":{"global_code":"849VQHQX+V6","compound_code":"QHQX+V6 Financial District, San Francisco, CA, USA"},"formatted_address":"44 Montgomery St, San Francisco, CA 94104, USA","address_components":[{"types":["street_number"],"long_name":"44","short_name":"44"},{"types":["route"],"long_name":"Montgomery Street","short_name":"Montgomery St"},{"types":["neighborhood","political"],"long_name":"Financial District","short_name":"Financial District"},{"types":["locality","political"],"long_name":"San Francisco","short_name":"SF"},{"types":["administrative_area_level_2","political"],"long_name":"San Francisco County","short_name":"San Francisco County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"94104","short_name":"94104"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '22dfbeb9-77a1-4dce-b60e-c2b9e6cbd1bf',
      wid: '22dfbeb9-77a1-4dce-b60e-c2b9e6cbd1bf',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Scorcese',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '95840622-a715-4556-b860-d1bdbb1918f2',
  },
  {
    contactCard: {
      addressCity: 'Albuquerque',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: 'U.S. Route 66, Albuquerque, New Mexico, , United States',
      addressState: 'New Mexico',
      addressStateShort: 'NM',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Luke',
      formOfIncorporation: null,
      geodata:
        '{"types":["route"],"geometry":{"bounds":{"east":-106.4706427,"west":-106.8814296,"north":35.09551250000002,"south":35.04675469999997},"location":{"lat":35.0854304,"lng":-106.6856129},"viewport":{"east":-106.4706427,"west":-106.8814296,"north":35.09551250000002,"south":35.04675469999997},"location_type":"GEOMETRIC_CENTER"},"place_id":"EiNVLlMuIFJvdXRlIDY2LCBBbGJ1cXVlcnF1ZSwgTk0sIFVTQSIuKiwKFAoSCTEoElYYVdqHES-Fcof5h7v8EhQKEgl7gwnT3QoihxH99tm4zvjTwA","formatted_address":"U.S. Rt. 66, Albuquerque, NM, USA","address_components":[{"types":["route"],"long_name":"U.S. Route 66","short_name":"U.S. Rt. 66"},{"types":["locality","political"],"long_name":"Albuquerque","short_name":"Albuquerque"},{"types":["administrative_area_level_2","political"],"long_name":"Bernalillo County","short_name":"Bernalillo County"},{"types":["administrative_area_level_1","political"],"long_name":"New Mexico","short_name":"NM"},{"types":["country","political"],"long_name":"United States","short_name":"US"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'fa3d83cd-92b1-4877-b385-2b21f3fd8fa2',
      wid: 'fa3d83cd-92b1-4877-b385-2b21f3fd8fa2',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Skyworker',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'bc6ca073-f8ac-4ee8-9370-da769313b396',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '545 8th Avenue, New York, New York, 10018, United States',
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Kirk',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-73.9919688,"west":-73.99241479999999,"north":40.7546543,"south":40.7543584},"location":{"lat":40.7544946,"lng":-73.99216539999999},"viewport":{"east":-73.99079491970849,"west":-73.9934928802915,"north":40.7558553302915,"south":40.7531573697085},"location_type":"ROOFTOP"},"place_id":"ChIJHxt6A61ZwokRCKP8zrj1-IQ","formatted_address":"545 8th Ave, New York, NY 10018, USA","address_components":[{"types":["street_number"],"long_name":"545","short_name":"545"},{"types":["route"],"long_name":"8th Avenue","short_name":"8th Ave"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10018","short_name":"10018"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '1fad7462-8557-41e6-8c71-01a29a5a9a02',
      wid: '1fad7462-8557-41e6-8c71-01a29a5a9a02',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Douglas',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'e0738e9c-bf81-4130-9f32-6cab4c9952ca',
  },
  {
    contactCard: {
      addressCity: 'Decatur',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '556 North McDonough Street, Decatur, Georgia, 30030, United States',
      addressState: 'Georgia',
      addressStateShort: 'GA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Mike',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-84.2970015,"west":-84.2974493,"north":33.7742918,"south":33.77384660000001},"location":{"lat":33.7740847,"lng":-84.29724329999999},"viewport":{"east":-84.29587641970849,"west":-84.2985743802915,"north":33.7752759302915,"south":33.7725779697085},"location_type":"ROOFTOP"},"place_id":"ChIJg_ZAzD0H9YgRwjQpthII9Bc","formatted_address":"556 N McDonough St, Decatur, GA 30030, USA","address_components":[{"types":["street_number"],"long_name":"556","short_name":"556"},{"types":["route"],"long_name":"North McDonough Street","short_name":"N McDonough St"},{"types":["neighborhood","political"],"long_name":"Downtown Decatur","short_name":"Downtown Decatur"},{"types":["locality","political"],"long_name":"Decatur","short_name":"Decatur"},{"types":["administrative_area_level_2","political"],"long_name":"DeKalb County","short_name":"Dekalb County"},{"types":["administrative_area_level_1","political"],"long_name":"Georgia","short_name":"GA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"30030","short_name":"30030"},{"types":["postal_code_suffix"],"long_name":"3355","short_name":"3355"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '43b1bd8b-7035-42dc-a626-108bc9befb5c',
      wid: '43b1bd8b-7035-42dc-a626-108bc9befb5c',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Myers',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '593d54ea-aed1-47ef-8660-e92021d17b73',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '443 Greenwich Street, New York, New York, 10013, United States',
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Draco',
      formOfIncorporation: null,
      geodata:
        '{"types":["establishment","point_of_interest"],"geometry":{"location":{"lat":40.7228796,"lng":-74.00948},"viewport":{"east":-74.00818006970849,"west":-74.01087803029151,"north":40.7243613802915,"south":40.72166341970851},"location_type":"ROOFTOP"},"place_id":"ChIJVVVVVfRZwokRJRNVNIUgZfg","plus_code":{"global_code":"87G7PXFR+56","compound_code":"PXFR+56 New York, NY, USA"},"formatted_address":"443 Greenwich St, New York, NY 10013, USA","address_components":[{"types":["street_number"],"long_name":"443","short_name":"443"},{"types":["route"],"long_name":"Greenwich Street","short_name":"Greenwich St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10013","short_name":"10013"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '4507c0d9-0384-4bf0-ae50-f3b40982a736',
      wid: '4507c0d9-0384-4bf0-ae50-f3b40982a736',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Malfoy',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '57a2589a-2da7-4de2-9a8b-afba95bb5015',
  },
  {
    contactCard: {
      addressCity: 'Chicago',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '2233 West Division Street, Chicago, Illinois, 60622, United States',
      addressState: 'Illinois',
      addressStateShort: 'IL',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Raul by AD',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-87.68234009999999,"west":-87.68427249999999,"north":41.9029134,"south":41.9017896},"location":{"lat":41.9020876,"lng":-87.6829261},"viewport":{"east":-87.68195731970849,"west":-87.6846552802915,"north":41.90371298029149,"south":41.9010150197085},"location_type":"ROOFTOP"},"place_id":"ChIJ1zphR7HSD4gRko8AVIa1d2k","formatted_address":"2233 W Division St, Chicago, IL 60622, USA","address_components":[{"types":["street_number"],"long_name":"2233","short_name":"2233"},{"types":["route"],"long_name":"West Division Street","short_name":"W Division St"},{"types":["neighborhood","political"],"long_name":"West Town","short_name":"West Town"},{"types":["locality","political"],"long_name":"Chicago","short_name":"Chicago"},{"types":["administrative_area_level_2","political"],"long_name":"Cook County","short_name":"Cook County"},{"types":["administrative_area_level_1","political"],"long_name":"Illinois","short_name":"IL"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"60622","short_name":"60622"},{"types":["postal_code_suffix"],"long_name":"8151","short_name":"8151"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'c6a3e6d4-f068-4760-91d5-eb46bade8da8',
      wid: 'c6a3e6d4-f068-4760-91d5-eb46bade8da8',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Li',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Mingxiao',
        id: 'b91a024d-513f-43cb-b1ea-d4fad39ebc8c',
        lastName: 'Fu',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '423862fe-fa33-458d-a56f-ccddc3ff49cf',
  },
  {
    contactCard: {
      addressCity: 'Houston',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '6655 Travis Street, Houston, Texas, 77030, United States',
      addressState: 'Texas',
      addressStateShort: 'TX',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Raul',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-95.4033977,"west":-95.4038592,"north":29.7096747,"south":29.7092066},"location":{"lat":29.709474,"lng":-95.4037313},"viewport":{"east":-95.*************,"west":-95.**************,"north":29.71084588029149,"south":29.70814791970849},"location_type":"ROOFTOP"},"place_id":"ChIJYVeLwnHAQIYRPpDJzIS3seQ","formatted_address":"6655 Travis St, Houston, TX 77030, USA","address_components":[{"types":["street_number"],"long_name":"6655","short_name":"6655"},{"types":["route"],"long_name":"Travis Street","short_name":"Travis St"},{"types":["neighborhood","political"],"long_name":"University Place","short_name":"University Place"},{"types":["locality","political"],"long_name":"Houston","short_name":"Houston"},{"types":["administrative_area_level_2","political"],"long_name":"Harris County","short_name":"Harris County"},{"types":["administrative_area_level_1","political"],"long_name":"Texas","short_name":"TX"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"77030","short_name":"77030"},{"types":["postal_code_suffix"],"long_name":"1312","short_name":"1312"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '9eb0592b-14a5-4ed1-bc46-48503d668ae6',
      wid: '9eb0592b-14a5-4ed1-bc46-48503d668ae6',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Li',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Mingxiao',
        id: '47d9b0f4-eee6-4c8e-a7b0-b14730ac4667',
        lastName: 'Fu',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '2339cca7-d7b3-4cfe-a564-874b51730c19',
  },
  {
    contactCard: {
      addressCity: 'Boston',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '55 Fruit Street, Boston, Massachusetts, 02114, United States',
      addressState: 'Massachusetts',
      addressStateShort: 'MA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Bla',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":42.3630828,"lng":-71.0686204},"viewport":{"east":-71.06720646970851,"west":-71.06990443029152,"north":42.36422083029149,"south":42.36152286970849},"location_type":"ROOFTOP"},"place_id":"ChIJm1IklZlw44kRgK8tf3E35xA","plus_code":{"global_code":"87JC9W7J+6H","compound_code":"9W7J+6H Boston, MA, USA"},"formatted_address":"55 Fruit St, Boston, MA 02114, USA","address_components":[{"types":["street_number"],"long_name":"55","short_name":"55"},{"types":["route"],"long_name":"Fruit Street","short_name":"Fruit St"},{"types":["neighborhood","political"],"long_name":"West End","short_name":"West End"},{"types":["locality","political"],"long_name":"Boston","short_name":"Boston"},{"types":["administrative_area_level_2","political"],"long_name":"Suffolk County","short_name":"Suffolk County"},{"types":["administrative_area_level_1","political"],"long_name":"Massachusetts","short_name":"MA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"02114","short_name":"02114"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '088473c0-c74e-43ae-aa63-7524b1458ddb',
      wid: '088473c0-c74e-43ae-aa63-7524b1458ddb',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'BlaBla',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '8fbf32af-76a3-4abc-9a1c-10d6fc16410b',
  },
  {
    contactCard: {
      addressCity: 'Guthrie',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1102 Dash For Cash Road, Guthrie, Texas, 79236, United States',
      addressState: 'Texas',
      addressStateShort: 'TX',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'bup',
      formOfIncorporation: null,
      geodata:
        '{"types":["establishment","point_of_interest"],"geometry":{"location":{"lat":33.6263205,"lng":-100.3374815},"viewport":{"east":-100.3362395197085,"west":-100.3389374802915,"north":33.6278326302915,"south":33.6251346697085},"location_type":"ROOFTOP"},"place_id":"ChIJKcYjCnJsVYYRausxc6W9ae8","plus_code":{"global_code":"855XJMG7+G2","compound_code":"JMG7+G2 Guthrie, TX, USA"},"formatted_address":"1102 Dash For Cash Road, Guthrie, TX 79236, USA","address_components":[{"types":["street_number"],"long_name":"1102","short_name":"1102"},{"types":["route"],"long_name":"Dash For Cash Road","short_name":"Dash For Cash Road"},{"types":["locality","political"],"long_name":"Guthrie","short_name":"Guthrie"},{"types":["administrative_area_level_2","political"],"long_name":"King County","short_name":"King County"},{"types":["administrative_area_level_1","political"],"long_name":"Texas","short_name":"TX"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"79236","short_name":"79236"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'fafdc6ec-db21-4499-b70a-7635b6ef740e',
      wid: 'fafdc6ec-db21-4499-b70a-7635b6ef740e',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'hlup',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'd2afac5a-a933-4ae7-8035-064fc5a654a1',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '560 Lexington Avenue, New York, New York, 10022, United States',
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Pla',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-73.9722754,"west":-73.97291659999999,"north":40.7570568,"south":40.7566189},"location":{"lat":40.7568337,"lng":-73.97267219999999},"viewport":{"east":-73.97123061970849,"west":-73.9739285802915,"north":40.7581868302915,"south":40.7554888697085},"location_type":"ROOFTOP"},"place_id":"ChIJ9wOHwvxYwokR-1Wx4wJU0f8","formatted_address":"560 Lexington Ave, New York, NY 10022, USA","address_components":[{"types":["street_number"],"long_name":"560","short_name":"560"},{"types":["route"],"long_name":"Lexington Avenue","short_name":"Lexington Ave"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10022","short_name":"10022"},{"types":["postal_code_suffix"],"long_name":"6828","short_name":"6828"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'dd43e0b1-5a45-496c-a65c-9b5d4b94d639',
      wid: 'dd43e0b1-5a45-496c-a65c-9b5d4b94d639',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Ple',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '14cc30d1-ebcb-4900-beda-dba866b615ee',
  },
  {
    contactCard: {
      addressCity: 'Chicago',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '939 West North Avenue suite 750, Chicago, Illinois, 60642, United States',
      addressState: 'Illinois',
      addressStateShort: 'IL',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Am',
      formOfIncorporation: null,
      geodata:
        '{"types":["establishment","gym","health","point_of_interest","school","spa"],"geometry":{"location":{"lat":41.9107382,"lng":-87.6529168},"viewport":{"east":-87.6516677697085,"west":-87.6543657302915,"north":41.9119684802915,"south":41.9092705197085},"location_type":"ROOFTOP"},"place_id":"ChIJgewO-SPTD4gRIuATS_TWono","plus_code":{"global_code":"86HJW86W+7R","compound_code":"W86W+7R Chicago, IL, USA"},"formatted_address":"939 W North Ave suite 750, Chicago, IL 60642, USA","address_components":[{"types":["subpremise"],"long_name":"suite 750","short_name":"suite 750"},{"types":["street_number"],"long_name":"939","short_name":"939"},{"types":["route"],"long_name":"West North Avenue","short_name":"W North Ave"},{"types":["neighborhood","political"],"long_name":"Ranch Triangle","short_name":"Ranch Triangle"},{"types":["locality","political"],"long_name":"Chicago","short_name":"Chicago"},{"types":["administrative_area_level_2","political"],"long_name":"Cook County","short_name":"Cook County"},{"types":["administrative_area_level_1","political"],"long_name":"Illinois","short_name":"IL"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"60642","short_name":"60642"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '39be3c05-7e7c-42f8-9d59-5b1b46b69bc3',
      wid: '39be3c05-7e7c-42f8-9d59-5b1b46b69bc3',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bum',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '37c25110-1ce5-4cc8-b985-a1a361a5912f',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '345 Park Avenue, New York, New York, 10154, United States',
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-73.9716548,"west":-73.9733005,"north":40.75829299999999,"south":40.7572736},"location":{"lat":40.7579332,"lng":-73.9722189},"viewport":{"east":-73.97112866970848,"west":-73.9738266302915,"north":40.7591322802915,"south":40.7564343197085},"location_type":"ROOFTOP"},"place_id":"ChIJY4Odu_xYwokRKcbdBPYj2KM","formatted_address":"345 Park Avenue, 590 Lexington Ave, New York, NY 10154, USA","address_components":[{"types":["premise"],"long_name":"345 Park Avenue","short_name":"345 Park Avenue"},{"types":["street_number"],"long_name":"590","short_name":"590"},{"types":["route"],"long_name":"Lexington Avenue","short_name":"Lexington Ave"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10154","short_name":"10154"},{"types":["postal_code_suffix"],"long_name":"3301","short_name":"3301"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '6cc49359-48b7-4761-a974-6f76f48a4b52',
      wid: '6cc49359-48b7-4761-a974-6f76f48a4b52',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'wealthboxcontact41',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Wang',
        id: '4d71ab38-7af0-47ee-8891-8f7a8317a0bf',
        lastName: 'wealthboxcontact41',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '6b782d11-e21a-4915-b401-afe7876703b9',
  },
  {
    contactCard: {
      addressCity: 'Del Monte Forest',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '17 Mile Drive, Del Monte Forest, California, , United States',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Ingrid',
      formOfIncorporation: null,
      geodata:
        '{"types":["route"],"geometry":{"bounds":{"east":-121.9195644,"west":-121.9738132,"north":36.59254650000003,"south":36.56059159999997},"location":{"lat":36.5727577,"lng":-121.948717},"viewport":{"east":-121.9195644,"west":-121.9738132,"north":36.59254650000003,"south":36.56059159999997},"location_type":"GEOMETRIC_CENTER"},"place_id":"Eh8xNyBNaWxlIERyaXZlLCBQZWJibGUgQmVhY2gsIENBIi4qLAoUChIJxyKzvNnmjYARt0oWiLxxUO8SFAoSCa1r0HVV5o2AESUhh5kBPeuc","formatted_address":"17 Mile Dr, Del Monte Forest, CA, USA","address_components":[{"types":["route"],"long_name":"17 Mile Drive","short_name":"17 Mile Dr"},{"types":["neighborhood","political"],"long_name":"Pebble Beach","short_name":"Pebble Beach"},{"types":["locality","political"],"long_name":"Del Monte Forest","short_name":"Del Monte Forest"},{"types":["administrative_area_level_2","political"],"long_name":"Monterey County","short_name":"Monterey County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'a064cd6e-87f0-4f0b-b209-ca65da9553ed',
      wid: 'a064cd6e-87f0-4f0b-b209-ca65da9553ed',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Santos',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'fe83d351-7f31-4c0d-ac0d-3d57cac563ca',
  },
  {
    contactCard: {
      addressCity: 'Arizona City',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: 'Arizona City, Arizona 85123, United States of America',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Test',
      formOfIncorporation: null,
      geodata:
        '{"types":["locality","political"],"geometry":{"bounds":{"east":-111.6365961,"west":-111.705616,"north":32.7630149,"south":32.73371780000001},"location":{"lat":32.7558935,"lng":-111.6709584},"viewport":{"east":-111.6365961,"west":-111.705616,"north":32.7630149,"south":32.73371780000001},"location_type":"APPROXIMATE"},"place_id":"ChIJ90OLRVRlKocRFjiCUGDC7Ik","formatted_address":"Arizona City, AZ 85123, USA","address_components":[{"types":["locality","political"],"long_name":"Arizona City","short_name":"Arizona City"},{"types":["administrative_area_level_2","political"],"long_name":"Pinal County","short_name":"Pinal County"},{"types":["administrative_area_level_1","political"],"long_name":"Arizona","short_name":"AZ"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"85123","short_name":"85123"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '6244351c-a18f-4e73-8c47-902f96a290dd',
      wid: '6244351c-a18f-4e73-8c47-902f96a290dd',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'User',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: '',
      nickname: '',
      notes: '',
      partner: {
        firstName: 'Test',
        id: 'c36d7e2c-dec4-4bb4-b472-e831718eaedf',
        lastName: 'Spouse',
        middleName: '',
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '+11231231321',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'c9ef1a63-99e5-4198-861a-6ddbf6cbaf2d',
  },
  {
    contactCard: {
      addressCity: 'Beaumont',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '10600 Highland Springs Avenue, Beaumont, California, 92223',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Test',
      formOfIncorporation: null,
      geodata:
        '{"types":["establishment","point_of_interest"],"geometry":{"location":{"lat":33.9676668,"lng":-116.941603},"viewport":{"east":-116.9405843697085,"west":-116.9432823302915,"north":33.96915423029149,"south":33.9664562697085},"location_type":"ROOFTOP"},"place_id":"ChIJWQk10f1E24AR64VZox2aHEM","plus_code":{"global_code":"8555X395+39","compound_code":"X395+39 Beaumont, CA, USA"},"formatted_address":"10600 Highland Springs Ave, Beaumont, CA 92223, USA","address_components":[{"types":["street_number"],"long_name":"10600","short_name":"10600"},{"types":["route"],"long_name":"Highland Springs Avenue","short_name":"Highland Springs Ave"},{"types":["locality","political"],"long_name":"Beaumont","short_name":"Beaumont"},{"types":["administrative_area_level_2","political"],"long_name":"Riverside County","short_name":"Riverside County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"92223","short_name":"92223"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '490aad3b-2a6d-4431-904e-d66530a0975b',
      wid: '490aad3b-2a6d-4431-904e-d66530a0975b',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Client',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: '',
      nickname: '',
      notes: '',
      partner: {
        firstName: 'My',
        id: '0bc0c816-d303-4755-8bfa-d2f48ce9a5c1',
        lastName: 'Spouse',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '+11231231233',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '80f0c34e-89ac-46c9-abbc-e4a29d80e2d5',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: ', New York, New York, , United States',
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Konj',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-74.0081522,"west":-74.010026,"north":40.7039451,"south":40.7025648},"location":{"lat":40.7033226,"lng":-74.0088962},"viewport":{"east":-74.00774011970849,"west":-74.01043808029151,"north":40.7046039302915,"south":40.7019059697085},"location_type":"ROOFTOP"},"place_id":"ChIJMQwbyhVawokRPmdg6gKA8-w","formatted_address":"55 Water Street, New York, NY, USA","address_components":[{"types":["premise"],"long_name":"55 Water Street","short_name":"55 Water Street"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '0212354b-bc69-40d6-acec-c2b6551bbbed',
      wid: '0212354b-bc69-40d6-acec-c2b6551bbbed',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Konj2',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '81ce6ece-63f5-4bcf-882a-609ec96a6ae5',
  },
  {
    contactCard: {
      addressCity: 'Houston',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '6701 Fannin Street, Houston, Texas, 77030, United States',
      addressState: 'Texas',
      addressStateShort: 'TX',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'FN',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-95.4019554,"west":-95.4026918,"north":29.7075561,"south":29.706577},"location":{"lat":29.706964,"lng":-95.4022177},"viewport":{"east":-95.*************,"west":-95.**************,"north":29.7084155302915,"south":29.7057175697085},"location_type":"ROOFTOP"},"place_id":"ChIJg9lUMHLAQIYRo-rlSj8VNHs","formatted_address":"6701 Fannin St, Houston, TX 77030, USA","address_components":[{"types":["street_number"],"long_name":"6701","short_name":"6701"},{"types":["route"],"long_name":"Fannin Street","short_name":"Fannin St"},{"types":["neighborhood","political"],"long_name":"South Central Houston","short_name":"South Central Houston"},{"types":["locality","political"],"long_name":"Houston","short_name":"Houston"},{"types":["administrative_area_level_2","political"],"long_name":"Harris County","short_name":"Harris County"},{"types":["administrative_area_level_1","political"],"long_name":"Texas","short_name":"TX"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"77030","short_name":"77030"},{"types":["postal_code_suffix"],"long_name":"2608","short_name":"2608"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'f5b99128-5dd8-49ea-b40e-e9ffc88500b1',
      wid: 'f5b99128-5dd8-49ea-b40e-e9ffc88500b1',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'LN',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '05d2df87-fd8f-495b-b243-0c3c03a7c72e',
  },
  {
    contactCard: {
      addressCity: 'Albuquerque',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: 'U.S. Route 66, Albuquerque, New Mexico, , United States',
      addressState: 'New Mexico',
      addressStateShort: 'NM',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'FN3243',
      formOfIncorporation: null,
      geodata:
        '{"types":["route"],"geometry":{"bounds":{"east":-106.4706427,"west":-106.8814296,"north":35.09551250000002,"south":35.04675469999997},"location":{"lat":35.0854304,"lng":-106.6856129},"viewport":{"east":-106.4706427,"west":-106.8814296,"north":35.09551250000002,"south":35.04675469999997},"location_type":"GEOMETRIC_CENTER"},"place_id":"Eh5VLlMuIFJvdXRlIDY2LCBBbGJ1cXVlcnF1ZSwgTk0iLiosChQKEgkxKBJWGFXahxEvhXKH-Ye7_BIUChIJe4MJ090KIocR_fbZuM7408A","formatted_address":"U.S. Rt. 66, Albuquerque, NM, USA","address_components":[{"types":["route"],"long_name":"U.S. Route 66","short_name":"U.S. Rt. 66"},{"types":["locality","political"],"long_name":"Albuquerque","short_name":"Albuquerque"},{"types":["administrative_area_level_2","political"],"long_name":"Bernalillo County","short_name":"Bernalillo County"},{"types":["administrative_area_level_1","political"],"long_name":"New Mexico","short_name":"NM"},{"types":["country","political"],"long_name":"United States","short_name":"US"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'd32c1916-f3b0-4dc9-a627-6e0fc8e0e635',
      wid: 'd32c1916-f3b0-4dc9-a627-6e0fc8e0e635',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'LN3243',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'a13e9686-1a28-4d25-bee5-04f896ee3f84',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '677 Imperial Street, Los Angeles, California, 90021, United States',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Jared',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-118.2313966,"west":-118.2323521,"north":34.0358777,"south":34.0355239},"location":{"lat":34.0357598,"lng":-118.2314174},"viewport":{"east":-118.2304939197085,"west":-118.2331918802915,"north":34.0370497802915,"south":34.0343518197085},"location_type":"ROOFTOP"},"place_id":"ChIJJfEaMiLGwoARX0Wbbov2CJ0","formatted_address":"677 Imperial St, Los Angeles, CA 90021, USA","address_components":[{"types":["street_number"],"long_name":"677","short_name":"677"},{"types":["route"],"long_name":"Imperial Street","short_name":"Imperial St"},{"types":["neighborhood","political"],"long_name":"Downtown Los Angeles","short_name":"Downtown Los Angeles"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90021","short_name":"90021"},{"types":["postal_code_suffix"],"long_name":"1320","short_name":"1320"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '6ebc9cc9-41b4-4cf1-9d49-ef2e93588b61',
      wid: '6ebc9cc9-41b4-4cf1-9d49-ef2e93588b61',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Leto',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '3e871c78-702c-4684-a9af-eb16265b089b',
  },
  {
    contactCard: {
      addressCity: 'Winnetka',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '671 Lincoln Avenue, Winnetka, Illinois, 60093, United States',
      addressState: 'Illinois',
      addressStateShort: 'IL',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Mrak',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-87.7334132,"west":-87.7337587,"north":42.1099375,"south":42.1096245},"location":{"lat":42.1097586,"lng":-87.73353709999999},"viewport":{"east":-87.7323079697085,"west":-87.73500593029152,"north":42.1110735302915,"south":42.1083755697085},"location_type":"ROOFTOP"},"place_id":"ChIJLRsVmvbED4gRvKYJ5kDLuuE","formatted_address":"671 Lincoln Ave, Winnetka, IL 60093, USA","address_components":[{"types":["street_number"],"long_name":"671","short_name":"671"},{"types":["route"],"long_name":"Lincoln Avenue","short_name":"Lincoln Ave"},{"types":["locality","political"],"long_name":"Winnetka","short_name":"Winnetka"},{"types":["administrative_area_level_3","political"],"long_name":"New Trier Township","short_name":"New Trier Township"},{"types":["administrative_area_level_2","political"],"long_name":"Cook County","short_name":"Cook County"},{"types":["administrative_area_level_1","political"],"long_name":"Illinois","short_name":"IL"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"60093","short_name":"60093"},{"types":["postal_code_suffix"],"long_name":"2345","short_name":"2345"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'b21ee7f8-2b27-4fe6-83e9-6c854f9a304d',
      wid: 'b21ee7f8-2b27-4fe6-83e9-6c854f9a304d',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Polny',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '4c0cc9cc-54b9-4732-bf49-6304cb63f61e',
  },
  {
    contactCard: {
      addressCity: 'College Station',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '400 Bizzell Street, College Station, Texas, 77843',
      addressState: 'Texas',
      addressStateShort: 'TX',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Invited',
      formOfIncorporation: null,
      geodata:
        '{"types":["establishment","point_of_interest","university"],"geometry":{"location":{"lat":30.6186806,"lng":-96.3364655},"viewport":{"east":-96.3343229197085,"west":-96.3370208802915,"north":30.6204232802915,"south":30.6177253197085},"location_type":"ROOFTOP"},"place_id":"ChIJAQDAM_eDRoYRyDvAuaS4R5c","plus_code":{"global_code":"8625JM97+FC","compound_code":"JM97+FC College Station, TX, USA"},"formatted_address":"Administration Building, 400 Bizzell St, College Station, TX 77843, USA","address_components":[{"types":["street_number"],"long_name":"400","short_name":"400"},{"types":["route"],"long_name":"Bizzell Street","short_name":"Bizzell St"},{"types":["locality","political"],"long_name":"College Station","short_name":"College Station"},{"types":["administrative_area_level_2","political"],"long_name":"Brazos County","short_name":"Brazos County"},{"types":["administrative_area_level_1","political"],"long_name":"Texas","short_name":"TX"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"77843","short_name":"77843"},{"types":["postal_code_suffix"],"long_name":"1372","short_name":"1372"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'bdaabef6-ece0-4ae6-8d3b-78fcb4be8a04',
      wid: 'bdaabef6-ece0-4ae6-8d3b-78fcb4be8a04',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Client',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '06b006c1-259e-4aa9-a05d-554450e2dbfc',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '650 South Hill Street, Los Angeles, California, 90014',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":34.0470872,"lng":-118.2539911},"viewport":{"east":-118.2525567697085,"west":-118.2552547302915,"north":34.0478494302915,"south":34.0451514697085},"location_type":"ROOFTOP"},"place_id":"ChIJYzygQbXHwoARbWt9CEuR1n8","plus_code":{"global_code":"85632PWW+RC","compound_code":"2PWW+RC Los Angeles, CA, USA"},"formatted_address":"650 S Hill St, Los Angeles, CA 90014, USA","address_components":[{"types":["street_number"],"long_name":"650","short_name":"650"},{"types":["route"],"long_name":"South Hill Street","short_name":"S Hill St"},{"types":["neighborhood","political"],"long_name":"Downtown Los Angeles","short_name":"Downtown Los Angeles"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90014","short_name":"90014"},{"types":["postal_code_suffix"],"long_name":"1783","short_name":"1783"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '1a6a63c1-02a1-434d-a944-2146ca7082ff',
      wid: '1a6a63c1-02a1-434d-a944-2146ca7082ff',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w53',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'd3e3eb0a-3995-44cb-b38c-d377cb593f7d',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1120 South Grand Avenue, Los Angeles, California, 90015',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-118.2620301,"west":-118.262975,"north":34.0408742,"south":34.0399458},"location":{"lat":34.0404585,"lng":-118.2624381},"viewport":{"east":-118.2611535697085,"west":-118.2638515302915,"north":34.0417551802915,"south":34.0390572197085},"location_type":"ROOFTOP"},"place_id":"ChIJvTqO9cjHwoARIxw506ruwCY","formatted_address":"1120 S Grand Ave, Los Angeles, CA 90015, USA","address_components":[{"types":["street_number"],"long_name":"1120","short_name":"1120"},{"types":["route"],"long_name":"South Grand Avenue","short_name":"S Grand Ave"},{"types":["neighborhood","political"],"long_name":"Downtown Los Angeles","short_name":"Downtown Los Angeles"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90015","short_name":"90015"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'ae1cba7c-cd18-419b-bdac-2ac4f20e6f34',
      wid: 'ae1cba7c-cd18-419b-bdac-2ac4f20e6f34',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w56',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'a6795ca4-75d5-4e32-aef8-fc13cc4914ec',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1414 South Grand Avenue, Los Angeles, California, 90015',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-118.2650578,"west":-118.2656686,"north":34.0366631,"south":34.0362347},"location":{"lat":34.0364757,"lng":-118.2653972},"viewport":{"east":-118.2640428697085,"west":-118.2667408302915,"north":34.0378192302915,"south":34.0351212697085},"location_type":"ROOFTOP"},"place_id":"ChIJ8xUy5MXHwoARigDo6NGHSJQ","formatted_address":"1414 S Grand Ave, Los Angeles, CA 90015, USA","address_components":[{"types":["street_number"],"long_name":"1414","short_name":"1414"},{"types":["route"],"long_name":"South Grand Avenue","short_name":"S Grand Ave"},{"types":["neighborhood","political"],"long_name":"Downtown Los Angeles","short_name":"Downtown Los Angeles"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90015","short_name":"90015"},{"types":["postal_code_suffix"],"long_name":"3067","short_name":"3067"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '15d16730-ffd3-4bb6-83f6-e85d3b78e9c4',
      wid: '15d16730-ffd3-4bb6-83f6-e85d3b78e9c4',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w10',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '7a71a4ec-8f6f-46f8-a45f-7cc83df88720',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1201 South Figueroa Street, Los Angeles, California, 90015',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-118.2681374,"west":-118.2718455,"north":34.0446984,"south":34.038309},"location":{"lat":34.0414337,"lng":-118.2689545},"viewport":{"east":-118.2678156,"west":-118.2718455,"north":34.0446984,"south":34.038309},"location_type":"ROOFTOP"},"place_id":"ChIJKabHrbjHwoARMOgj94mpgFc","formatted_address":"1201 S Figueroa St, Los Angeles, CA 90015, USA","address_components":[{"types":["street_number"],"long_name":"1201","short_name":"1201"},{"types":["route"],"long_name":"South Figueroa Street","short_name":"S Figueroa St"},{"types":["neighborhood","political"],"long_name":"Downtown Los Angeles","short_name":"Downtown Los Angeles"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90015","short_name":"90015"},{"types":["postal_code_suffix"],"long_name":"1308","short_name":"1308"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '5bcc9137-f214-49dd-86ae-4d1aae586eb0',
      wid: '5bcc9137-f214-49dd-86ae-4d1aae586eb0',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w512',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '6ed1c174-33e5-40c4-be71-ec1bcce6fb18',
  },
  {
    contactCard: {
      addressCity: 'Commerce',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '4341 East Washington Boulevard, Commerce, California, 90023',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":34.0089988,"lng":-118.180664},"viewport":{"east":-118.1796570697085,"west":-118.1823550302915,"north":34.0096572802915,"south":34.0069593197085},"location_type":"ROOFTOP"},"place_id":"ChIJhYsA9BjPwoARe4mxGK-iliY","plus_code":{"global_code":"85632R59+HP","compound_code":"2R59+HP Commerce, CA, USA"},"formatted_address":"4341 E Washington Blvd, Commerce, CA 90040, USA","address_components":[{"types":["street_number"],"long_name":"4341","short_name":"4341"},{"types":["route"],"long_name":"East Washington Boulevard","short_name":"E Washington Blvd"},{"types":["locality","political"],"long_name":"Commerce","short_name":"Commerce"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90040","short_name":"90040"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'f2cf4cf8-1bf7-4848-8596-193324309884',
      wid: 'f2cf4cf8-1bf7-4848-8596-193324309884',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w523',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '8620ebe1-279f-4cb5-b3f1-e2ed4a37c22c',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1245 North Spring Street, Los Angeles, California, 90012',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["establishment","point_of_interest","premise"],"geometry":{"bounds":{"east":-118.2345183,"west":-118.2345686,"north":34.0661595,"south":34.0660243},"location":{"lat":34.06608870000001,"lng":-118.2345424},"viewport":{"east":-118.2330247197085,"west":-118.2357226802915,"north":34.06728563029149,"south":34.06458766970849},"location_type":"ROOFTOP"},"place_id":"ChIJCcIut17GwoAR5sdlkEHrzws","formatted_address":"1245 N Spring St, Los Angeles, CA 90012, USA","address_components":[{"types":["street_number"],"long_name":"1245","short_name":"1245"},{"types":["route"],"long_name":"North Spring Street","short_name":"N Spring St"},{"types":["neighborhood","political"],"long_name":"Central LA","short_name":"Central LA"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90012","short_name":"90012"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '14c2185b-e751-457f-932d-96d7d159073b',
      wid: '14c2185b-e751-457f-932d-96d7d159073b',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w5231',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'f9994088-3c51-4eea-8e31-e9a0be4c4783',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1234 Wilshire Boulevard, Los Angeles, California, 90017',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":34.0532098,"lng":-118.2659385},"viewport":{"east":-118.2644987697085,"west":-118.2671967302915,"north":34.0546548802915,"south":34.0519569197085},"location_type":"ROOFTOP"},"place_id":"ChIJJTHVVLXHwoARKZW6VODaLrU","plus_code":{"global_code":"85633P3M+7J","compound_code":"3P3M+7J Los Angeles, CA, USA"},"formatted_address":"1234 Wilshire Blvd, Los Angeles, CA 90017, USA","address_components":[{"types":["street_number"],"long_name":"1234","short_name":"1234"},{"types":["route"],"long_name":"Wilshire Boulevard","short_name":"Wilshire Blvd"},{"types":["neighborhood","political"],"long_name":"Westlake","short_name":"Westlake"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90017","short_name":"90017"},{"types":["postal_code_suffix"],"long_name":"1970","short_name":"1970"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'e3e76a1f-3263-4a5b-8cff-303d85926f5b',
      wid: 'e3e76a1f-3263-4a5b-8cff-303d85926f5b',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w5232',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '5d986cf9-3903-4bdc-8e7f-8a0e665afa70',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1235 East Olympic Boulevard, Los Angeles, California, 90021',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":34.0329984,"lng":-118.2448928},"viewport":{"east":-118.2435570197085,"west":-118.2462549802915,"north":34.03418643029149,"south":34.0314884697085},"location_type":"ROOFTOP"},"place_id":"ChIJe20w6C7GwoARbwIf_TMD6Rk","plus_code":{"global_code":"85632QM4+52","compound_code":"2QM4+52 Los Angeles, CA, USA"},"formatted_address":"1235 E Olympic Blvd, Los Angeles, CA 90021, USA","address_components":[{"types":["street_number"],"long_name":"1235","short_name":"1235"},{"types":["route"],"long_name":"East Olympic Boulevard","short_name":"E Olympic Blvd"},{"types":["neighborhood","political"],"long_name":"Downtown Los Angeles","short_name":"Downtown Los Angeles"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90021","short_name":"90021"},{"types":["postal_code_suffix"],"long_name":"1837","short_name":"1837"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'cbff9fb4-dc29-4232-ac68-5a9edfcffd4f',
      wid: 'cbff9fb4-dc29-4232-ac68-5a9edfcffd4f',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w541',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'ff56d430-773a-43d0-95f5-8749a4fd78cc',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '3250 Wilshire Boulevard, Los Angeles, California, 90010',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-118.2930176,"west":-118.2935235,"north":34.0616345,"south":34.0605142},"location":{"lat":34.061086,"lng":-118.2933151},"viewport":{"east":-118.2918728197085,"west":-118.2945707802915,"north":34.06242333029149,"south":34.0597253697085},"location_type":"ROOFTOP"},"place_id":"ChIJiTFb-XvHwoARs5PimkzwK-g","formatted_address":"3250 Wilshire Blvd, Los Angeles, CA 90010, USA","address_components":[{"types":["street_number"],"long_name":"3250","short_name":"3250"},{"types":["route"],"long_name":"Wilshire Boulevard","short_name":"Wilshire Blvd"},{"types":["neighborhood","political"],"long_name":"Mid City","short_name":"Mid City"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90010","short_name":"90010"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '5108d8a8-b061-4b85-a90d-b60ff081b2f8',
      wid: '5108d8a8-b061-4b85-a90d-b60ff081b2f8',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w542',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '23973d21-0018-44e9-a43f-b9b1a4c8f151',
  },
  {
    contactCard: {
      addressCity: 'Commerce',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '4341 East Washington Boulevard, Commerce, California, 90023',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":34.0089988,"lng":-118.180664},"viewport":{"east":-118.1796570697085,"west":-118.1823550302915,"north":34.0096572802915,"south":34.0069593197085},"location_type":"ROOFTOP"},"place_id":"ChIJhYsA9BjPwoARe4mxGK-iliY","plus_code":{"global_code":"85632R59+HP","compound_code":"2R59+HP Commerce, CA, USA"},"formatted_address":"4341 E Washington Blvd, Commerce, CA 90040, USA","address_components":[{"types":["street_number"],"long_name":"4341","short_name":"4341"},{"types":["route"],"long_name":"East Washington Boulevard","short_name":"E Washington Blvd"},{"types":["locality","political"],"long_name":"Commerce","short_name":"Commerce"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90040","short_name":"90040"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '9b039325-271f-4b98-a74a-652896686ffa',
      wid: '9b039325-271f-4b98-a74a-652896686ffa',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w543',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'e32271e7-9acf-4ac2-a0a7-2d3d1d6cdf0b',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '123 South Figueroa Street, Los Angeles, California, 90012',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":34.0572772,"lng":-118.2526665},"viewport":{"east":-118.2512550197085,"west":-118.2539529802915,"north":34.0585857802915,"south":34.0558878197085},"location_type":"ROOFTOP"},"place_id":"ChIJXxAfzVLGwoARJebi04BrBJY","plus_code":{"global_code":"85633P4W+WW","compound_code":"3P4W+WW Los Angeles, CA, USA"},"formatted_address":"123 S Figueroa St, Los Angeles, CA 90012, USA","address_components":[{"types":["street_number"],"long_name":"123","short_name":"123"},{"types":["route"],"long_name":"South Figueroa Street","short_name":"S Figueroa St"},{"types":["neighborhood","political"],"long_name":"Downtown Los Angeles","short_name":"Downtown Los Angeles"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90012","short_name":"90012"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '9977b001-2a12-47e9-9952-e12f2577bba2',
      wid: '9977b001-2a12-47e9-9952-e12f2577bba2',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w544',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'a427154b-20bb-49ea-845d-212f61bd98cd',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '450 Bauchet Street, Los Angeles, California, 90012',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-118.2295814,"west":-118.2318282,"north":34.0594192,"south":34.0580367},"location":{"lat":34.0588042,"lng":-118.2309227},"viewport":{"east":-118.2293558197085,"west":-118.2320537802915,"north":34.0600769302915,"south":34.0573789697085},"location_type":"ROOFTOP"},"place_id":"ChIJczCkjELGwoARUykgAENmgsw","formatted_address":"450 Bauchet St, Los Angeles, CA 90012, USA","address_components":[{"types":["street_number"],"long_name":"450","short_name":"450"},{"types":["route"],"long_name":"Bauchet Street","short_name":"Bauchet St"},{"types":["neighborhood","political"],"long_name":"Central LA","short_name":"Central LA"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90012","short_name":"90012"},{"types":["postal_code_suffix"],"long_name":"2907","short_name":"2907"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '9296493a-b7ad-4b79-b327-878b71c8bc45',
      wid: '9296493a-b7ad-4b79-b327-878b71c8bc45',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w545',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Wang',
        id: '18d8e2ff-5f4e-4137-9c61-6a81e2d5a434',
        lastName: 'w545Spouse',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'e994ff3b-5b98-4614-8afb-6743e225bda1',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '123 South Figueroa Street, Los Angeles, California, 90012',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":34.0572772,"lng":-118.2526665},"viewport":{"east":-118.2512550197085,"west":-118.2539529802915,"north":34.0585857802915,"south":34.0558878197085},"location_type":"ROOFTOP"},"place_id":"ChIJXxAfzVLGwoARJebi04BrBJY","plus_code":{"global_code":"85633P4W+WW","compound_code":"3P4W+WW Los Angeles, CA, USA"},"formatted_address":"123 S Figueroa St, Los Angeles, CA 90012, USA","address_components":[{"types":["street_number"],"long_name":"123","short_name":"123"},{"types":["route"],"long_name":"South Figueroa Street","short_name":"S Figueroa St"},{"types":["neighborhood","political"],"long_name":"Downtown Los Angeles","short_name":"Downtown Los Angeles"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90012","short_name":"90012"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '799f61cb-f60d-421f-8690-23d3836f274d',
      wid: '799f61cb-f60d-421f-8690-23d3836f274d',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w547',
      legalName: null,
      maritalStatus: MaritalStatus.DomesticPartnership,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'wangSS',
        id: '1961b6b0-8921-4114-8196-c81bfab555b1',
        lastName: 'w547Spouse',
        middleName: '',
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '+1',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '625886bf-25f9-4e58-a6a5-748f232e4c4d',
  },
  {
    contactCard: {
      addressCity: 'Los Angeles',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '123 South Figueroa Street, Los Angeles, California, 90012',
      addressState: 'California',
      addressStateShort: 'CA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'wang',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":34.0572772,"lng":-118.2526665},"viewport":{"east":-118.2512550197085,"west":-118.2539529802915,"north":34.0585857802915,"south":34.0558878197085},"location_type":"ROOFTOP"},"place_id":"ChIJXxAfzVLGwoARJebi04BrBJY","plus_code":{"global_code":"85633P4W+WW","compound_code":"3P4W+WW Los Angeles, CA, USA"},"formatted_address":"123 S Figueroa St, Los Angeles, CA 90012, USA","address_components":[{"types":["street_number"],"long_name":"123","short_name":"123"},{"types":["route"],"long_name":"South Figueroa Street","short_name":"S Figueroa St"},{"types":["neighborhood","political"],"long_name":"Downtown Los Angeles","short_name":"Downtown Los Angeles"},{"types":["locality","political"],"long_name":"Los Angeles","short_name":"Los Angeles"},{"types":["administrative_area_level_2","political"],"long_name":"Los Angeles County","short_name":"Los Angeles County"},{"types":["administrative_area_level_1","political"],"long_name":"California","short_name":"CA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"90012","short_name":"90012"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'b7966919-59ea-421d-944f-e2e0e958ec41',
      wid: 'b7966919-59ea-421d-944f-e2e0e958ec41',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'w548',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'wang',
        id: 'b29a388a-dd1a-403e-91e9-eee8ff2411d9',
        lastName: 'w548spouse',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'b8147c3e-e409-4dc0-9b41-778958233985',
  },
  {
    contactCard: {
      addressCity: 'Guthrie',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1102 Dash For Cash Road, Guthrie, Texas, 79236',
      addressState: 'Texas',
      addressStateShort: 'TX',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Alex_client1001',
      formOfIncorporation: null,
      geodata:
        '{"types":["establishment","point_of_interest","veterinary_care"],"geometry":{"location":{"lat":33.6263205,"lng":-100.3374815},"viewport":{"east":-100.3362395197085,"west":-100.3389374802915,"north":33.6278326302915,"south":33.6251346697085},"location_type":"ROOFTOP"},"place_id":"ChIJKcYjCnJsVYYRausxc6W9ae8","plus_code":{"global_code":"855XJMG7+G2","compound_code":"JMG7+G2 Guthrie, TX, USA"},"formatted_address":"1102 Dash For Cash Road, Guthrie, TX 79236, USA","address_components":[{"types":["street_number"],"long_name":"1102","short_name":"1102"},{"types":["route"],"long_name":"Dash For Cash Road","short_name":"Dash For Cash Road"},{"types":["locality","political"],"long_name":"Guthrie","short_name":"Guthrie"},{"types":["administrative_area_level_2","political"],"long_name":"King County","short_name":"King County"},{"types":["administrative_area_level_1","political"],"long_name":"Texas","short_name":"TX"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"79236","short_name":"79236"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '790471fb-d833-434a-bd38-ab61f95976ea',
      wid: '790471fb-d833-434a-bd38-ab61f95976ea',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bortnik',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: '',
      nickname: '',
      notes: '',
      partner: {
        firstName: 'Alexs_wife1001',
        id: 'e9a04186-5a31-4dd1-bf75-e6f6dac5fa7b',
        lastName: 'Bortnik',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '+17647767567',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '739e73ef-2b3b-44b8-b2bd-0966c77564d8',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '42 Broadway, New York, New York, 10004',
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Visualizer AD',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-74.0122229,"west":-74.0129829,"north":40.7062522,"south":40.7057071},"location":{"lat":40.705974,"lng":-74.01261459999999},"viewport":{"east":-74.01128891970849,"west":-74.01398688029151,"north":40.7073286302915,"south":40.7046306697085},"location_type":"ROOFTOP"},"place_id":"ChIJ4QabSxFawokRqQxhvwd4qtI","formatted_address":"42 Broadway, New York, NY 10004, USA","address_components":[{"types":["street_number"],"long_name":"42","short_name":"42"},{"types":["route"],"long_name":"Broadway","short_name":"Broadway"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10004","short_name":"10004"},{"types":["postal_code_suffix"],"long_name":"1617","short_name":"1617"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '09c09275-a53e-46b5-8786-fa554de05dff',
      wid: '09c09275-a53e-46b5-8786-fa554de05dff',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Testing Account',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'a',
        id: 'b5123354-6aac-4ff3-8a55-87b24a148f3e',
        lastName: 'b',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '01ea9ce9-bd90-4368-92ca-a439721fbf70',
  },
  {
    contactCard: {
      addressCity: 'Phoenix',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1625 E Berridge Lane, Phoenix, Arizona, 12345',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Test_Pending',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-112.0462704,"west":-112.0465622,"north":33.5254577,"south":33.5253352},"location":{"lat":33.5253965,"lng":-112.0464028},"viewport":{"east":-112.0450673197085,"west":-112.0477652802915,"north":33.52678068029149,"south":33.5240827197085},"location_type":"ROOFTOP"},"place_id":"ChIJiXH2xlcNK4cRirAhQ2ktk94","formatted_address":"1625 E Berridge Ln, Phoenix, AZ 85016, USA","address_components":[{"types":["street_number"],"long_name":"1625","short_name":"1625"},{"types":["route"],"long_name":"East Berridge Lane","short_name":"E Berridge Ln"},{"types":["neighborhood","political"],"long_name":"Madison Village","short_name":"Madison Village"},{"types":["locality","political"],"long_name":"Phoenix","short_name":"Phoenix"},{"types":["administrative_area_level_2","political"],"long_name":"Maricopa County","short_name":"Maricopa County"},{"types":["administrative_area_level_1","political"],"long_name":"Arizona","short_name":"AZ"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"85016","short_name":"85016"},{"types":["postal_code_suffix"],"long_name":"1813","short_name":"1813"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '82231c39-87a4-477c-b0c0-b0fa636abfe8',
      wid: '82231c39-87a4-477c-b0c0-b0fa636abfe8',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'TestP',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'e40395e1-b29f-44b7-b5a5-e0e46c489479',
  },
  {
    contactCard: {
      addressCity: 'Phoenix',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1625 E Berridge Lane, Phoenix, Arizona, 12345',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Alex_test5',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-112.0462704,"west":-112.0465622,"north":33.5254577,"south":33.5253352},"location":{"lat":33.5253965,"lng":-112.0464028},"viewport":{"east":-112.0450673197085,"west":-112.0477652802915,"north":33.52678068029149,"south":33.5240827197085},"location_type":"ROOFTOP"},"place_id":"ChIJiXH2xlcNK4cRirAhQ2ktk94","formatted_address":"1625 E Berridge Ln, Phoenix, AZ 85016, USA","address_components":[{"types":["street_number"],"long_name":"1625","short_name":"1625"},{"types":["route"],"long_name":"East Berridge Lane","short_name":"E Berridge Ln"},{"types":["neighborhood","political"],"long_name":"Madison Village","short_name":"Madison Village"},{"types":["locality","political"],"long_name":"Phoenix","short_name":"Phoenix"},{"types":["administrative_area_level_2","political"],"long_name":"Maricopa County","short_name":"Maricopa County"},{"types":["administrative_area_level_1","political"],"long_name":"Arizona","short_name":"AZ"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"85016","short_name":"85016"},{"types":["postal_code_suffix"],"long_name":"1813","short_name":"1813"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '5f24b321-4ea9-414f-9fa2-1f17b51e4422',
      wid: '5f24b321-4ea9-414f-9fa2-1f17b51e4422',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bortnik_test5',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'd9eed814-88f8-4ead-8548-ea30dc45ca6d',
  },
  {
    contactCard: {
      addressCity: 'Phoenix',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '1625 E Berridge Lane, Phoenix, Arizona, 12345',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Percival',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-112.0462704,"west":-112.0465622,"north":33.5254577,"south":33.5253352},"location":{"lat":33.5253965,"lng":-112.0464028},"viewport":{"east":-112.0450673197085,"west":-112.0477652802915,"north":33.52678068029149,"south":33.5240827197085},"location_type":"ROOFTOP"},"place_id":"ChIJiXH2xlcNK4cRirAhQ2ktk94","formatted_address":"1625 E Berridge Ln, Phoenix, AZ 85016, USA","address_components":[{"types":["street_number"],"long_name":"1625","short_name":"1625"},{"types":["route"],"long_name":"East Berridge Lane","short_name":"E Berridge Ln"},{"types":["neighborhood","political"],"long_name":"Madison Village","short_name":"Madison Village"},{"types":["locality","political"],"long_name":"Phoenix","short_name":"Phoenix"},{"types":["administrative_area_level_2","political"],"long_name":"Maricopa County","short_name":"Maricopa County"},{"types":["administrative_area_level_1","political"],"long_name":"Arizona","short_name":"AZ"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"85016","short_name":"85016"},{"types":["postal_code_suffix"],"long_name":"1813","short_name":"1813"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '8fd44b74-b9e1-4c5e-8df2-86ea6ac5f068',
      wid: '8fd44b74-b9e1-4c5e-8df2-86ea6ac5f068',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Worthington',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'be5239a0-a3b2-4ac8-aebd-b992b7bcf8e9',
  },
  {
    contactCard: {
      addressCity: 'Phoenix',
      addressCountry: null,
      addressCountryShort: null,
      addressCounty: null,
      addressFormatted: '1625 E Berridge Lane, Phoenix, Arizona, 12345',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Valentino',
      formOfIncorporation: null,
      geodata: '{}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'cd0a1922-cf42-426c-8300-5cbea192c45b',
      wid: 'cd0a1922-cf42-426c-8300-5cbea192c45b',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Rossi',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'dcd404b3-683a-48d9-9875-cd1002a2369d',
  },
  {
    contactCard: {
      addressCity: 'Phoenix',
      addressCountry: null,
      addressCountryShort: null,
      addressCounty: null,
      addressFormatted: '1625 E Berridge Lane, Phoenix, Arizona, 12345',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Kerbal',
      formOfIncorporation: null,
      geodata: '{}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'cdc61b64-9452-4674-a097-fa5982a51181',
      wid: 'cdc61b64-9452-4674-a097-fa5982a51181',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Space',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '0ca3a748-d9b5-42e4-9807-eb69c313665a',
  },
  {
    contactCard: {
      addressCity: 'Phoenix',
      addressCountry: null,
      addressCountryShort: null,
      addressCounty: null,
      addressFormatted: '1625 E Berridge Lane, Phoenix, Arizona, 12345',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Mariko',
      formOfIncorporation: null,
      geodata: '{}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'ec933521-3068-40cd-8470-7ffcb1280b25',
      wid: 'ec933521-3068-40cd-8470-7ffcb1280b25',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Yamamoto',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '2c5db94d-9e4c-426c-b772-1d562ce8b345',
  },
  {
    contactCard: {
      addressCity: 'Honolulu',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '444 Niu Street, Honolulu, Hawaii, 96815',
      addressState: 'Hawaii',
      addressStateShort: 'HI',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'A1',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-157.8322404,"west":-157.8330296,"north":21.2873586,"south":21.2865182},"location":{"lat":21.2869649,"lng":-157.8327139},"viewport":{"east":-157.8312860197085,"west":-157.8339839802915,"north":21.2882873802915,"south":21.2855894197085},"location_type":"ROOFTOP"},"place_id":"ChIJPQm8sYxtAHwRwkH9zx3KA9w","formatted_address":"444 Niu St, Honolulu, HI 96815, USA","address_components":[{"types":["street_number"],"long_name":"444","short_name":"444"},{"types":["route"],"long_name":"Niu Street","short_name":"Niu St"},{"types":["locality","political"],"long_name":"Honolulu","short_name":"Honolulu"},{"types":["administrative_area_level_2","political"],"long_name":"Honolulu County","short_name":"Honolulu County"},{"types":["administrative_area_level_1","political"],"long_name":"Hawaii","short_name":"HI"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"96815","short_name":"96815"},{"types":["postal_code_suffix"],"long_name":"1830","short_name":"1830"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '2e8bdd39-a8e1-416c-9e2f-fd0988e19950',
      wid: '2e8bdd39-a8e1-416c-9e2f-fd0988e19950',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'B1',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'da0d0fb9-96c4-4af7-8abf-8067fa9ea864',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '345 East 24th Street, New York, New York, 10010',
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'testpending',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":40.7379393,"lng":-73.9783254},"viewport":{"east":-73.9770512197085,"west":-73.97974918029152,"north":40.7391885802915,"south":40.7364906197085},"location_type":"ROOFTOP"},"place_id":"ChIJTfEpmAtZwokRAgk4KoiBr4g","plus_code":{"global_code":"87G8P2QC+5M","compound_code":"P2QC+5M New York, NY, USA"},"formatted_address":"345 E 24th St, New York, NY 10010, USA","address_components":[{"types":["street_number"],"long_name":"345","short_name":"345"},{"types":["route"],"long_name":"East 24th Street","short_name":"E 24th St"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10010","short_name":"10010"},{"types":["postal_code_suffix"],"long_name":"4020","short_name":"4020"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '1c77da90-5d71-4c8d-aae3-99650b1f32cc',
      wid: '1c77da90-5d71-4c8d-aae3-99650b1f32cc',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'testpending',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: '+16345123411',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '526003d9-e9b6-4cca-a4fb-456b964d48cb',
  },
  {
    contactCard: {
      addressCity: 'New York',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '432 Park Avenue, New York, New York, 10022',
      addressState: 'New York',
      addressStateShort: 'NY',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'abc',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-73.9716372,"west":-73.9720913,"north":40.7617864,"south":40.7614429},"location":{"lat":40.761648,"lng":-73.97176019999999},"viewport":{"east":-73.97022906970851,"west":-73.97292703029152,"north":40.7629217302915,"south":40.7602237697085},"location_type":"ROOFTOP"},"place_id":"ChIJG7FMzvpYwokR3MH0rrXlNKc","formatted_address":"432 Park Ave, New York, NY 10022, USA","address_components":[{"types":["street_number"],"long_name":"432","short_name":"432"},{"types":["route"],"long_name":"Park Avenue","short_name":"Park Ave"},{"types":["political","sublocality","sublocality_level_1"],"long_name":"Manhattan","short_name":"Manhattan"},{"types":["locality","political"],"long_name":"New York","short_name":"New York"},{"types":["administrative_area_level_2","political"],"long_name":"New York County","short_name":"New York County"},{"types":["administrative_area_level_1","political"],"long_name":"New York","short_name":"NY"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"10022","short_name":"10022"},{"types":["postal_code_suffix"],"long_name":"3534","short_name":"3534"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'f5c29d42-097e-4dae-bb9f-72bb0e8908ea',
      wid: 'f5c29d42-097e-4dae-bb9f-72bb0e8908ea',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'cde',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Fake',
        id: 'ba1d3526-f78d-4e4a-b078-f2c38c034e87',
        lastName: 'WIFE',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '7af2f82f-5b50-4373-8f41-50d06e0e4f21',
  },
  {
    contactCard: {
      addressCity: 'Austin',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '2433 Ridgepoint Drive, Austin, Texas, 78754',
      addressState: 'Texas',
      addressStateShort: 'TX',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Yana',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":30.3276059,"lng":-97.6736508},"viewport":{"east":-97.67234066970848,"west":-97.675***********,"north":30.3289516802915,"south":30.3262537197085},"location_type":"ROOFTOP"},"place_id":"ChIJrWqONKrJRIYRKIVkSvtOkVk","plus_code":{"global_code":"862488HG+2G","compound_code":"88HG+2G Austin, TX, USA"},"formatted_address":"2433 Ridgepoint Dr, Austin, TX 78754, USA","address_components":[{"types":["street_number"],"long_name":"2433","short_name":"2433"},{"types":["route"],"long_name":"Ridgepoint Drive","short_name":"Ridgepoint Dr"},{"types":["neighborhood","political"],"long_name":"Walnut Creek Business Park","short_name":"Walnut Creek Business Park"},{"types":["locality","political"],"long_name":"Austin","short_name":"Austin"},{"types":["administrative_area_level_2","political"],"long_name":"Travis County","short_name":"Travis County"},{"types":["administrative_area_level_1","political"],"long_name":"Texas","short_name":"TX"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"78754","short_name":"78754"},{"types":["postal_code_suffix"],"long_name":"5226","short_name":"5226"}]}',
      hasChildren: true,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'a430de52-9f00-4f2c-9571-0f52f90b76ae',
      wid: 'a430de52-9f00-4f2c-9571-0f52f90b76ae',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Colon',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Fake',
        id: '3a22a72d-2dfc-4496-af53-93e7949a5150',
        lastName: 'name',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: '+17874345345345',
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '11d0041e-9daf-4163-bfd4-c401f8224f1b',
  },
  {
    contactCard: {
      addressCity: 'Honolulu',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '444 Niu Street, Honolulu, Hawaii, 96815',
      addressState: 'Hawaii',
      addressStateShort: 'HI',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Al6',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-157.8322404,"west":-157.8330296,"north":21.2873586,"south":21.2865182},"location":{"lat":21.2869649,"lng":-157.8327139},"viewport":{"east":-157.8312860197085,"west":-157.8339839802915,"north":21.2882873802915,"south":21.2855894197085},"location_type":"ROOFTOP"},"place_id":"ChIJPQm8sYxtAHwRwkH9zx3KA9w","formatted_address":"444 Niu St, Honolulu, HI 96815, USA","address_components":[{"types":["street_number"],"long_name":"444","short_name":"444"},{"types":["route"],"long_name":"Niu Street","short_name":"Niu St"},{"types":["locality","political"],"long_name":"Honolulu","short_name":"Honolulu"},{"types":["administrative_area_level_2","political"],"long_name":"Honolulu County","short_name":"Honolulu County"},{"types":["administrative_area_level_1","political"],"long_name":"Hawaii","short_name":"HI"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"96815","short_name":"96815"},{"types":["postal_code_suffix"],"long_name":"1830","short_name":"1830"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '49ed8cc8-4ee8-4119-a314-b4649c35df6d',
      wid: '49ed8cc8-4ee8-4119-a314-b4649c35df6d',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Br6',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'fc0c019a-967d-42f6-b4c8-db7b435f62d3',
  },
  {
    contactCard: {
      addressCity: 'Union City',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '6400 Buffington Road, Union City, Georgia, 30291',
      addressState: 'Georgia',
      addressStateShort: 'GA',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'AL_floppy',
      formOfIncorporation: null,
      geodata:
        '{"types":["street_address"],"geometry":{"location":{"lat":33.5798882,"lng":-84.50865639999999},"viewport":{"east":-84.50786356970848,"west":-84.5105615302915,"north":33.5821253802915,"south":33.5794274197085},"location_type":"ROOFTOP"},"place_id":"ChIJKaypY87l9IgRLxOH9G1hdVY","plus_code":{"global_code":"865QHFHR+XG","compound_code":"HFHR+XG Union City, GA, USA"},"formatted_address":"6400 Buffington Rd, Union City, GA 30291, USA","address_components":[{"types":["street_number"],"long_name":"6400","short_name":"6400"},{"types":["route"],"long_name":"Buffington Road","short_name":"Buffington Rd"},{"types":["locality","political"],"long_name":"Union City","short_name":"Union City"},{"types":["administrative_area_level_2","political"],"long_name":"Fulton County","short_name":"Fulton County"},{"types":["administrative_area_level_1","political"],"long_name":"Georgia","short_name":"GA"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"30291","short_name":"30291"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '7e2a1e13-89e3-4533-a6c6-973feccb6bd1',
      wid: '7e2a1e13-89e3-4533-a6c6-973feccb6bd1',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bortnik',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '4b05d459-c898-40c5-b4b4-f9c81e2180da',
  },
  {
    contactCard: {
      addressCity: 'Phoenix',
      addressCountry: null,
      addressCountryShort: null,
      addressCounty: null,
      addressFormatted: '1625 E Berridge Lane, Phoenix, Arizona, 12345',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Al_t1',
      formOfIncorporation: null,
      geodata: '{}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '34c95c94-0f06-40cf-bdec-99d9a2770220',
      wid: '34c95c94-0f06-40cf-bdec-99d9a2770220',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bortnik',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: '9a165a70-6adf-4201-818f-77c6080cd710',
  },
  {
    contactCard: {
      addressCity: 'Phoenix',
      addressCountry: null,
      addressCountryShort: null,
      addressCounty: null,
      addressFormatted: '1625 E Berridge Lane, Phoenix, Arizona, 12345',
      addressState: 'Arizona',
      addressStateShort: 'AZ',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Mur',
      formOfIncorporation: null,
      geodata: '{}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: 'c6de45d0-d90f-4b57-9710-0a0fd04c25b1',
      wid: 'c6de45d0-d90f-4b57-9710-0a0fd04c25b1',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Bur',
      legalName: null,
      maritalStatus: MaritalStatus.Single,
      middleName: null,
      nickname: null,
      notes: null,
      partner: null,
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'ea44fd9b-9cf5-4fdf-ad61-df4d552e0cc3',
  },
  {
    contactCard: {
      addressCity: 'Shawnee',
      addressCountry: null,
      addressCountryShort: 'US',
      addressCounty: null,
      addressFormatted: '4009 North Kickapoo Avenue, Shawnee, Oklahoma, 74804',
      addressState: 'Oklahoma',
      addressStateShort: 'OK',
      addressStreetName: null,
      addressStreetNumber: null,
      addressZipCode: null,
      aliases: [],
      children: null,
      citizenship: null,
      dob: null,
      ein: null,
      email: '<EMAIL>',
      firstName: 'Adam',
      formOfIncorporation: null,
      geodata:
        '{"types":["premise"],"geometry":{"bounds":{"east":-96.9282089,"west":-96.9283609,"north":35.3732278,"south":35.3729579},"location":{"lat":35.3730862,"lng":-96.9282787},"viewport":{"east":-96.92689206970849,"west":-96.9295900302915,"north":35.3745255802915,"south":35.3718276197085},"location_type":"ROOFTOP"},"place_id":"ChIJb3_ieQTIs4cRt6NrphJ3fKA","formatted_address":"4009 N Kickapoo Ave, Shawnee, OK 74804, USA","address_components":[{"types":["street_number"],"long_name":"4009","short_name":"4009"},{"types":["route"],"long_name":"North Kickapoo Avenue","short_name":"N Kickapoo Ave"},{"types":["locality","political"],"long_name":"Shawnee","short_name":"Shawnee"},{"types":["administrative_area_level_2","political"],"long_name":"Pottawatomie County","short_name":"Pottawatomie County"},{"types":["administrative_area_level_1","political"],"long_name":"Oklahoma","short_name":"OK"},{"types":["country","political"],"long_name":"United States","short_name":"US"},{"types":["postal_code"],"long_name":"74804","short_name":"74804"},{"types":["postal_code_suffix"],"long_name":"1623","short_name":"1623"}]}',
      hasChildren: false,
      hasFamilyMemberWithSpecialNeeds: false,
      hasPets: false,
      id: '9ebe5d3d-31cc-4499-a437-e3a2cd5405ab',
      wid: '9ebe5d3d-31cc-4499-a437-e3a2cd5405ab',
      isDeceased: false,
      isDonorAdvisedFund: null,
      isPrimary: true,
      lastName: 'Wolf',
      legalName: null,
      maritalStatus: MaritalStatus.Married,
      middleName: null,
      nickname: null,
      notes: null,
      partner: {
        firstName: 'Kathleen',
        id: 'e777b8ac-621a-4826-8c08-0c46985a598b',
        lastName: 'Wolf',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      phoneNumber: null,
      relationship: null,
      tin: null,
      trustCreationDate: null,
      trustType: null,
      type: ContactCardType.Individual,
      isLegalNameConfirmed: true,
      __typename: 'ContactCard' as const,
    },
    id: 'f9398f0c-bd72-4d85-9b18-baf9f4394acc',
  },
];
