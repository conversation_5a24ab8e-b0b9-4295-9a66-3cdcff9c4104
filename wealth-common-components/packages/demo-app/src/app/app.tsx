import { BrowserRouter, Routes, Route, Outlet } from 'react-router-dom';
import Index from '../pages/Index';
import { demos } from '../demos';

export default function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Outlet />}>
          <Route index element={<Index />} />
          {demos.map((d, i) => (
            <Route key={i} path={d.path} element={d.page()} />
          ))}
        </Route>
      </Routes>
    </BrowserRouter>
  );
}
