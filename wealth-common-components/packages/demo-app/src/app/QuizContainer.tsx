import { Quiz } from '@wealthcom/common-quiz';
import { useState } from 'react';
import Chat from './ChatComponent';
import { FaqPanel } from './FaqPanel';

const onQuizRepeat = () => {
  console.log('repeat');
};

const onQuizComplete = (recommendedPlan: string, recommendedPlanType: string, quizAnswers: (string | null)[]) => {
  console.log('Quiz has been completed');
  console.log(recommendedPlan);
  console.log(recommendedPlanType);
  console.log(quizAnswers);
};

const onChoosePlan = () => {
  console.log('On Choose Plan');
};

const onShowAdditionalPlans = () => {
  console.log('Show Additional Plans');
};

export function QuizContainer() {
  const [openQuiz, setOpenQuiz] = useState(false);
  const [faqOn, setFaqOn] = useState(false);
  const [panelId, setPanelId] = useState('recommendation-quiz-click-all-that-apply-to-you');

  return (
    <>
      <button onClick={() => setOpenQuiz(true)}> Quiz </button>

      <Quiz
        skipAddressCheck={true}
        isOpen={openQuiz}
        onClose={() => setOpenQuiz(false)}
        onQuizRepeat={onQuizRepeat}
        onQuizComplete={onQuizComplete}
        onChoosePlan={onChoosePlan}
        onSeeMorePlans={onShowAdditionalPlans}
        faqProps={{ faqOn, setFaqOn, component: <FaqPanel /> }}
        ChatComponent={<Chat />}
        userAddress="New York"
        maritalPreselect={true}
        netWorthPreselect={true}
        hasEstatePlan={true}
        setPanelId={setPanelId}
        config={{
          apiPublicUrl: 'https://pj5vmm437bcn7crxtcsstgz3ri.appsync-api.us-west-2.amazonaws.com/graphql',
          googleMapsApiKey: 'AIzaSyCowJDcftiRenFF00lhikXfwsavZc19qQs',
        }}
      />
    </>
  );
}

export default QuizContainer;
