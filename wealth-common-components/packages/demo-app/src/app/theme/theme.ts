import palette from './palette';
import { SimplePaletteColorOptions, Theme, ThemeOptions } from '@mui/material/styles';

declare module '@mui/material/styles' {
  interface BreakpointOverrides {
    zero: true;
    inf: true;
  }
}

declare module '@mui/material/styles' {
  interface TypographyVariants {
    body3: React.CSSProperties;
    subtitle3: React.CSSProperties;
    chart?: {
      innerDonutFont?: string;
      labelFont?: string;
    };
  }

  // allow configuration using `createTheme`
  interface TypographyVariantsOptions {
    body3?: React.CSSProperties;
    subtitle3?: React.CSSProperties;
    chart?: {
      innerDonutFont?: string;
      labelFont?: string;
    };
  }
}
declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    body3: true;
    subtitle3: true;
  }
}

const clayPalette = {
  background: { default: '#fff', neutral: '#F5F3ED' },
  border: { disabled: '#ECEEF3' },
  divider: '#DEE3EC',
  text: { primary: '#1A1B1C', secondary: '#696C71', disabled: '#9B9B9B' },
  primary: {
    ...palette.light.primary,
    lighter: '#C2E8E5',
    light: '#75DBD3',
    main: '#00857C',
    dark: '#00534F',
    darker: '#00655E',
  },
  secondary: {
    ...palette.light.secondary,
    light: '#F5F3ED',
    main: '#E3E2E1',
  },
  success: {
    ...palette.light.success,
    light: '#75DB9E',
    main: '#75DBD3',
    darker: '#00534F',
    dark: '#36A199',
  },
  error: { ...palette.light.error, main: '#FB3E24', dark: '#D90B1C' },
  warning: { ...palette.light.warning, main: '#FCBD1E', light: '#EFFEB2' },
  info: {
    ...palette.light.info,
    main: '#696C71',
    light: '#455BA9',
    lighter: '#75AADB',
  },
  action: {
    ...palette.light.action,
    selected: '#000000',
    hover: '#DCF4F2',
    disabledBackground: '#eceef3',
    disabled: '#9B9B9B',
  },
  categoryIcon: 'red',
  deleteIcon: {
    primary: '#84878E',
    secondary: '#D90B1C',
  },
  grey: {
    100: '#DEE3EC',
    200: '#F4F5F8',
    300: '#cccccc',
    400: '#84878E',
    500: '#e3e2e1', // Secondary dark beige
    600: '#E2EAF2',
    700: '#000000',
  },
  green: {
    50: '#00534F',
  },
  common: { black: '#000000', white: '#ffffff' },
};

const defaultBackground = { background: clayPalette.background?.default };

// This value matches the @font-face declaration for Neue Medium in fonts/index.css.
const DEFAULT_FONT_WEIGHT = 400;
const MEDIUM_FONT_WEIGHT = 500;

// Neue Haas W1G
const DEFAULT_FONT_STACK = 'Neue Haas Unica W1G, Neue Haas Unica Pro, Inter, Lato, sans-serif';
const MEDIUM_FONT_STACK = 'Neue Haas Unica W1G Medium, Neue Haas Unica Pro Medium, Inter, Lato, sans-serif';

const clayTheme: ThemeOptions = {
  breakpoints: {
    values: {
      zero: 0,
      xs: 360,
      sm: 719,
      md: 1024,
      lg: 1279,
      xl: 1440,
      inf: 1920,
    },
  },
  palette: clayPalette as never,
  shape: {
    borderRadius: 8,
  },
  typography: {
    fontFamily: DEFAULT_FONT_STACK,
    fontWeightRegular: DEFAULT_FONT_WEIGHT,
    fontWeightMedium: MEDIUM_FONT_WEIGHT,
    // TODO: Do we need a bold variant?
    fontWeightBold: 900,
    h1: {
      fontFamily: 'Reckless',
      fontSize: 48,
      lineHeight: '58px',
    },
    // Display Medium
    h2: {
      fontSize: 44,
      fontFamily: MEDIUM_FONT_STACK,
      fontWeight: MEDIUM_FONT_WEIGHT,
      letterSpacing: '-0.01em',
      lineHeight: '52px',
    },
    // Headline Large
    h3: {
      fontSize: 34,
      fontFamily: MEDIUM_FONT_STACK,
      fontWeight: MEDIUM_FONT_WEIGHT,
      letterSpacing: '-0.005em',
      lineHeight: '40px',
    },
    // Headline Medium
    h4: {
      fontSize: 24,
      fontFamily: MEDIUM_FONT_STACK,
      fontWeight: MEDIUM_FONT_WEIGHT,
      lineHeight: '30px',
    },
    // Headline Small
    h5: {
      fontSize: 20,
      fontFamily: MEDIUM_FONT_STACK,
      fontWeight: MEDIUM_FONT_WEIGHT,
      lineHeight: '24px',
    },
    h6: {
      fontFamily: MEDIUM_FONT_STACK,
      fontSize: 16,
      fontWeight: MEDIUM_FONT_WEIGHT,
      lineHeight: '20px',
    },
    // Label Large
    subtitle1: {
      fontFamily: DEFAULT_FONT_STACK,
      fontSize: 16,
      fontWeight: MEDIUM_FONT_WEIGHT,
      letterSpacing: '.01em',
      lineHeight: '22px',
    },
    // Label Medium
    subtitle2: {
      fontFamily: DEFAULT_FONT_STACK,
      fontSize: 14,
      fontWeight: MEDIUM_FONT_WEIGHT,
      letterSpacing: '.01em',
      lineHeight: '18px',
    },
    // Label Small
    subtitle3: {
      fontFamily: DEFAULT_FONT_STACK,
      fontSize: 13,
      lineHeight: '16px',
    },
    // Body Large
    body1: {
      fontFamily: DEFAULT_FONT_STACK,
      fontSize: 20,
      lineHeight: '28px',
    },
    // Body Medium
    body2: {
      fontFamily: DEFAULT_FONT_STACK,
      fontSize: 16,
      lineHeight: '22px',
    },
    // Body Small
    body3: {
      fontFamily: DEFAULT_FONT_STACK,
      fontSize: 14,
      lineHeight: '20px',
    },
    button: {
      fontSize: 14,
      fontWeight: 500,
      lineHeight: '18px',
      letterSpacing: '-.01em',
      textTransform: 'none',
    },
  },
  components: {
    MuiButton: {
      defaultProps: {
        disableElevation: true,
      },
      styleOverrides: {
        root: {
          borderRadius: '8px',
        },
        sizeMedium: {
          height: 40,
        },
        sizeLarge: {
          height: 48,
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          '&.Mui-disabled': {
            color: clayPalette.text?.secondary,
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          ...defaultBackground,
          borderRadius: 8,
          '& .MuiInputBase-root': {
            padding: 0,
          },
          '& .MuiInputBase-input': {
            padding: '12px',
          },
          '&.Mui-disabled': {
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: clayPalette.grey?.[300],
            },
          },
          '& .MuiOutlinedInput-input': {
            fontSize: 16,
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: clayPalette.grey?.[300],
          },
          '&.Mui-error': {
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: '#c4c4c4',
            },
          },
        },
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          marginLeft: '0',
        },
      },
    },
    MuiTooltip: {
      defaultProps: {
        arrow: true,
        placement: 'right-start',
      },
      styleOverrides: {
        arrow: {
          marginTop: '10px',
        },
        tooltip: {
          backgroundColor: (clayPalette.primary as SimplePaletteColorOptions)?.dark,
          padding: '16px',
          fontSize: '14px',
          borderRadius: '12px',
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        colorPrimary: {
          '&': {
            color: clayPalette.text?.primary,
          },
          '&.Mui-disabled': {
            color: clayPalette.text?.disabled,
          },
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          '&.Mui-selected': {
            color: (clayPalette.primary as SimplePaletteColorOptions)?.main,
          },
        },
      },
    },
    MuiAccordion: {
      styleOverrides: {
        root: {
          '&.MuiAccordion-rounded': {
            borderRadius: '12px',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          '&.MuiPaper-rounded': {
            borderRadius: '12px',
          },
        },
      },
    },
  },
};

/** Overrides a theme's first 4 shadows with the clay Elevation look.  */
export function overrideShadows({ shadows }: Theme) {
  shadows[1] = '0px 1px 5px 1px rgba(64, 65, 69, 0.07), 0px 1px 2px rgba(108, 109, 114, 0.11), 0px 3px 7px -1px rgba(66, 67, 69, 0.1)';
  shadows[2] = '0px 4px 12px rgba(74, 76, 80, 0.08), 0px 2px 4px rgba(102, 105, 110, 0.05)';
  shadows[3] = '0px 10px 30px 5px rgba(64, 65, 69, 0.05), 0px 10px 15px rgba(64, 65, 69, 0.06)';
  shadows[4] = '0px 6px 32px rgba(64, 65, 69, 0.15)';
}

export default clayTheme;
