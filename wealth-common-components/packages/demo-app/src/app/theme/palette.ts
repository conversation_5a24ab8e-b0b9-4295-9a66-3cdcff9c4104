import { alpha } from '@mui/material/styles';

// ----------------------------------------------------------------------

function createGradient(color1: string, color2: string) {
  return `linear-gradient(to bottom, ${color1}, ${color2})`;
}

interface GradientsPaletteOptions {
  primary: string;
  info: string;
  success: string;
  warning: string;
  error: string;
}

declare module '@mui/material/styles/createPalette' {
  interface TypeBackground {
    neutral: string;
  }
  interface SimplePaletteColorOptions {
    lighter: string;
    darker: string;
  }
  interface PaletteColor {
    lighter: string;
    darker: string;
  }
  interface Palette {
    gradients: GradientsPaletteOptions;
    coverPage?: {
      leftBackground?: string;
      rightBackground?: string;
      arrows: {
        primary?: string;
        secondary?: string;
        accent?: string;
      };
    };
    commonPage?: {
      primaryBackground?: string;
      neutralBackground?: string;
    };
    chart?: {
      primary?: string;
      secondary?: string;
    };
    footer?: {
      background?: string;
      color?: string;
    };
    icons?: {
      fill?: string;
      border?: string;
      background?: string;
    };
  }
  interface PaletteOptions {
    gradients?: GradientsPaletteOptions;
    coverPage?: {
      leftBackground?: string;
      rightBackground?: string;
      arrows: {
        primary?: string;
        secondary?: string;
        accent?: string;
      };
    };
    commonPage?: {
      primaryBackground?: string;
      neutralBackground?: string;
    };
    chart?: {
      primary?: string;
      secondary?: string;
    };
    footer?: {
      background?: string;
      color?: string;
    };
    icons?: {
      fill?: string;
      border?: string;
      background?: string;
    };
  }

  interface Theme {
    palette: Palette;
  }
  // allow configuration using `createTheme`
  interface ThemeOptions {
    palette: PaletteOptions;
  }
}

declare module '@mui/material' {
  interface Color {
    0: string;
    150: string;
    175: string;
    225: string;
    250: string;
    260: string;
    275: string;
    325: string;
    350: string;
    375: string;
    425: string;
    450: string;
    475: string;
    525: string;
    530: string;
    550: string;
    750: string;
    500_8: string;
    500_12: string;
    500_16: string;
    500_24: string;
    500_32: string;
    500_48: string;
    500_56: string;
    500_80: string;
  }

  interface Palette {
    blue: Color;
    green: Color;
  }
}

// SETUP COLORS
const BLUE = {
  600: '#3F7FE0',
  700: '#4C79C8',
  750: '#485B70',
  900: '#9DBCCB',
};
// These green values are placeholders
const GREEN = {
  600: '#39D477',
  700: '#2BC3BC',
};
const PRIMARY = {
  lighter: '#DFE3E8',
  light: '#C4CDD5',
  main: '#454F5B',
  dark: '#637381',
  darker: '#454F5B',
};
const SECONDARY = {
  lighter: '#D6E4FF',
  light: '#84A9FF',
  main: BLUE[600],
  dark: BLUE[600],
  darker: '#091A7A',
};
const INFO = {
  lighter: '#D0F2FF',
  light: '#74CAFF',
  main: '#1890FF',
  dark: '#0C53B7',
  darker: '#04297A',
};
const SUCCESS = {
  lighter: '#E9FCD4',
  light: '#AAF27F',
  main: '#54D62C',
  dark: '#229A16',
  darker: '#08660D',
};
const WARNING = {
  lighter: '#FFF7CD',
  light: '#FFE16A',
  main: '#FFC107',
  dark: '#B78103',
  darker: '#7A4F01',
};
const ERROR = {
  main: '#FB3E24',
  darker: '#C90515',
  dark: '#D90B1C',
  light: '#FFF0F0',
  lighter: '#E35030',
};
const GREY = {
  0: '#FFFFFF',
  100: '#F9FAFB',
  150: '#F9F9F9',
  175: '#F6F6F6',
  200: '#F4F6F8',
  225: '#F0F0F0',
  250: '#EDEDED',
  260: '#E3E3E3',
  275: '#E0E0E0',
  300: '#DFE3E8',
  325: '#DCDCDC',
  350: '#CECECE',
  375: '#CACACA',
  400: '#C4CDD5',
  425: '#B7B7B7',
  450: '#B4B4B4',
  475: '#999999',
  500: '#919EAB',
  525: '#8F8F8F',
  530: '#707070',
  550: '#666666',
  600: '#637381',
  650: '#5C5C5C',
  700: '#454F5B',
  750: '#3E3E3E',
  800: '#212B36',
  900: '#161C24',
  500_8: alpha('#919EAB', 0.08),
  500_12: alpha('#919EAB', 0.12),
  500_16: alpha('#919EAB', 0.16),
  500_24: alpha('#919EAB', 0.24),
  500_32: alpha('#919EAB', 0.32),
  500_48: alpha('#919EAB', 0.48),
  500_56: alpha('#919EAB', 0.56),
  500_80: alpha('#919EAB', 0.8),
};
const GRADIENTS = {
  primary: createGradient(PRIMARY.light, PRIMARY.main),
  info: createGradient(INFO.light, INFO.main),
  success: createGradient(SUCCESS.light, SUCCESS.main),
  warning: createGradient(WARNING.light, WARNING.main),
  error: createGradient(ERROR.light, ERROR.main),
};
const BUTTON_BACKCOLORS = {
  primary: '#478ACC',
  default: '#F1F4F8',
  gray: '#E6E6E6',
  black: '#2C323F',
};
const COMMON = {
  common: { black: '#000', white: '#fff' },
  primary: { ...PRIMARY, contrastText: '#fff' },
  secondary: { ...SECONDARY, contrastText: '#fff' },
  info: { ...INFO, contrastText: '#fff' },
  success: { ...SUCCESS, contrastText: GREY[800] },
  warning: { ...WARNING, contrastText: GREY[800] },
  error: { ...ERROR, contrastText: '#fff' },
  grey: GREY,
  blue: BLUE,
  green: GREEN,
  gradients: GRADIENTS,
  divider: GREY[500_24],
  action: {
    hover: GREY[500_8],
    selected: GREY[500_16],
    disabled: '#FFFFFF',
    disabledBackground: '#C8DDF1',
    focus: GREY[500_24],
    hoverOpacity: 0.08,
    disabledOpacity: 0.48,
  },
  button: BUTTON_BACKCOLORS,
  categoryIcon: '#999999',
  notificationBar: '#47CC7C',
  sideBarBackColor: '#ECEFF3',
  sideBarColor: '#9BAAB8',
  formBorder: '#BEBEBE',
  contactCard: {
    background: {
      lightGray: '#ECEFF3',
      black: '#2C323F',
      darkBlue: '#182036',
      blue: '#3F7FE0',
    },
    border: {
      gray: '#C9CCD1',
    },
    text: {
      blue: '#3F7FE0',
      black: '#2C323F',
      gray: '#AAB0B8',
      red: '#E03F67',
    },
  },
};

const palette = {
  light: {
    ...COMMON,
    text: { primary: GREY[800], secondary: GREY[600], disabled: GREY[500] },
    background: { paper: '#fff', default: '#fff', neutral: GREY[200] },
    action: { active: GREY[600], ...COMMON.action },
  },
  dark: {
    ...COMMON,
    text: { primary: '#fff', secondary: GREY[500], disabled: GREY[600] },
    background: { paper: GREY[800], default: GREY[900], neutral: GREY[500_16] },
    action: { active: GREY[500], ...COMMON.action },
  },
};

export default palette;
