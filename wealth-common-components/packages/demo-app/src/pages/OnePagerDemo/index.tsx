import { Stack, Box } from '@mui/material';
import { MarkdownEditorProvider, OnePager } from '@wealthcom/visualizer';
import { onePagerData } from './demo-data-one-pager/ONE_PAGER_1';

export function OnePagerDemo() {
  return (
    <MarkdownEditorProvider
      initialState={{
        setCitationLocation: undefined,
        mdText: onePagerData.data.summaryData ?? '',
        updatedAt: undefined,
        onSave: undefined,
      }}
    >
      <Stack sx={{ height: '100%' }}>
        <OnePager />
      </Stack>
    </MarkdownEditorProvider>
  );
}
