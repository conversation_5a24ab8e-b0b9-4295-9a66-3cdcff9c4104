import { StoryFn, Meta } from '@storybook/react-vite';
import { Quiz } from '@wealthcom/common-quiz';
import { getDefaultTheme } from 'storybook-host';

const mockFunc = () => {
  return null;
};

export default {
  component: Quiz,
  title: 'Quiz',
} as Meta<typeof Quiz>;

const Template: StoryFn<typeof Quiz> = (args) => <Quiz {...args} theme={getDefaultTheme()} />;

export const Primary = Template.bind({});
Primary.args = {
  skipAddressCheck: true,
  isOpen: true,
  onClose: mockFunc,
  onQuizRepeat: mockFunc,
  onQuizComplete: mockFunc,
  onChoosePlan: mockFunc,
  onSeeMorePlans: mockFunc,
  faqProps: undefined,
  userAddress: 'New York',
  maritalPreselect: true,
  netWorthPreselect: true,
  hasEstatePlan: true,
  theme: getDefaultTheme(),
  config: {
    apiPublicUrl: 'https://pj5vmm437bcn7crxtcsstgz3ri.appsync-api.us-west-2.amazonaws.com/graphql',
    googleMapsApiKey: 'AIzaSyCowJDcftiRenFF00lhikXfwsavZc19qQs',
  },
};
