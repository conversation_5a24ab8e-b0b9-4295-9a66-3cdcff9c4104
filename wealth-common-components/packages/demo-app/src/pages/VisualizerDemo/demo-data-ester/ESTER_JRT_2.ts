import { JobResultEntry } from '@wealthcom/visualizer';
// revocable_trusts_and_supporting_documents
// 'Individual or Joint Trust': 'Joint'
export const ESTER_JRT_2 = [
  {
    category: 'Overview',
    contextEndIndex: 37,
    contextStartIndex: 33,
    key: 'Document Type',
    pageNumber: 0,
    probability: 0.9310874968612811,
    value: 'Trust',
    valueType: 'DocumentType',
  },
  {
    category: 'Overview',
    contextEndIndex: 17,
    contextStartIndex: 12,
    key: 'Restatement or Amendment',
    pageNumber: 0,
    probability: 0.9406170177882771,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 289,
    contextStartIndex: 279,
    key: 'Trust Name',
    pageNumber: 0,
    probability: 0.999948171795808,
    value: '"The W Seymour Smith And Alice Farny Smith Revocable Living Trust"',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 54,
    contextStartIndex: 48,
    key: 'Trustor',
    pageNumber: 1,
    probability: 0.684626697992126,
    value: '<PERSON> <PERSON>',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 54,
    contextStartIndex: 48,
    key: 'Trustor',
    pageNumber: 0,
    probability: 0.6590019323271379,
    value: 'Alice Farny Smith',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Individual or Joint Trust',
    pageNumber: 1,
    probability: 0.99,
    value: 'Joint',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 3097,
    contextStartIndex: 3097,
    key: 'Governing State',
    pageNumber: 1,
    probability: 0.8875411500011812,
    value: 'Florida',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 97,
    contextStartIndex: 95,
    key: 'Trust Creation Date',
    pageNumber: 1,
    probability: 0.9991014505866193,
    value: 'April 17, 1975',
    valueType: 'String',
  },
  {
    category: 'Family',
    contextEndIndex: 2074,
    contextStartIndex: 2073,
    key: 'Child',
    pageNumber: 7,
    probability: 0.6112600091980941,
    value: 'Noel Fernandez',
    valueType: 'Person',
  },
  {
    category: 'Family',
    contextEndIndex: 2114,
    contextStartIndex: 2113,
    key: 'Child',
    pageNumber: 0,
    probability: 0.9978050188664154,
    value: 'Patricia Asch',
    valueType: 'Person',
  },
  {
    category: 'Family',
    contextEndIndex: 2094,
    contextStartIndex: 2092,
    key: 'Child',
    pageNumber: 0,
    probability: 0.7451416308879297,
    value: 'Peter S Wood',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    contextEndIndex: 64,
    contextStartIndex: 62,
    key: 'Initial Trustee',
    pageNumber: 0,
    probability: 0.6972298837633069,
    value: 'Alice Farny Smith',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 64,
    contextStartIndex: 58,
    key: 'Initial Trustee',
    pageNumber: 1,
    probability: 0.***************,
    value: 'W Seymour Smith',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 821,
    contextStartIndex: 815,
    key: 'Successor Trustee',
    pageNumber: 5,
    probability: 0.****************,
    value: 'William A Muirhead',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 830,
    contextStartIndex: 818,
    key: 'Second Successor Trustee',
    pageNumber: 3,
    probability: 0.****************,
    value: 'Guaranty Bank And Trust Company',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Third Successor Trustee',
    pageNumber: 0,
    probability: 0,
    value: 'No Information Found',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 2115,
    contextStartIndex: 2098,
    key: 'Fourth Successor Trustee',
    pageNumber: 0,
    probability: 0.****************,
    value: 'Patricia Asch',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 2097,
    contextStartIndex: 2075,
    key: 'Fifth Successor Trustee',
    pageNumber: 0,
    probability: 0.****************,
    value: 'Peter S Wood',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 7,
    contextStartIndex: 5,
    key: 'Investment Advisor',
    pageNumber: 1,
    probability: 0.****************,
    value: 'Steven W Macris',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: 269,
    contextStartIndex: 265,
    key: 'Credit Shelter Trust',
    pageNumber: 0,
    probability: 0.6743198066660309,
    value: 'No',
    valueType: 'Boolean',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: 2115,
    contextStartIndex: 2114,
    key: 'Beneficiary During Trust Period',
    pageNumber: 0,
    probability: 0.9690636583531212,
    value: 'Patricia Asch',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: 1388,
    contextStartIndex: 1386,
    key: 'Mandatory Income Distribution',
    pageNumber: 0,
    probability: 0.9329341606447789,
    value: 'Yes: Quarterly',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Is there a Power of Appointment?',
    pageNumber: 9,
    probability: 0,
    value: 'No',
    valueType: 'Boolean',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: 1516,
    contextStartIndex: 1511,
    key: 'Trust Termination Event',
    pageNumber: 0,
    probability: 0.****************,
    value: 'Death Of Beneficiary',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Funding Directive',
    pageNumber: 0,
    probability: 0.99,
    value: 'Other / Undefined',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: 2104,
    contextStartIndex: 2098,
    key: 'Marital Trust',
    pageNumber: 0,
    probability: 0.6811649205331839,
    value: 'No',
    valueType: 'Boolean',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: 1388,
    contextStartIndex: 1386,
    key: 'Mandatory Income Distribution',
    pageNumber: 0,
    probability: 0.9340303899534231,
    value: 'Yes: Quarterly',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust Termination Event',
    pageNumber: 0,
    probability: 0.99,
    value: 'Death Of Beneficiary',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: 2033,
    contextStartIndex: 2030,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: 0,
    probability: 0.6371962602659409,
    value: 'Gordon R S Smith: 20%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: 2120,
    contextStartIndex: 2110,
    key: 'Funding Directive',
    pageNumber: 0,
    probability: 0.7637035085767496,
    value: 'Fractional Share',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: 2061,
    contextStartIndex: 2057,
    key: 'Trust for Descendants',
    pageNumber: 0,
    probability: 0.6783539546213964,
    value: 'No',
    valueType: 'Boolean',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 2115,
    contextStartIndex: 2114,
    key: 'Beneficiary During Trust Period',
    pageNumber: 0,
    probability: 0.8005744572412425,
    value: 'Patricia Asch',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 2115,
    contextStartIndex: 2114,
    key: 'Beneficiary During Trust Period',
    pageNumber: 0,
    probability: 0.8005744572412425,
    value: 'Patricia Asch 2',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 2115,
    contextStartIndex: 2114,
    key: 'Beneficiary During Trust Period',
    pageNumber: 0,
    probability: 0.8005744572412425,
    value: 'Patricia Asch 3',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 2033,
    contextStartIndex: 2030,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: 0,
    probability: 0.6371962602659409,
    value: 'Gordon R S Smith: 20%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 2033,
    contextStartIndex: 2030,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: 0,
    probability: 0.6371962602659409,
    value: 'Gordon R S Smith: 20%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 2033,
    contextStartIndex: 2030,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: 0,
    probability: 0.6371962602659409,
    value: 'Gordon R S Smith: 20%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 2033,
    contextStartIndex: 2030,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: 0,
    probability: 0.6371962602659409,
    value: 'Gordon R S Smith: 20%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 2033,
    contextStartIndex: 2030,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: 0,
    probability: 0.6371962602659409,
    value: 'Gordon R S Smith: 20%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 1388,
    contextStartIndex: 1386,
    key: 'Mandatory Income Distribution',
    pageNumber: 0,
    probability: 0.7739792792843101,
    value: 'Yes: Quarterly',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 1388,
    contextStartIndex: 1388,
    key: 'Interim Distribution or Withdrawal Power',
    pageNumber: 0,
    probability: 0.6558532307253256,
    value: 'Yes',
    valueType: 'Boolean',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 1516,
    contextStartIndex: 1511,
    key: 'Trust Termination Event',
    pageNumber: 0,
    probability: 0.6556792374474287,
    value: 'Death Of Beneficiary',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: "Survivor's Trust",
    pageNumber: 9,
    probability: 0,
    value: 'No',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust for Other Beneficiaries',
    pageNumber: 9,
    probability: 0,
    value: 'No',
    valueType: 'Boolean',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 2115,
    contextStartIndex: 2114,
    key: 'Primary Beneficiary - Specific Gift (Real Estate)',
    pageNumber: 0,
    probability: 0.9683989713409844,
    value: 'Patricia Asch',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 2115,
    contextStartIndex: 2114,
    key: 'Primary Beneficiary - Specific Gift',
    pageNumber: 0,
    probability: 0.9978760749214185,
    value: 'Patricia Asch',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 1',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 2',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 3',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 4',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 5',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 6',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 7',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 8',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 9',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 10',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 11',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 12',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 13',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 14',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 15',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 16',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Gordon R S Smith: Twenty Percent (20%) 17',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 1',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 2',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 3',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 4',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 5',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 6',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 7',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 8',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 9',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 10',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 11',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Noel Fernandez: Twenty Percent (80%) 12',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Residuary Outright to Beneficiary',
    pageNumber: 0,
    probability: 0.99,
    value: 'Yes',
    valueType: 'Boolean',
  },
  {
    category: 'Other Details',
    contextEndIndex: 7,
    contextStartIndex: 5,
    key: 'Attorney',
    pageNumber: 1,
    probability: 0.6863880520859473,
    value: 'Steven W Macris',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Generation-Skipping Transfer Tax Provisions',
    pageNumber: 9,
    probability: 0.4450885501398837,
    value: 'No',
    valueType: 'Boolean',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 2264,
    contextStartIndex: 2251,
    key: 'Spendthrift Clause',
    pageNumber: 0,
    probability: 0.7720465363924779,
    value: 'Yes',
    valueType: 'Boolean',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 2147,
    contextStartIndex: 2143,
    key: 'No Contest Clause',
    pageNumber: 0,
    probability: 0.9698975397499493,
    value: 'Yes',
    valueType: 'Boolean',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Conduit Trust)',
    pageNumber: 9,
    probability: 0.6535679725756397,
    value: 'No',
    valueType: 'Boolean',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 289,
    contextStartIndex: 279,
    key: 'Retirement Benefits (Accumulation Trust)',
    pageNumber: 0,
    probability: 0.8223358728581573,
    value: 'Yes',
    valueType: 'Boolean',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Special Needs Provision',
    pageNumber: 9,
    probability: 0.5475074772590418,
    value: 'No',
    valueType: 'Boolean',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 4037,
    contextStartIndex: 4036,
    key: 'Spousal Disclaimer Provision',
    pageNumber: 0,
    probability: 0.639313737489175,
    value: 'Yes',
    valueType: 'Boolean',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Digital Assets Powers',
    pageNumber: 9,
    probability: 0.5520774453126308,
    value: 'No',
    valueType: 'Boolean',
  },
] as JobResultEntry[];
