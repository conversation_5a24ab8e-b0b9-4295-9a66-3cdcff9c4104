// revocable_trusts_and_supporting_documents
// 'Individual or Joint Trust': 'Joint'
export const ESTER_JRT_1 = [
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Document Type',
    pageNumber: 1,
    probability: null,
    value: 'Trust',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Restatement or Amendment',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust Name',
    pageNumber: 0,
    probability: null,
    value: '<PERSON><PERSON> and <PERSON>s Living Trust',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Individual or Joint Trust',
    pageNumber: 0,
    probability: null,
    value: 'Joint',
    valueType: 'String',
  },
  {
    category: 'Family',
    contextEndIndex: 4382,
    contextStartIndex: 3907,
    key: 'Grandchild',
    pageNumber: 0,
    probability: null,
    value: 'Cadence Liberty Puls',
    valueType: 'String',
  },
  {
    category: 'Family',
    contextEndIndex: 4382,
    contextStartIndex: 3907,
    key: 'Grandchild',
    pageNumber: 0,
    probability: null,
    value: 'Melody Adeline Puls',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 596,
    contextStartIndex: 545,
    key: 'Initial Trustee',
    pageNumber: 1,
    probability: null,
    value: 'Kristi Rentz Puls and Randolph Carsten Puls',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Successor Trustee',
    pageNumber: 0,
    probability: null,
    value: 'Fred M Puls',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Second Successor Trustee',
    pageNumber: 1,
    probability: null,
    value: 'Connie Voke',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: 4759,
    contextStartIndex: 4384,
    key: "Survivor's Trust",
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: 10012,
    contextStartIndex: 9833,
    key: 'Trust for Descendants',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Residuary Outright to Beneficiary',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Marital Trust',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Credit Shelter Trust',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust for Other Beneficiaries',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: 4382,
    contextStartIndex: 3907,
    key: 'Beneficiary During Trust Period',
    pageNumber: 0,
    probability: null,
    value: 'Cadence Liberty Puls and Melody Adeline Puls',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Income Distribution',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'No Contest Clause',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spendthrift Clause',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Generation-Skipping Transfer Tax Provisions',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Conduit Trust)',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Accumulation Trust)',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Special Needs Provision',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spousal Disclaimer Provision',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Qualified Domestic Trust',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Qualified Subchapter S Provision',
    pageNumber: 2,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 2039,
    contextStartIndex: 1657,
    key: 'Discretionary Distribution Provision',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 6535,
    contextStartIndex: 5715,
    key: 'Digital Assets Powers',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
];
