import { JobResultEntry } from '@wealthcom/visualizer';
// revocable_trusts_and_supporting_documents
// 'Individual or Joint Trust': 'Individual'
export const ESTER_IRT_2 = [
  {
    category: 'Overview',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Document Type',
    probability: null,
    value: 'Trust',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Individual or Joint Trust',
    probability: null,
    value: 'Individual',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Name',
    probability: null,
    value: 'The Myron E Wolf Family Trust',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trustor',
    probability: null,
    value: '<PERSON><PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Overview',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Governing State',
    probability: null,
    value: 'Pennsylvania',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Creation Date',
    probability: null,
    value: 'February 14, 1992',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Spouse / Partner',
    probability: null,
    value: 'Arlene S Wolf',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Sibling',
    probability: null,
    value: 'John Witmer Wolf',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Sibling',
    probability: null,
    value: 'NEVIN WITMER WOLF',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Sibling',
    probability: null,
    value: 'JAMES WITMER WOLF',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Sibling',
    probability: null,
    value: 'MARY ELIZABETH McCARDLE',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Sibling',
    probability: null,
    value: 'CHARLOTTE SCHMID LUTZ',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Sibling',
    probability: null,
    value: 'MARY JANE SCHMID PHILLIPS',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Sibling',
    probability: null,
    value: 'ERLA MARIE SCHMID WENTZEL',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Sibling',
    probability: null,
    value: 'HERBERT JAMES SCHMID, JR.',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Initial Trustee',
    probability: null,
    value: 'Myron E Wolf',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Successor Trustee',
    probability: null,
    value: 'ARLENE S. WOLF',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Successor Trustee',
    probability: null,
    value: 'John W. Wolf ',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Second Successor Trustee',
    probability: null,
    value: 'WILLIAM D. FISHER',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Second Successor Trustee',
    probability: null,
    value: 'HERSHEY GROFF, JR.',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Guardian',
    probability: null,
    value: 'Hamilton Bank (amended to Bank of Lancaster County)',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Residuary Outright to Beneficiary',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Marital Trust',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Credit Shelter Trust',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust for Descendants',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust for Other Beneficiaries',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'Jane Smith: 100%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Specific Gift (Cash)',
    probability: null,
    value: 'John Smith: $15,000',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Specific Gift',
    probability: null,
    value: 'Anna Schmidt: Engagement Ring',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Generation-Skipping Transfer Tax Provisions',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Spendthrift Clause',
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'No Contest Clause',
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Retirement Benefits (Conduit Trust)',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Retirement Benefits (Accumulation Trust)',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Special Needs Provision',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Spousal Disclaimer Provision',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Digital Assets Powers',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Witness',
    probability: null,
    value: 'Douglas Craig Lutz',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Name of the Credit Shelter Trust',
    probability: null,
    value: 'MYRON E. WOLF Family Trust',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Beneficiary During Trust Period',
    probability: null,
    value: 'ARLENE S. WOLF',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Mandatory Income Distribution',
    probability: null,
    value: 'Yes: Undefined',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    probability: null,
    value: 'Death of ARLENE S. WOLF',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'John Witmer: 15%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'NEVIN WITMER WOLF: 16%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'JAMES WITMER WOLF: 15%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'MARY ELIZABETH McCARDLE: 10%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'CHARLOTTE SCHMID LUTZ: 11%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'MARY JANE SCHMID PHILLIPS: 8%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'ERLA MARIE SCHMID WENTZEL: 6%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'HERBERT JAMES SCHMID, JR.: 4%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: "ST. LUKE'S UNITED CHURCH OF CHRIST: 5%",
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'THE LANCASTER THEOLOGICAL SEMINARY OF THE UNITED CHURCH OF CHRIST: 5%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'THE LANCASTER COUNTY FOUNDATION: 5%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Funding Directive',
    probability: null,
    value: 'Pecuniary Amount',
    valueType: 'String',
  },
] as JobResultEntry[];
