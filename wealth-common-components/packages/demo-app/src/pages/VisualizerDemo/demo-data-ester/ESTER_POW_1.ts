export const ESTER_POW_1 = [
  {
    category: 'Overview',
    contextEndIndex: 85,
    contextStartIndex: 0,
    key: 'Document Type',
    pageNumber: 1,
    probability: null,
    value: 'Will',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 208,
    contextStartIndex: 133,
    key: 'Governing State',
    pageNumber: 1,
    probability: null,
    value: 'New York',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 85,
    contextStartIndex: 12,
    key: 'Restatement or Amendment',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 85,
    contextStartIndex: 12,
    key: 'Testator',
    pageNumber: 1,
    probability: null,
    value: 'RODERICK E. CHARLES',
    valueType: 'Person',
  },
  {
    category: 'Overview',
    contextEndIndex: 3689,
    contextStartIndex: 3619,
    key: 'Date of Signature',
    pageNumber: 0,
    probability: null,
    value: 'March 13, 1986',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 208,
    contextStartIndex: 10,
    key: 'Spouse / Partner',
    pageNumber: 0,
    probability: null,
    value: 'MAMIE DEBMAN CHARLES',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Child',
    pageNumber: 3,
    probability: null,
    value: 'RODERICK TODD CHARLES',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 994,
    contextStartIndex: 790,
    key: 'Child',
    pageNumber: 0,
    probability: null,
    value: 'KIMBERLY ANN CHARLES',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    contextEndIndex: 2675,
    contextStartIndex: 2553,
    key: 'Executor',
    pageNumber: 0,
    probability: null,
    value: 'MAMIE DEBMAN CHARLES',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'First Alternate Executor',
    pageNumber: 3,
    probability: null,
    value: 'RODERICK TODD CHARLES',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 3063,
    contextStartIndex: 2974,
    key: 'First Alternate Executor',
    pageNumber: 0,
    probability: null,
    value: 'KIMBERLY ANN CHARLES',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 3191,
    contextStartIndex: 3073,
    key: 'Guardian',
    pageNumber: 0,
    probability: null,
    value: 'KIMBERLY ANN CHARLES',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'First Alternate Guardian',
    pageNumber: 3,
    probability: null,
    value: 'RODERICK TODD CHARLES',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 3191,
    contextStartIndex: 3073,
    key: 'Initial Trustee',
    pageNumber: 0,
    probability: null,
    value: 'KIMBERLY ANN CHARLES',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Successor Trustee',
    pageNumber: 3,
    probability: null,
    value: 'RODERICK TODD CHARLES',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: 1333,
    contextStartIndex: 1089,
    key: 'Marital Trust',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: 1333,
    contextStartIndex: 1089,
    key: 'Credit Shelter Trust',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: 1333,
    contextStartIndex: 1089,
    key: 'Trust for Descendants',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: 1333,
    contextStartIndex: 1089,
    key: 'Trust for Other Beneficiaries',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Residuary Outright to Beneficiary',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: "Survivor's Trust",
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Primary Beneficiary - Residuary Estate',
    pageNumber: 0,
    probability: null,
    value: 'MAMIE DEBMAN CHARLES: (100%)',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Contingent Beneficiary - Residuary Estate',
    pageNumber: 0,
    probability: null,
    value: 'RODERICK TODD CHARLES: (1/2)',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Contingent Beneficiary - Residuary Estate',
    pageNumber: 0,
    probability: null,
    value: 'KIMBERLY ANN CHARLES: (1/2)',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Generation-Skipping Transfer Tax Provisions',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spendthrift Clause',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'No Contest Clause',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Conduit Trust)',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Accumulation Trust)',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Special Needs Provision',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spousal Disclaimer Provision',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Digital Assets Powers',
    pageNumber: 3,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Witness',
    pageNumber: 3,
    probability: null,
    value: 'LINDA J. WOHLHUETER',
    valueType: 'String',
  },
  {
    category: 'Other Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Witness',
    pageNumber: 3,
    probability: null,
    value: 'ROGER T. DAVISON',
    valueType: 'String',
  },
];
