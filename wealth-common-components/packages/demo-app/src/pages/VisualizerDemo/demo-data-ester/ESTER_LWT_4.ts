import { JobResultEntry } from '@wealthcom/visualizer';

export const ESTER_LWT_4 = [
  {
    category: 'Overview',
    key: 'Document Type',
    probability: null,
    value: 'Last Will And Testament',
    valueType: 'String',
  },
  {
    category: 'Overview',
    key: 'Date Of Signature',
    probability: null,
    value: 'April 28, 2017',
    valueType: 'Person',
  },
  {
    category: 'Overview',
    key: 'Governing State',
    probability: null,
    value: 'Georgia',
    valueType: 'String',
  },
  {
    category: 'Overview',
    key: 'Testator',
    probability: null,
    value: '<PERSON><PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Child',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Child',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Child',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Grandchild',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Executor',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'First Alternate Executor',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Second Alternate Executor',
    probability: null,
    value: 'Walter Dean White',
    valueType: 'Person',
  },
  // {
  //   category: 'Key People',
  //   key: 'Guardian',
  //   probability: null,
  //   value: 'Willa Gaddy',
  //   valueType: 'Person',
  //
  // },
  {
    category: 'Key People',
    key: 'Initial Trustee',
    probability: null,
    value: 'Ronald Keith White',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Successor Trustee',
    probability: null,
    value: 'Nicholas Pepper',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Second Successor Trustee',
    probability: null,
    value: 'James Earl White',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Third Successor Trustee',
    probability: null,
    value: 'Walter Dean White',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Fourth Successor Trustee',
    probability: null,
    value: 'William R Gaddy',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Pet Caretaker',
    probability: null,
    value: 'Willa Gaddy',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Credit Shelter Trust',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Marital Trust',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Trust for Descendants',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Trust for Other Beneficiaries',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Residuary Outright to Beneficiary',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Residuary Outright to Beneficiary',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    key: 'Primary Beneficiary - Residuary Estate',
    probability: null,
    value: 'James Earl White',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    key: 'Contingent Beneficiary - Residuary Estate',
    probability: null,
    value: 'Deborah Kay White',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    key: 'Primary Beneficiary - Specific Gift (Real Estate)',
    probability: null,
    value: 'James Earl White',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    key: 'Contingent Beneficiary - Specific Gift (Real Estate)',
    probability: null,
    value: 'Deborah Kay White',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    key: 'Beneficiary During Trust Period',
    probability: null,
    value: 'James Earl White',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    key: 'Mandatory Income Distribution',
    probability: null,
    value: 'Any Unused Money Is To Be Divided Equally Between My Three Sons',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    key: 'Trust Termination Event',
    probability: null,
    value: 'Simultaneous Death',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'My Heirs Determined According To The Laws Of Intestate Succession',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    key: 'Funding Directive',
    probability: null,
    value: 'Any Unused Money Is To Be Divided Equally Between My Three Sons',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    key: 'Beneficiary During Trust Period',
    probability: null,
    value: 'James Earl White',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    key: 'Mandatory Income Distribution',
    probability: null,
    value: 'Any Unused Money Is To Be Divided Equally Between My Three Sons',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    key: 'Trust Termination Event',
    probability: null,
    value: 'Uniform Simultaneous Death Act As Amended Or Any Substantially Similar Successor Act Effective On The Date Of My Death Will Apply',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'My Heirs Determined According To The Laws Of Intestate Succession',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    key: 'Funding Directive',
    probability: null,
    value: 'Any Unused Money Is To Be Divided Equally Between My Three Sons',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Beneficiary During Trust Period',
    probability: null,
    value: 'James Earl White',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Income Distribution',
    probability: null,
    value: 'Any Unused Money Is To Be Divided Equally Between My Three Sons',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Is there a Limited Power of  Appointment?',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Interim Distribution or Withdrawal Power',
    probability: null,
    value: '3334%',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Trust Termination Event',
    probability: null,
    value: 'Uniform Simultaneous Death Act As Amended Or Any Substantially Similar Successor Act Effective On The Date Of My Death Will Apply',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'My Heirs Determined According To The Laws Of Intestate Succession',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Beneficiary During Trust Period',
    probability: null,
    value: 'James Earl White',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Income Distribution',
    probability: null,
    value: 'Any Unused Money Is To Be Divided Equally Between My Three Sons',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Is there a Limited Power of  Appointment?',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Trust Termination Event',
    probability: null,
    value: 'Uniform Simultaneous Death Act',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'My Heirs Determined According To The Laws Of Intestate Succession',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Generation-Skipping Transfer Tax Provisions',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Generation-Skipping Transfer Tax Provisions Trust',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Spendthrift Clause',
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    key: 'No Contest Clause',
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    key: 'Retirement Benefits (Conduit Trust)',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Retirement Benefits (Accumulation Trust)',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Spousal Disclaimer Provision',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Digital Assets Powers',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    key: 'Attorney',
    probability: null,
    value: 'Nicholas Pepper',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    key: 'Notary',
    probability: null,
    value: 'J Bartow',
    valueType: 'Person',
  },
] as JobResultEntry[];
