export const ESTER_LWT_2 = [
  {
    category: 'Overview',
    key: 'Document Type',
    probability: null,
    value: 'Last Will And Testament',
    valueType: 'String',
  },
  {
    category: 'Overview',
    key: 'Date Of Signature',
    probability: null,
    value: 'September 17, 2018',
    valueType: 'Person',
  },
  {
    category: 'Overview',
    key: 'Governing State',
    probability: null,
    value: 'Georgia',
    valueType: 'String',
  },
  {
    category: 'Overview',
    key: 'Testator',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Spouse / Partner',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Child',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Grandchild',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'First Alternate Executor',
    probability: null,
    value: '<PERSON>ra <PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Second Alternate Executor',
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
  },
  // {
  //   category: 'Key People',
  //   key: 'Guardian',
  //   probability: null,
  //   value: '<PERSON>',
  //   valueType: 'Person',
  //
  // },
  {
    category: 'Key People',
    key: 'First Alternate Guardian',
    probability: null,
    value: 'Tyra S Williams',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Pet Caretaker',
    probability: null,
    value: 'Justin Chicken',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Credit Shelter Trust',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Marital Trust',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Trust for Descendants',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Trust for Other Beneficiaries',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Residuary Outright to Beneficiary',
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Generation-Skipping Transfer Tax Provisions Trust',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Spendthrift Clause',
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    key: 'Retirement Benefits (Conduit Trust)',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Special Needs Provision',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Spousal Disclaimer Provision',
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    key: 'Notary',
    probability: null,
    value: 'James W Friedewald',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    key: 'Witness',
    probability: null,
    value: 'Sanden B Dean',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    key: 'Witness',
    probability: null,
    value: 'Justin Chicken',
    valueType: 'Person',
  },
];
