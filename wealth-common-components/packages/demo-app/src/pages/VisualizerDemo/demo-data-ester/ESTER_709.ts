import { Tax709CalculationResultPayload } from '@wealthcom/visualizer';

export const ESTER_709 = {
  self: {
    exemptionForfeited: 13220000,
    federalGiftEstateTaxExemptionUsed: 531900,
    postSunset: {
      effectiveTaxRate: 0.4,
      estateGiftExemption: 7000000,
      exemptionForfeited: null,
      gstExemption: 7000000,
      liability: 0,
      remainingEstateGiftExemption: 6468100,
      remainingGstExemption: 6457990,
      taxExposedEstate: 0,
      __typename: 'Tax709Calculation',
    },
    preSunset: {
      effectiveTaxRate: 0.4,
      estateGiftExemption: 13610000,
      exemptionForfeited: null,
      gstExemption: 13610000,
      liability: 0,
      remainingEstateGiftExemption: 13078100,
      remainingGstExemption: 13067990,
      taxExposedEstate: 0,
      __typename: 'Tax709Calculation',
    },
    totalGstExemptionUsed: 542010,
    __typename: 'Tax709CalculationResultPerson',
  },
  spouse: {
    exemptionForfeited: 13220000,
    federalGiftEstateTaxExemptionUsed: 1652076,
    postSunset: {
      effectiveTaxRate: 0.4,
      estateGiftExemption: 7000000,
      exemptionForfeited: null,
      gstExemption: 7000000,
      liability: 0,
      remainingEstateGiftExemption: 5347924,
      remainingGstExemption: 7000000,
      taxExposedEstate: 0,
      __typename: 'Tax709Calculation',
    },
    preSunset: {
      effectiveTaxRate: 0.4,
      estateGiftExemption: 13610000,
      exemptionForfeited: null,
      gstExemption: 13610000,
      liability: 0,
      remainingEstateGiftExemption: 11957924,
      remainingGstExemption: 13610000,
      taxExposedEstate: 0,
      __typename: 'Tax709Calculation',
    },
    totalGstExemptionUsed: 0,
    __typename: 'Tax709CalculationResultPerson',
  },
  __typename: 'Tax709CalculationResultPayload',
} as Tax709CalculationResultPayload;
