import { Tax709CalculationResultPayload } from '@wealthcom/visualizer';

export const ESTER_709_3 = {
  self: {
    exemptionForfeited: 6610000,
    federalGiftEstateTaxExemptionUsed: 3872046,
    postSunset: {
      estateGiftExemption: 7000000,
      exemptionForfeited: null,
      gstExemption: 7000000,
      liability: 0,
      remainingEstateGiftExemption: 3127954,
      remainingGstExemption: 2645764,
      __typename: 'Tax709Calculation',
    },
    preSunset: {
      estateGiftExemption: 13610000,
      exemptionForfeited: null,
      gstExemption: 13610000,
      liability: 0,
      remainingEstateGiftExemption: 9737954,
      remainingGstExemption: 9255764,
      __typename: 'Tax709Calculation',
    },
    totalGstExemptionUsed: 4354236,
    __typename: 'Tax709CalculationResultPerson',
  },
  spouse: null,
  __typename: 'Tax709CalculationResultPayload',
} as Tax709CalculationResultPayload;
