import { DocumentImportJobResponse } from '@wealthcom/visualizer';

export const ESTER_709_2 = {
  error: null,
  payload: [
    {
      confirmedResult: [
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Document Type',
          pageNumber: null,
          probability: null,
          value: 'Form 709',
          valueType: 'String',
        },
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Tax Year',
          pageNumber: null,
          probability: null,
          value: '2022',
          valueType: 'String',
        },
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Donor',
          pageNumber: null,
          probability: null,
          value: '<PERSON><PERSON>',
          valueType: 'Person',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total Taxable Gifts',
          pageNumber: null,
          probability: null,
          value: '524300',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'DSUE | Total from Column E, Parts 1 and 2',
          pageNumber: null,
          probability: null,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Restored Exclusion Amount',
          pageNumber: null,
          probability: null,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption available for future transfers',
          pageNumber: null,
          probability: null,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Taxable Gifts',
          pageNumber: null,
          probability: null,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total Amount of Taxable Gifts for Prior Periods',
          pageNumber: null,
          probability: null,
          value: '524300',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total exemption used for periods before filing this return',
          pageNumber: null,
          probability: null,
          value: '524300',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption claimed on this return',
          pageNumber: null,
          probability: null,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Automatic allocation of exemption to transfers reported',
          pageNumber: null,
          probability: null,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption allocated to transfers not shown',
          pageNumber: null,
          probability: null,
          value: '0',
          valueType: 'Currency',
        },
      ],
      documentType: 'Tax709',
      failureReason: null,
      intermittentResult:
        '{"Overview":{"entries":[{"fullValue":"Form 709","nonUniqueKey":"Document Type","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"String","inputs":{"Document Type":"Form 709"},"pageNumber":null},{"fullValue":"2022","nonUniqueKey":"Tax Year","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"String","inputs":{"Tax Year":"2022"},"pageNumber":null},{"fullValue":"Alina Berman","nonUniqueKey":"Donor","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Person","inputs":{"firstName":"Alina","middleName":"","lastName":"Berman","role":"Alina Berman","relationship":"Other"},"pageNumber":null}],"confirmed":true,"expanded":false},"Form 709 | Important Numbers":{"entries":[{"fullValue":"524300","nonUniqueKey":"Total Taxable Gifts","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Total Taxable Gifts":"524300"},"pageNumber":null},{"fullValue":"0","nonUniqueKey":"DSUE | Total from Column E, Parts 1 and 2","probability":0,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"DSUE | Total from Column E, Parts 1 and 2":"0"},"pageNumber":null},{"fullValue":"0","nonUniqueKey":"Restored Exclusion Amount","probability":0,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Restored Exclusion Amount":"0"},"pageNumber":null},{"fullValue":"0","nonUniqueKey":"Exemption available for future transfers","probability":0,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Exemption available for future transfers":"0"},"pageNumber":null}],"confirmed":true,"expanded":false},"Form 709 | Other Details":{"entries":[{"fullValue":"0","nonUniqueKey":"Taxable Gifts","probability":0.5,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Taxable Gifts":"0"},"pageNumber":null},{"fullValue":"524300","nonUniqueKey":"Total Amount of Taxable Gifts for Prior Periods","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Total Amount of Taxable Gifts for Prior Periods":"524300"},"pageNumber":null},{"fullValue":"524300","nonUniqueKey":"Total exemption used for periods before filing this return","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Total exemption used for periods before filing this return":"524300"},"pageNumber":null},{"fullValue":"0","nonUniqueKey":"Exemption claimed on this return","probability":0,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Exemption claimed on this return":"0"},"pageNumber":null},{"fullValue":"0","nonUniqueKey":"Automatic allocation of exemption to transfers reported","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Automatic allocation of exemption to transfers reported":"0"},"pageNumber":null},{"fullValue":"0","nonUniqueKey":"Exemption allocated to transfers not shown","probability":0,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Exemption allocated to transfers not shown":"0"},"pageNumber":null}],"confirmed":true,"expanded":false}}',
      jobId: '06f81aa7-c67c-4db0-bd98-417b9151843e',
      jobResult: [
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Document Type',
          pageNumber: null,
          probability: 1,
          value: 'Form 709',
          valueType: 'String',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total Taxable Gifts',
          pageNumber: null,
          probability: 1,
          value: '524300',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Taxable Gifts',
          pageNumber: null,
          probability: 0.5,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'DSUE | Total from Column E, Parts 1 and 2',
          pageNumber: null,
          probability: 0,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total Amount of Taxable Gifts for Prior Periods',
          pageNumber: null,
          probability: 1,
          value: '524300',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Restored Exclusion Amount',
          pageNumber: null,
          probability: 0,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total exemption used for periods before filing this return',
          pageNumber: null,
          probability: 1,
          value: '524300',
          valueType: 'Currency',
        },
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Tax Year',
          pageNumber: null,
          probability: 1,
          value: '2022',
          valueType: 'String',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption available for future transfers',
          pageNumber: null,
          probability: 0,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption claimed on this return',
          pageNumber: null,
          probability: 0,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Automatic allocation of exemption to transfers reported',
          pageNumber: null,
          probability: 1,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption allocated to transfers not shown',
          pageNumber: null,
          probability: 0,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Donor',
          pageNumber: null,
          probability: 1,
          value: 'Alina Berman',
          valueType: 'Person',
        },
      ],
      jobStatus: 'Confirmed',
      metadata: '{"totalTaxableEstate":13610001}',
      softDeleted: false,
      teamId: null,
      vaultId: '282cc6e6-b4d2-480c-a9be-a20d4758c655',
    },
    {
      confirmedResult: [
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Document Type',
          pageNumber: null,
          probability: null,
          value: 'Form 709',
          valueType: 'String',
        },
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Tax Year',
          pageNumber: null,
          probability: null,
          value: '2022',
          valueType: 'String',
        },
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Donor',
          pageNumber: null,
          probability: null,
          value: 'Kate Quagmire',
          valueType: 'Person',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total Taxable Gifts',
          pageNumber: null,
          probability: null,
          value: '1026',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'DSUE | Total from Column E, Parts 1 and 2',
          pageNumber: null,
          probability: null,
          value: '12205',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Restored Exclusion Amount',
          pageNumber: null,
          probability: null,
          value: '167290',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption available for future transfers',
          pageNumber: null,
          probability: null,
          value: '3263',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Taxable Gifts',
          pageNumber: null,
          probability: null,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total Amount of Taxable Gifts for Prior Periods',
          pageNumber: null,
          probability: null,
          value: '771370',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total exemption used for periods before filing this return',
          pageNumber: null,
          probability: null,
          value: '1000',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption claimed on this return',
          pageNumber: null,
          probability: null,
          value: '2731',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Automatic allocation of exemption to transfers reported',
          pageNumber: null,
          probability: null,
          value: '1000',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption allocated to transfers not shown',
          pageNumber: null,
          probability: null,
          value: '41223',
          valueType: 'Currency',
        },
      ],
      documentType: 'Tax709',
      failureReason: null,
      intermittentResult:
        '{"Overview":{"entries":[{"fullValue":"Form 709","nonUniqueKey":"Document Type","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"String","inputs":{"Document Type":"Form 709"},"pageNumber":null},{"fullValue":"2022","nonUniqueKey":"Tax Year","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"String","inputs":{"Tax Year":"2022"},"pageNumber":null},{"fullValue":"Kate Quagmire","nonUniqueKey":"Donor","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Person","inputs":{"firstName":"Kate","middleName":"","lastName":"Quagmire","role":"Kate Quagmire","relationship":"Other"},"pageNumber":null}],"confirmed":true,"expanded":false},"Form 709 | Important Numbers":{"entries":[{"fullValue":"1026","nonUniqueKey":"Total Taxable Gifts","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Total Taxable Gifts":"1026"},"pageNumber":null},{"fullValue":"12205","nonUniqueKey":"DSUE | Total from Column E, Parts 1 and 2","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"DSUE | Total from Column E, Parts 1 and 2":"12205"},"pageNumber":null},{"fullValue":"167290","nonUniqueKey":"Restored Exclusion Amount","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Restored Exclusion Amount":"167290"},"pageNumber":null},{"fullValue":"3263","nonUniqueKey":"Exemption available for future transfers","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Exemption available for future transfers":"3263"},"pageNumber":null}],"confirmed":true,"expanded":false},"Form 709 | Other Details":{"entries":[{"fullValue":"0","nonUniqueKey":"Taxable Gifts","probability":0,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Taxable Gifts":"0"},"pageNumber":null},{"fullValue":"771370","nonUniqueKey":"Total Amount of Taxable Gifts for Prior Periods","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Total Amount of Taxable Gifts for Prior Periods":"771370"},"pageNumber":null},{"fullValue":"1000","nonUniqueKey":"Total exemption used for periods before filing this return","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Total exemption used for periods before filing this return":"1000"},"pageNumber":null},{"fullValue":"2731","nonUniqueKey":"Exemption claimed on this return","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Exemption claimed on this return":"2731"},"pageNumber":null},{"fullValue":"1000","nonUniqueKey":"Automatic allocation of exemption to transfers reported","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Automatic allocation of exemption to transfers reported":"1000"},"pageNumber":null},{"fullValue":"41223","nonUniqueKey":"Exemption allocated to transfers not shown","probability":1,"contextStartIndex":null,"contextEndIndex":null,"type":"Currency","inputs":{"Exemption allocated to transfers not shown":"41223"},"pageNumber":null}],"confirmed":true,"expanded":false}}',
      jobId: '06f81aa7-c67c-4db0-bd98-417b9151843e',
      jobResult: [
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Document Type',
          pageNumber: null,
          probability: 1,
          value: 'Form 709',
          valueType: 'String',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total Taxable Gifts',
          pageNumber: null,
          probability: 1,
          value: '1026',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Taxable Gifts',
          pageNumber: null,
          probability: 0,
          value: '0',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'DSUE | Total from Column E, Parts 1 and 2',
          pageNumber: null,
          probability: 1,
          value: '12205',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total Amount of Taxable Gifts for Prior Periods',
          pageNumber: null,
          probability: 1,
          value: '771370',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Restored Exclusion Amount',
          pageNumber: null,
          probability: 1,
          value: '167290',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Total exemption used for periods before filing this return',
          pageNumber: null,
          probability: 1,
          value: '1000',
          valueType: 'Currency',
        },
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Tax Year',
          pageNumber: null,
          probability: 1,
          value: '2022',
          valueType: 'String',
        },
        {
          category: 'Form 709 | Important Numbers',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption available for future transfers',
          pageNumber: null,
          probability: 1,
          value: '3263',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption claimed on this return',
          pageNumber: null,
          probability: 1,
          value: '2731',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Automatic allocation of exemption to transfers reported',
          pageNumber: null,
          probability: 1,
          value: '1000',
          valueType: 'Currency',
        },
        {
          category: 'Form 709 | Other Details',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Exemption allocated to transfers not shown',
          pageNumber: null,
          probability: 1,
          value: '41223',
          valueType: 'Currency',
        },
        {
          category: 'Overview',
          contextEndIndex: null,
          contextStartIndex: null,
          key: 'Donor',
          pageNumber: null,
          probability: 1,
          value: 'Kate Quagmire',
          valueType: 'Person',
        },
      ],
      jobStatus: 'Confirmed',
      metadata: '{"totalTaxableEstate":13610001}',
      softDeleted: false,
      teamId: null,
      vaultId: '5d17ec4d-4bf4-4075-91d0-3db1667eabc1',
    },
  ],
  success: true,
  __typename: 'DocumentImportJobResponse',
} as DocumentImportJobResponse;
