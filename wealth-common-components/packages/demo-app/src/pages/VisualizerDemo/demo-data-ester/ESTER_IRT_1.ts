// revocable_trusts_and_supporting_documents
// 'Individual or Joint Trust': 'Individual'
export const ESTER_IRT_1 = [
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Document Type',
    pageNumber: 4,
    probability: null,
    value: 'Trust',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Restatement or Amendment',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 143,
    contextStartIndex: 136,
    key: 'Trust Name',
    pageNumber: 1,
    probability: null,
    value: 'Pauline Dixon Revocable I 2001175039 Ii Revocable Trust',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 39,
    contextStartIndex: 38,
    key: 'Trustor',
    pageNumber: 1,
    probability: null,
    value: '<PERSON>',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Individual or Joint Trust',
    pageNumber: 0,
    probability: null,
    value: 'Individual',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 52,
    contextStartIndex: 52,
    key: 'Governing State',
    pageNumber: 0,
    probability: null,
    value: 'Florida',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 1398,
    contextStartIndex: 1397,
    key: 'Spouse / Partner',
    pageNumber: 1,
    probability: null,
    value: 'Pauline Dixon',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 1199,
    contextStartIndex: 1196,
    key: 'Child',
    pageNumber: 0,
    probability: null,
    value: 'Diane T R Smith',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 527,
    contextStartIndex: 525,
    key: 'Child',
    pageNumber: 2,
    probability: null,
    value: 'Dora Jean Wilson-Dixon',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 527,
    contextStartIndex: 521,
    key: 'Child',
    pageNumber: 0,
    probability: null,
    value: 'Diane Tr Smith',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 529,
    contextStartIndex: 527,
    key: 'Child',
    pageNumber: 0,
    probability: null,
    value: 'Albert William Dixon',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 1386,
    contextStartIndex: 1372,
    key: 'Child',
    pageNumber: 0,
    probability: null,
    value: 'Andrea Zito',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 1386,
    contextStartIndex: 1372,
    key: 'Child',
    pageNumber: 5,
    probability: null,
    value: 'Elizabeth A Dodson',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 1226,
    contextStartIndex: 1224,
    key: 'Child in Law',
    pageNumber: 0,
    probability: null,
    value: 'Amber Spring Wilson',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 1362,
    contextStartIndex: 1360,
    key: 'Sibling',
    pageNumber: 0,
    probability: null,
    value: 'Elisabeth C Radson',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 1171,
    contextStartIndex: 1170,
    key: 'Initial Trustee',
    pageNumber: 1,
    probability: null,
    value: 'Pauline Dixon',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 1201,
    contextStartIndex: 1194,
    key: 'Successor Trustee',
    pageNumber: 0,
    probability: null,
    value: 'Diane T R Smith',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 1238,
    contextStartIndex: 1228,
    key: 'Second Successor Trustee',
    pageNumber: 0,
    probability: null,
    value: 'Amber Spring Wilson',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 1237,
    contextStartIndex: 1235,
    key: 'Pet Caretaker',
    pageNumber: 0,
    probability: null,
    value: 'Amber Spring Wilson',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: 141,
    contextStartIndex: 137,
    key: 'Credit Shelter Trust',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Marital Trust',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust for Descendants',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: 141,
    contextStartIndex: 137,
    key: 'Trust for Other Beneficiaries',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Residuary Outright to Beneficiary',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 528,
    contextStartIndex: 526,
    key: 'Primary Beneficiary - Specific Gift (Real Estate)',
    pageNumber: 0,
    probability: null,
    value: 'Albert William Dixon',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 528,
    contextStartIndex: 515,
    key: 'Primary Beneficiary - Specific Gift (Real Estate)',
    pageNumber: 2,
    probability: null,
    value: 'Dora Jean Wilson-Dixon',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 528,
    contextStartIndex: 515,
    key: 'Primary Beneficiary - Specific Gift (Real Estate)',
    pageNumber: 0,
    probability: null,
    value: 'Diane Tr Smith',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 1223,
    contextStartIndex: 1221,
    key: 'Contingent Beneficiary - Specific Gift (Real Estate)',
    pageNumber: 0,
    probability: null,
    value: 'Amber Spring Wilson',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 528,
    contextStartIndex: 526,
    key: 'Primary Beneficiary - Specific Gift',
    pageNumber: 0,
    probability: null,
    value: 'Albert William Dixon',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 528,
    contextStartIndex: 515,
    key: 'Primary Beneficiary - Specific Gift',
    pageNumber: 2,
    probability: null,
    value: 'Dora Jean Wilson-Dixon',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 528,
    contextStartIndex: 515,
    key: 'Primary Beneficiary - Specific Gift',
    pageNumber: 0,
    probability: null,
    value: 'Diane Tr Smith',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 1223,
    contextStartIndex: 1221,
    key: 'Contingent Beneficiary - Specific Gift',
    pageNumber: 0,
    probability: null,
    value: 'Amber Spring Wilson',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: 620,
    contextStartIndex: 618,
    key: 'Mandatory Income Distribution',
    pageNumber: 0,
    probability: null,
    value: 'Yes: Other / Undefined Interval',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Is there a Power of Appointment?',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: 541,
    contextStartIndex: 538,
    key: 'Trust Termination Event',
    pageNumber: 0,
    probability: null,
    value: 'Death Of Beneficiary',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Funding Directive',
    pageNumber: 0,
    probability: null,
    value: 'Other / Undefined',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: 528,
    contextStartIndex: 526,
    key: 'Beneficiary During Trust Period',
    pageNumber: 0,
    probability: null,
    value: 'Albert William Dixon',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Mandatory Income Distribution',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust Termination Event',
    pageNumber: 0,
    probability: null,
    value: 'Other',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 651,
    contextStartIndex: 634,
    key: 'Generation-Skipping Transfer Tax Provisions',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spendthrift Clause',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'No Contest Clause',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Conduit Trust)',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 143,
    contextStartIndex: 136,
    key: 'Retirement Benefits (Accumulation Trust)',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 1262,
    contextStartIndex: 1249,
    key: 'Special Needs Provision',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spousal Disclaimer Provision',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Digital Assets Powers',
    pageNumber: 4,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    contextEndIndex: 1404,
    contextStartIndex: 1403,
    key: 'Witness',
    pageNumber: 0,
    probability: null,
    value: 'Andrea Zito',
    valueType: 'String',
  },
];
