import { JobResultEntry } from '@wealthcom/visualizer';
// revocable_trusts_and_supporting_documents
// 'Individual or Joint Trust': 'Individual'
export const ESTER_IRT_3 = [
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Credit Shelter Trust',
    pageNumber: null,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust for Descendants',
    pageNumber: null,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: 1852,
    contextStartIndex: 1386,
    key: 'Trust for Other Beneficiaries',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Trust<PERSON>'s Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Residuary Outright to Beneficiary',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Overview',
    contextEndIndex: 408,
    contextStartIndex: 68,
    key: 'Document Type',
    pageNumber: null,
    probability: null,
    value: 'Trust',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 27992,
    contextStartIndex: 27630,
    key: 'Date of Signature',
    pageNumber: null,
    probability: null,
    value: 'February 08, 2019',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: 9208,
    contextStartIndex: 8856,
    key: 'Governing State',
    pageNumber: null,
    probability: null,
    value: 'New York',
    valueType: 'String',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trustor',
    pageNumber: null,
    probability: null,
    value: 'David K Donaldson Jr',
    valueType: 'Person',
  },
  {
    category: 'Overview',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Name',
    probability: null,
    value: 'The Myron E Wolf Family Trust',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 25698,
    contextStartIndex: 25253,
    key: 'Spouse / Partner',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 3613,
    contextStartIndex: 3259,
    key: 'Child',
    pageNumber: null,
    probability: null,
    value: 'Jennifer L Donaldson',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Child',
    pageNumber: null,
    probability: null,
    value: 'G Nicholas Gaetanos',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Child',
    pageNumber: null,
    probability: null,
    value: 'Benjamin N Gaetanos',
    valueType: 'Person',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Child in Law',
    pageNumber: null,
    probability: null,
    value: 'David Oliver Donaldson',
    valueType: 'String',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Parent',
    pageNumber: null,
    probability: null,
    value: 'Athena J Donaldson',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 25604,
    contextStartIndex: 25253,
    key: 'Executor',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'First Alternate Executor',
    pageNumber: null,
    probability: null,
    value: 'David Oliver Donaldson',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Second Alternate Executor',
    pageNumber: null,
    probability: null,
    value: 'John F Leone',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 2590,
    contextStartIndex: 2228,
    key: 'Guardian',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 26574,
    contextStartIndex: 26227,
    key: 'Initial Trustee',
    pageNumber: null,
    probability: null,
    value: 'G Nicholas Gaetanos',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 26801,
    contextStartIndex: 26453,
    key: 'Successor Trustee',
    pageNumber: null,
    probability: null,
    value: 'Benjamin N Gaetanos',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Third Successor Trustee',
    pageNumber: null,
    probability: null,
    value: 'Jennifer L Donaldson',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Fourth Successor Trustee',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Fifth Successor Trustee',
    pageNumber: null,
    probability: null,
    value: 'David Oliver Donaldson',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 25604,
    contextStartIndex: 25253,
    key: 'Trustee Appointer',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Investment Advisor',
    pageNumber: null,
    probability: null,
    value: 'John F Leone',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 2590,
    contextStartIndex: 2228,
    key: 'Pet Caretaker',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Residuary Outright to Beneficiary',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Marital Trust',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Credit Shelter Trust',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: "Survivor's Trust",
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust for Descendants',
    pageNumber: null,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust for Other Beneficiaries',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 3266,
    contextStartIndex: 2914,
    key: 'Primary Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 3672,
    contextStartIndex: 3259,
    key: 'Contingent Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: 'Benjamin N Gaetanos',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 3672,
    contextStartIndex: 3259,
    key: 'Contingent Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: 'G Nicholas Gaetanos',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 1492,
    contextStartIndex: 1115,
    key: 'Primary Beneficiary - Specific Gift (Real Estate)',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: 1492,
    contextStartIndex: 1115,
    key: 'Primary Beneficiary - Specific Gift',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Contingent Beneficiary - Specific Gift',
    pageNumber: null,
    probability: null,
    value: 'Jennifer L Donaldson',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    pageNumber: null,
    key: 'Beneficiary During Trust Period',
    probability: null,
    value: 'John Smith: 25%',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Mandatory Income Distribution',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'Death of Beneficiary',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'John Witmer: 15%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'NEVIN WITMER WOLF: 16%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'JAMES WITMER WOLF: 15%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'MARY ELIZABETH McCARDLE: 10%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    probability: null,
    value: 'CHARLOTTE SCHMID LUTZ: 11%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Funding Directive',
    pageNumber: null,
    probability: null,
    value: 'Fractional Share',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Name of the Credit Shelter Trust',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Beneficiary During Trust Period',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Mandatory Income Distribution',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'John Witmer: 15%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Funding Directive',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Survivor's Trust - Additional Details",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Name',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Survivor's Trust - Additional Details",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: "Beneficiaries: Survivor's Trust - Additional Details",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'John Witmer: 15%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Beneficiary During Trust Period',
    pageNumber: null,
    probability: null,
    value: "The Trustors' Descendants",
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Income Distribution',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Interim Distribution or Withdrawal Power',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'John Witmer: 15%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'Pedro Witmer: 15%',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: 1492,
    contextStartIndex: 1115,
    key: 'Beneficiary During Trust Period',
    pageNumber: null,
    probability: null,
    value: 'Dianne Civello',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: 7169,
    contextStartIndex: 6771,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'When Beneficiaries Reach Specific Age',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: 1886,
    contextStartIndex: 1486,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'Trust Beneficiary',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Income Distribution',
    pageNumber: null,
    probability: null,
    value: 'Yes: Other / Undefined Interval',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'John Witmer: 15%',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Generation-Skipping Transfer Tax Provisions',
    pageNumber: null,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spendthrift Clause',
    pageNumber: null,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'No Contest Clause',
    pageNumber: null,
    probability: null,
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 2590,
    contextStartIndex: 2228,
    key: 'Retirement Benefits (Conduit Trust)',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Accumulation Trust)',
    pageNumber: null,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Special Needs Provision',
    pageNumber: null,
    probability: null,
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 2590,
    contextStartIndex: 2228,
    key: 'Spousal Disclaimer Provision',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 2590,
    contextStartIndex: 2228,
    key: 'Digital Assets Powers',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    contextEndIndex: 29161,
    contextStartIndex: 28790,
    key: 'Attorney',
    pageNumber: null,
    probability: null,
    value: 'John F Leone',
    valueType: 'String',
  },
  {
    category: 'Other Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Notary',
    pageNumber: null,
    probability: null,
    value: 'Marian Parylo',
    valueType: 'String',
  },
  {
    category: 'Other Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Witness',
    pageNumber: null,
    probability: null,
    value: 'Katherine M Liebner',
    valueType: 'String',
  },
] as JobResultEntry[];
