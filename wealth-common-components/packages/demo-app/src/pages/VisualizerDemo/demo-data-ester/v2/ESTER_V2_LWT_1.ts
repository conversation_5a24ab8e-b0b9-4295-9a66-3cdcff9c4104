export const ESTER_V2_LWT_1 = [
  {
    category: 'Beneficiaries: Specific Gifts - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Ester™ Generated - Specific Gifts Summary',
    pageNumber: 0,
    probability: null,
    value:
      '*lorem Ipsum Dolor Sit Amet, Consectetur Adipiscing Elit, Sed Do Eiusmod Tempor Incididunt Ut Labore Et Dolore Magna Aliqua. Ut Enim Ad Minim Veniam*',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Document Type',
    pageNumber: 0,
    probability: null,
    value: 'Will',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 399,
    contextStartIndex: 0,
    key: 'Testator',
    pageNumber: 1,
    probability: null,
    value: '<PERSON>',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 399,
    contextStartIndex: 0,
    key: 'Testator',
    pageNumber: 1,
    probability: null,
    value: 'Carolyn A. Masline',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 70533,
    contextStartIndex: 70134,
    key: 'Governing State',
    pageNumber: 27,
    probability: null,
    value: 'California',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 84998,
    contextStartIndex: 84993,
    key: 'Date of Signature',
    pageNumber: 31,
    probability: null,
    value: 'March 26 2015',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 85689,
    contextStartIndex: 85294,
    key: 'Spouse / Partner',
    pageNumber: 0,
    probability: null,
    value: 'Carolyn A. Masline',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 1592,
    contextStartIndex: 1193,
    key: 'Child',
    pageNumber: 1,
    probability: null,
    value: 'John Anthony Masline',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 1592,
    contextStartIndex: 1193,
    key: 'Child',
    pageNumber: 1,
    probability: null,
    value: 'Ann Kathleen Masline',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 1592,
    contextStartIndex: 1193,
    key: 'Child',
    pageNumber: 1,
    probability: null,
    value: 'Steven Edward Masline',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Initial Trustee',
    pageNumber: null,
    probability: null,
    value: 'AAA',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Specific Gifts',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Residuary Outright to Beneficiary',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: 8728,
    contextStartIndex: 8334,
    key: 'Marital Trust',
    pageNumber: 5,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: 11025,
    contextStartIndex: 10627,
    key: 'Credit Shelter Trust',
    pageNumber: 6,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust for Other Beneficiaries',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Specific Gift (Cash)',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Specific Gift (Cash)',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Specific Gift (Real Estate)',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Specific Gift (Real Estate)',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Specific Gift',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Specific Gift',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: 'This is the AI-generated summary. You can edit or update this content.',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Name of the Marital Trust',
    pageNumber: null,
    probability: null,
    value: 'Here is the Name of the Marital Trust',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Beneficiary During Trust Period',
    pageNumber: null,
    probability: null,
    value: 'Here is the Beneficiary During Trust Period',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Mandatory Income Distribution',
    pageNumber: null,
    probability: null,
    value: 'Marital-Trust-F',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'Marital-Trust-F',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'Marital-Trust-F',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Funding Directive',
    pageNumber: null,
    probability: null,
    value: 'Marital-Trust-F',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Ultimate Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: 'This is the AI-generated summary. You can edit or update this content.**Bold Text**```javascript// code hereconst a = 1;```',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Name of the Credit Shelter Trust',
    pageNumber: null,
    probability: null,
    value: 'Credit-Shelter-Trust-f',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Beneficiary During Trust Period',
    pageNumber: null,
    probability: null,
    value: 'Credit-Shelter-Trust-f',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Mandatory Income Distribution',
    pageNumber: null,
    probability: null,
    value: 'Credit-Shelter-Trust-f',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'Credit-Shelter-Trust-f',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'Credit-Shelter-Trust-f',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Funding Directive',
    pageNumber: null,
    probability: null,
    value: 'Credit-Shelter-Trust-f',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Ultimate Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: 'This is the AI-generated summary. You can edit or update this content.Yes/No',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'No Contest Clause',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 60211,
    contextStartIndex: 59814,
    key: 'Spendthrift Clause',
    pageNumber: 25,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 80554,
    contextStartIndex: 80155,
    key: 'Generation-Skipping Transfer Tax Provisions',
    pageNumber: 29,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Conduit Trust)',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Accumulation Trust)',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Special Needs Provision',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 9918,
    contextStartIndex: 9523,
    key: 'Spousal Disclaimer Provision',
    pageNumber: 5,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Digital Assets Powers',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Other Details',
    contextEndIndex: 88092,
    contextStartIndex: 87693,
    key: 'Attorney',
    pageNumber: 0,
    probability: null,
    value: 'Jennifer H. Friedman',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Other Details',
    contextEndIndex: 89974,
    contextStartIndex: 89664,
    key: 'Notary',
    pageNumber: 12,
    probability: null,
    value: 'Cheryl Gardner',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Beneficiary During Trust Period',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Mandatory Income Distribution',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
];
