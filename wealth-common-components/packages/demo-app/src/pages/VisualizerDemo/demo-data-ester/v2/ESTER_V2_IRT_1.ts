// revocable_trusts_and_supporting_documents
// 'Individual or Joint Trust': 'Individual'
export const ESTER_V2_IRT_1 = [
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Document Type',
    pageNumber: 0,
    probability: null,
    value: 'Trust',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Restatement or Amendment',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 4391,
    contextStartIndex: 4043,
    key: 'Trust Name',
    pageNumber: 3,
    probability: null,
    value: 'Revocable Trust Agreement',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Individual or Joint Trust',
    pageNumber: 0,
    probability: null,
    value: 'Individual',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 14540,
    contextStartIndex: 14179,
    key: 'Trustor',
    pageNumber: 0,
    probability: null,
    value: 'Frank Distel',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 12589,
    contextStartIndex: 12190,
    key: 'Governing State',
    pageNumber: 9,
    probability: null,
    value: 'Florida',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust Creation Date',
    pageNumber: 0,
    probability: null,
    value: 'February 4 1993',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Date of Signature',
    pageNumber: null,
    probability: null,
    value: 'Look here',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 6674,
    contextStartIndex: 6276,
    key: 'Child',
    pageNumber: 5,
    probability: null,
    value: 'John Franklin Distel',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 6674,
    contextStartIndex: 6276,
    key: 'Child',
    pageNumber: 5,
    probability: null,
    value: 'Mary Susan Madsen',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Spouse / Partner',
    pageNumber: null,
    probability: null,
    value: 'Look here',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Sibling',
    pageNumber: null,
    probability: null,
    value: 'Sib',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Child',
    pageNumber: null,
    probability: null,
    value: 'Child',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Friend',
    pageNumber: null,
    probability: null,
    value: 'Friend',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Child in Law',
    pageNumber: null,
    probability: null,
    value: 'in law child',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Grandchild',
    pageNumber: null,
    probability: null,
    value: 'GC',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Niece / Nephew',
    pageNumber: null,
    probability: null,
    value: 'nie',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Parent',
    pageNumber: null,
    probability: null,
    value: 'Parent',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Pet',
    pageNumber: null,
    probability: null,
    value: 'Luna',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Excluded Individual',
    pageNumber: null,
    probability: null,
    value: 'Ex In',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Parent in Law',
    pageNumber: null,
    probability: null,
    value: 'Parent in law',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Parent',
    pageNumber: null,
    probability: null,
    value: 'Parent 2',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Initial Trustee',
    pageNumber: 0,
    probability: null,
    value: 'Frank Distel',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: 12189,
    contextStartIndex: 11795,
    key: 'Successor Trustee',
    pageNumber: 9,
    probability: null,
    value: 'John Franklin Distel',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: 64,
    contextStartIndex: 62,
    key: 'Initial Trustee',
    pageNumber: 0,
    probability: 0.6972298837633069,
    value: 'Alice Farny Smith',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 64,
    contextStartIndex: 58,
    key: 'Initial Trustee',
    pageNumber: 1,
    probability: 0.***************,
    value: 'W Seymour Smith',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 821,
    contextStartIndex: 815,
    key: 'Successor Trustee',
    pageNumber: 5,
    probability: 0.****************,
    value: 'William A Muirhead',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 830,
    contextStartIndex: 818,
    key: 'Second Successor Trustee',
    pageNumber: 3,
    probability: 0.****************,
    value: 'Guaranty Bank And Trust Company',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Third Successor Trustee',
    pageNumber: 0,
    probability: 0,
    value: 'No Information Found',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 2115,
    contextStartIndex: 2098,
    key: 'Fourth Successor Trustee',
    pageNumber: 0,
    probability: 0.****************,
    value: 'Patricia Asch',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: 2097,
    contextStartIndex: 2075,
    key: 'Fifth Successor Trustee',
    pageNumber: 0,
    probability: 0.****************,
    value: 'Peter S Wood',
    valueType: 'String',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Pet Caretaker',
    pageNumber: null,
    probability: null,
    value: 'Look here',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Second Successor Trustee',
    pageNumber: null,
    probability: null,
    value: '2 SST',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Protector',
    pageNumber: null,
    probability: null,
    value: 'TP',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Executor',
    pageNumber: null,
    probability: null,
    value: 'EXec',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trustee Appointer',
    pageNumber: null,
    probability: null,
    value: 'Tr App',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Second Alternate Executor',
    pageNumber: null,
    probability: null,
    value: '2 AE',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'First Alternate Executor',
    pageNumber: null,
    probability: null,
    value: '1 AE',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Successor Trustee Appointer',
    pageNumber: null,
    probability: null,
    value: '2 TA',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Guardian',
    pageNumber: null,
    probability: null,
    value: 'Guard',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Temporary Guardian',
    pageNumber: null,
    probability: null,
    value: 'T Guard',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'First Alternate Guardian',
    pageNumber: null,
    probability: null,
    value: '1 A Guard',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Investment Advisor',
    pageNumber: null,
    probability: null,
    value: 'Invest Ad',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Second Alternate Guardian',
    pageNumber: null,
    probability: null,
    value: '2 AGuard',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Pet Caretaker',
    pageNumber: null,
    probability: null,
    value: 'Pet',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Specific Gifts',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Residuary Outright to Beneficiary',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Marital Trust',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Credit Shelter Trust',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: "Survivor's Trust",
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust for Other Beneficiaries',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: '{"name":"Res Prima","recipientType":"Charity","percentage":"33","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: '{"name":"Con Res","recipientType":"Entity","percentage":"22","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: '{"name":"prima another","recipientType":"Trust","percentage":"7","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: '{"name":"Prson here","recipientType":"Person","percentage":"9","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Specific Gifts - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Specific Gift (Cash)',
    pageNumber: null,
    probability: null,
    value: '{"name":"Look here","recipientType":"Entity","percentage":"","description":"Cash","amount":"555"}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Specific Gifts - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Specific Gift (Cash)',
    pageNumber: null,
    probability: null,
    value: '{"name":"Prima Cash","recipientType":"Trust","percentage":"","description":"Cashii","amount":"99"}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Specific Gifts - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Specific Gift (Real Estate)',
    pageNumber: null,
    probability: null,
    value: '{"name":"Con Real","recipientType":"Person","percentage":"","description":"364 kjhkjh k4ihkh","amount":"888888"}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Specific Gifts - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Contingent Beneficiary - Specific Gift',
    pageNumber: null,
    probability: null,
    value: '{"name":"Con Gift","recipientType":"Charity","percentage":"","description":"desc","amount":"888"}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Specific Gifts - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Specific Gift (Real Estate)',
    pageNumber: null,
    probability: null,
    value: '{"name":"prima RE","recipientType":"Person","percentage":"","description":"34 kdjfhkh","amount":"66666"}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Specific Gifts - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Specific Gift',
    pageNumber: null,
    probability: null,
    value: '{"name":"PRima SG","recipientType":"Entity","percentage":"","description":"7676","amount":"8888"}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Specific Gifts - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: 'Handmade summary from QA',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Name of the Marital Trust',
    pageNumber: null,
    probability: null,
    value: 'Name',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Beneficiary During Trust Period',
    pageNumber: null,
    probability: null,
    value: 'Ben DTP',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Income Distribution',
    pageNumber: null,
    probability: null,
    value: 'ID',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    pageNumber: null,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: '{"name":"Ulitmate Ben","recipientType":"Trust","percentage":"60","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: '{"name":"UB 2","recipientType":"Entity","percentage":"6","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: '{"name":"UB3","recipientType":"Person","percentage":"29","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: '{"name":"UB4","recipientType":"Charity","percentage":"3","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Ultimate Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: 'HAnd-made',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'TTE',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Distribution Event',
    pageNumber: null,
    probability: null,
    value: 'Hello TDE',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Ultimate Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: 'not ester summary ',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: '{"name":"UB","recipientType":"Person","percentage":"40","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Beneficiary During Trust Period',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Ultimate Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: 'not ester summary ',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  // {
  //   category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
  //   contextEndIndex: null,
  //   contextStartIndex: null,
  //   key: 'Ultimate Beneficiaries Upon Trust Termination',
  //   pageNumber: null,
  //   probability: null,
  //   value: '{"name":"Ub","recipientType":"Trust","percentage":"88","description":""}',
  //   valueType: 'String',
  //   __typename: 'JobResultEntry',
  // },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Principal Distribution',
    pageNumber: null,
    probability: null,
    value: 'PD',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'No Contest Clause',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spendthrift Clause',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Generation-Skipping Transfer Tax Provisions',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Conduit Trust)',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Accumulation Trust)',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 2454,
    contextStartIndex: 2057,
    key: 'Special Needs Provision',
    pageNumber: 2,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spousal Disclaimer Provision',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Digital Assets Powers',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Other Details',
    contextEndIndex: 14933,
    contextStartIndex: 14541,
    key: 'Notary',
    pageNumber: 12,
    probability: null,
    value: 'Nanette D. Langenderfer',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
];
