// revocable_trusts_and_supporting_documents
// 'Individual or Joint Trust': 'Joint'
export const ESTER_V2_JRT_1 = [
  {
    category: 'Beneficiaries: Specific Gifts - Additional Details',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Ester™ Generated - Specific Gifts Summary',
    pageNumber: 0,
    probability: null,
    value:
      '*lorem Ipsum Dolor Sit Amet, Consectetur Adipiscing Elit, Sed Do Eiusmod Tempor Incididunt Ut Labore Et Dolore Magna Aliqua. Ut Enim Ad Minim Veniam*',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Document Type',
    pageNumber: 0,
    probability: null,
    value: 'Trust',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Restatement or Amendment',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 392,
    contextStartIndex: 0,
    key: 'Trust Name',
    pageNumber: 1,
    probability: null,
    value: 'The Theodore And Anna Gonzalez Revocable Living Trust',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 19240,
    contextStartIndex: 18841,
    key: 'Individual or Joint Trust',
    pageNumber: 11,
    probability: null,
    value: 'Joint',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 59768,
    contextStartIndex: 59379,
    key: 'Trustor',
    pageNumber: 32,
    probability: null,
    value: 'Theodore Ralph Gonzalez',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 59768,
    contextStartIndex: 59379,
    key: 'Trustor',
    pageNumber: 32,
    probability: null,
    value: 'Anna Marie Gonzalez',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: 28942,
    contextStartIndex: 28546,
    key: 'Governing State',
    pageNumber: 0,
    probability: null,
    value: 'Texas',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Overview',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust Creation Date',
    pageNumber: 0,
    probability: null,
    value: 'June 27 2018',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spouse / Partner',
    pageNumber: 0,
    probability: null,
    value: 'Theodore Ralph Gonzalez',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spouse / Partner',
    pageNumber: 0,
    probability: null,
    value: 'Anna Marie Gonzalez',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 63544,
    contextStartIndex: 63216,
    key: 'Child',
    pageNumber: 34,
    probability: null,
    value: 'Theodore Gonzalez, Jr.',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Family and Friends',
    contextEndIndex: 63544,
    contextStartIndex: 63216,
    key: 'Child',
    pageNumber: 34,
    probability: null,
    value: 'Joanna Claire Wordell',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Initial Trustee',
    pageNumber: 0,
    probability: null,
    value: 'Theodore Ralph Gonzalez',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Initial Trustee',
    pageNumber: 0,
    probability: null,
    value: 'Anna Marie Gonzalez',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: 21211,
    contextStartIndex: 20817,
    key: 'Successor Trustee',
    pageNumber: 11,
    probability: null,
    value: 'Fidelity Personal Trust Company, Fsb (fptc)',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Key People',
    contextEndIndex: 21211,
    contextStartIndex: 20817,
    key: 'Second Successor Trustee',
    pageNumber: 11,
    probability: null,
    value:
      'A Successor Trustee Shall Be Appointed in The Manner And With The Qualifications Pursuant To The Terms of This Trust Agreement Set Forth in The Paragraph Below Entitled "appointment of Successor Trustees"',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Specific Gifts',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Residuary Outright to Beneficiary',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Marital Trust',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: 58332,
    contextStartIndex: 57936,
    key: 'Credit Shelter Trust',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: "Survivor's Trust",
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Disposition Upon Trustor's Death",
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Trust for Other Beneficiaries',
    pageNumber: 0,
    probability: null,
    value: 'Yes',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Primary Beneficiary - Residuary Estate',
    pageNumber: null,
    probability: null,
    value: 'Outright to Beneficiary fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: 'This is the AI-generated summary. You can edit or update this content.Outright to Beneficiary fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Name of the Credit Shelter Trust',
    pageNumber: null,
    probability: null,
    value: 'Credit Shelter Trust Name',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'Credit Shelter Trust fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Credit Shelter Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Ultimate Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: 'This is the AI-generated summary. You can edit or update this content.Credit Shelter Trust',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'No Contest Clause',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: 33950,
    contextStartIndex: 33553,
    key: 'Spendthrift Clause',
    pageNumber: 18,
    probability: null,
    value: 'Yes',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Generation-Skipping Transfer Tax Provisions',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Conduit Trust)',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Retirement Benefits (Accumulation Trust)',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Special Needs Provision',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Spousal Disclaimer Provision',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Important Provisions',
    contextEndIndex: -1,
    contextStartIndex: -1,
    key: 'Digital Assets Powers',
    pageNumber: 0,
    probability: null,
    value: 'No',
    valueType: 'Person',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Other Details',
    contextEndIndex: 63214,
    contextStartIndex: 63134,
    key: 'Attorney',
    pageNumber: 38,
    probability: null,
    value: 'The J. D. Wilson Law Firm, Pllc.',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Other Details',
    contextEndIndex: 60345,
    contextStartIndex: 59945,
    key: 'Notary',
    pageNumber: 0,
    probability: null,
    value: 'Joshua D. Wilson',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'NEVIN WITMER WOLF: 16%',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: '{"name":" HERBERT JAMES SCHMID, JR.","recipientType":"Person","percentage":"6","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Marital Trust - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Ultimate Beneficiaries Summary --Ignore--',
    pageNumber: null,
    probability: null,
    value: 'This is the AI-generated summary. You can edit or update this content.Marital Trust fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Survivor's Trust - Additional Details",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: "Survivor's Trust fff",
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: "Beneficiaries: Survivor's Trust - Additional Details",
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ester™ Generated - Ultimate Beneficiaries Summary',
    pageNumber: null,
    probability: null,
    value: "This is the AI-generated summary. You can edit or update this content.Survivor's Trust fff",
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'John Witmer: 15%',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Beneficiary During Trust Period',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Mandatory Income Distribution',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Is there a Power of Appointment?',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Trust Termination Event',
    pageNumber: null,
    probability: null,
    value: 'Trust for Other Beneficiaries fff',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    contextEndIndex: null,
    contextStartIndex: null,
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    pageNumber: null,
    probability: null,
    value: '{"name":"UB4","recipientType":"Charity","percentage":"3","description":""}',
    valueType: 'String',
    __typename: 'JobResultEntry',
  },
];
