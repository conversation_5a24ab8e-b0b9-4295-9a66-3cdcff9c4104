import { JobResultEntry } from '@wealthcom/visualizer';
// last_will_and_testament
export const ESTER_POW_2 = [
  {
    category: 'Overview',
    key: 'Document Type',
    value: 'Will',
    valueType: 'String',
  },
  {
    category: 'Overview',
    key: 'Date of Signature',
    value: 'August 29, 2023',
    valueType: 'String',
  },
  {
    category: 'Overview',
    key: 'Governing State',
    value: 'New York',
    valueType: 'String',
  },
  {
    category: 'Overview',
    key: 'Testator',
    value: 'Virginia A Charleson',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Spouse / Partner',
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Child',
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Child',
    value: '<PERSON>',
    valueType: 'Person',
  },
  {
    category: 'Family',
    key: 'Grandchild',
    value: '<PERSON>',
    valueType: 'String',
  },
  {
    category: 'Family',
    key: 'Child in Law',
    value: '<PERSON>',
    valueType: 'String',
  },
  {
    category: 'Family',
    key: 'Niece / Nephew',
    value: '<PERSON>',
    valueType: 'String',
  },
  {
    category: 'Family',
    key: 'Niece / Nephew',
    value: 'Susan <PERSON>on',
    valueType: 'String',
  },
  {
    category: 'Key People',
    key: 'Executor',
    value: 'Henry Charleson',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'First Alternate Executor',
    value: 'Joan Theresa Charleson',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Second Alternate Executor',
    value: 'Nancy Ann Charleson',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Guardian',
    value: 'William Sinon',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'Guardian',
    value: 'Mary C Sinon',
    valueType: 'Person',
  },
  {
    category: 'Key People',
    key: 'First Alternate Guardian',
    value: 'Joan Theresa Charleson',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Credit Shelter Trust',
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Marital Trust',
    value: 'No',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Trust for Descendants',
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Trust for Other Beneficiaries',
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: "Beneficiaries: Disposition Upon Testator's Death",
    key: 'Residuary Outright to Beneficiary',
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    key: 'Primary Beneficiary - Residuary Estate',
    value: 'Henry Charleson',
    valueType: 'String',
  },
  {
    category: 'Beneficiaries: Outright to Beneficiary - Additional Details',
    key: 'Contingent Beneficiary - Specific Gift (Real Estate)',
    value: 'Nancy Ann Charleson',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Income Distribution',
    value:
      'If Any Property Including Income Hereunder Shall Be- Come Payable Or Distributable To Any Infant Such Property Shall Vest In Such Infant',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Is there a Power of Appointment?',
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Interim Distribution or Withdrawal Power',
    value:
      'If Any Property Including Income Hereunder Shall Be- Come Payable Or Distributable To Any Infant Such Property Shall Vest In Such Infant',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Trust Termination Event',
    value: 'If My Husband Shall Not Survive Me',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    value: 'My Issue Surviving At My Death',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    value: 'Absolutely',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Descendants - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    value: 'Per Stirpes',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Income Distribution',
    value:
      'If Any Property Including Income Hereunder Shall Be- Come Payable Or Distributable To Any Infant Such Property Shall Vest In Such Infant',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Is there a Power of Appointment?',
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Trust Termination Event',
    value: 'If My Husband Shall Not Survive Me',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    value: 'My Issue Surviving At My Death',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    value: 'Absolutely',
    valueType: 'Person',
  },
  {
    category: 'Beneficiaries: Trust for Other Beneficiaries - Additional Details',
    key: 'Ultimate Beneficiaries Upon Trust Termination',
    value: 'Per Stirpes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Generation-Skipping Transfer Tax Provisions',
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Spendthrift Clause',
    value: 'Yes',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    key: 'No Contest Clause',
    value: 'No',
    valueType: 'String',
  },
  {
    category: 'Important Provisions',
    key: 'Retirement Benefits (Conduit Trust)',
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Retirement Benefits (Accumulation Trust)',
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Special Needs Provision',
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Spousal Disclaimer Provision',
    value: 'No',
    valueType: 'Person',
  },
  {
    category: 'Important Provisions',
    key: 'Digital Assets Powers',
    value: 'Yes',
    valueType: 'Person',
  },
  {
    category: 'Other Details',
    key: 'Notary',
    value: 'Mary C Sinon',
    valueType: 'Person',
  },
] as JobResultEntry[];
