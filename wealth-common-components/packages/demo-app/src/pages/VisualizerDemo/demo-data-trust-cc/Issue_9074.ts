import { Asset, ContactCard, TrustInformation } from '@wealthcom/visualizer';

export const assets = [
  {
    assetDescription: null,
    assetId: 'MRUqn4uQkUiTq9yLUblpKQ',
    assetInfo:
      '{"nickname":"P1-222","descriptionEstatePlan":"","estimateValue":50000.0,"purchaseCost":0.0,"asOfDate":"2024-07-22T08:41:57.842+00:00","isFavorite":false}',
    assetInfoType: 'ManualPersonalInventory',
    assetMask: null,
    assetName: null,
    assetOwnerName: null,
    balanceAsOf: '2024-07-22T08:42:19+00:00',
    balanceCostBasis: 0,
    balanceCostFrom: 'UserManual',
    balanceCurrent: 50000,
    balanceFrom: 'UserManual',
    balancePrice: null,
    balancePriceFrom: 'UserManual',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'fb79b771-bf14-48f8-a601-dcdbf31da9ec',
    creationDate: '2024-07-22T08:42:19+00:00',
    currencyCode: null,
    deactivateBy: null,
    descriptionEstatePlan: '',
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: 101,
    institutionName: null,
    integration: null,
    integrationAccountId: null,
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: null,
    lastUpdate: '2024-07-22T08:41:58+00:00',
    lastUpdateAttempt: '2024-07-22T08:42:19+00:00',
    logoName: null,
    modificationDate: null,
    nextUpdate: null,
    nickname: 'P1-222',
    note: null,
    noteDate: null,
    ownership: null,
    primaryAssetCategory: 'OtherProperty',
    status: null,
    statusCode: null,
    userInstitutionId: 'v45R7CKAvEaOk2rfmomp9A',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: null,
    vendorResponseType: 'Other',
    wealthAssetType: 'PersonalInventory',
    wid: '41b7f726-8564-4d8e-961f-7fd622f4a08c',
  },
] as Asset[];

export const contacts = [
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2024-07-22T03:40:52',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: '<EMAIL>',
    firstName: '9074',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'fb79b771-bf14-48f8-a601-dcdbf31da9ec',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: true,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-07-22T03:40:52',
    nickname: null,
    notes: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: '41b7f726-8564-4d8e-961f-7fd622f4a08c',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
    creationDate: '2024-07-22T08:42:34',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '78dd28c5-09c7-4b5f-af24-641d018d26ec',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: '',
    legalName: 'Gifts',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-07-22T08:42:34',
    nickname: '',
    notes: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Joint',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: null,
      isGrantorTrust: true,
      isGenerationSkippingTransferTaxExempt: null,
      currentBeneficiaries: null,
      currentTrustees: null,
      keyPeople: null,
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: {
        isDistributedEvenly: true,
        hasContingentBeneficiaries: false,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: [
          {
            contactId: 'fb79b771-bf14-48f8-a601-dcdbf31da9ec',
            distributionPercentage: 100.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
          },
        ],
        hasGiftContingentBeneficiaries: null,
        hasSpecificGifts: true,
        cashGifts: [
          {
            contactId: 'fb79b771-bf14-48f8-a601-dcdbf31da9ec',
            value: 40003,
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: null,
          },
        ],
        assetGifts: [
          {
            contactId: 'fb79b771-bf14-48f8-a601-dcdbf31da9ec',
            assetId: 'MRUqn4uQkUiTq9yLUblpKQ',
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: null,
          },
        ],
      },
      survivingSpouseOutright: null,
      survivingSpouseOutrightPercentageOfResiduary: null,
      survivingSpouseInTrust: {
        percentageOfResiduary: 100.0,
        maritalTrust: null,
        creditShelterTrust: null,
        survivorTrust: {
          distributionPercentage: null,
          name: 'Survivor Trust 1',
          beneficiariesDuringTrustPeriod: null,
          fundingDirectiveType: 'remainderAfterPecuniaryAmountToCreditShelterTrust',
          fundingDirectiveAmount: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        hasDifferentSecondLevelDistributions: null,
      },
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: null,
      otherBeneficiariesInTrust: null,
      secondDeathBeneficiariesOutrightPercentageOfResiduary: 100.0,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrust: null,
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: null,
    } as TrustInformation),
    trustType: 'RevocableTrust',
    trustors: null,
    type: 'Trust',
    wid: '41b7f726-8564-4d8e-961f-7fd622f4a08c',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
    creationDate: '2024-07-22T13:49:13',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'd3d5d21a-ad1f-4719-b46a-7acd91a08b08',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: '',
    legalName: 'IRT gifts',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-07-22T13:49:13',
    nickname: '',
    notes: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Individual',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: null,
      isGrantorTrust: true,
      isGenerationSkippingTransferTaxExempt: null,
      currentBeneficiaries: null,
      currentTrustees: null,
      keyPeople: null,
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: {
        isDistributedEvenly: true,
        hasContingentBeneficiaries: false,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: [
          {
            contactId: 'fb79b771-bf14-48f8-a601-dcdbf31da9ec',
            distributionPercentage: 100.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
          },
        ],
        hasGiftContingentBeneficiaries: null,
        hasSpecificGifts: true,
        cashGifts: [
          {
            contactId: 'fb79b771-bf14-48f8-a601-dcdbf31da9ec',
            value: 232354,
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: null,
          },
        ],
        assetGifts: [
          {
            contactId: 'fb79b771-bf14-48f8-a601-dcdbf31da9ec',
            assetId: 'MRUqn4uQkUiTq9yLUblpKQ',
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: null,
          },
        ],
      },
      survivingSpouseOutright: null,
      survivingSpouseOutrightPercentageOfResiduary: null,
      survivingSpouseInTrust: {
        percentageOfResiduary: null,
        maritalTrust: {
          distributionPercentage: null,
          name: '100',
          beneficiariesDuringTrustPeriod: null,
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        creditShelterTrust: null,
        survivorTrust: null,
        hasDifferentSecondLevelDistributions: null,
      },
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: null,
      otherBeneficiariesInTrust: null,
      secondDeathBeneficiariesOutrightPercentageOfResiduary: 100.0,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrust: null,
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: null,
    } as TrustInformation),
    trustType: 'RevocableTrust',
    trustors: null,
    type: 'Trust',
    wid: '41b7f726-8564-4d8e-961f-7fd622f4a08c',
  },
] as ContactCard[];
