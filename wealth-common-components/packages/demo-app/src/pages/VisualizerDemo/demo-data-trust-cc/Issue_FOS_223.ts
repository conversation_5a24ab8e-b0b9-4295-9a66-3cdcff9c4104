import { Asset, ContactCard } from '@wealthcom/visualizer';

export const assets = [] as Asset[];

export const contacts = [
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'California',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2024-11-18T22:00:36',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '<EMAIL>',
    esterJobId: null,
    familyName: null,
    firstName: '9674',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'd899cd6f-4bbc-410b-9ca1-93f4edd68617',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: true,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-11-18T22:00:36',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: { id: '67f33567-48d1-417f-a525-c0b2516b9533', name: 'Spouse Test' },
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: [{ id: '67f33567-48d1-417f-a525-c0b2516b9533', name: 'Spouse Test' }],
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-17T21:00:43',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Raul Test',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '67f33567-48d1-417f-a525-c0b2516b9533',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: 'L',
    legalName: 'Raul Test',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-05-21T18:46:28',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'CRAT',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: null,
      isGrantorTrust: null,
      isGenerationSkippingTransferTaxExempt: false,
      currentBeneficiaries: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: null,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: [
          {
            contactId: '103bd4ec-3902-4fbb-b17c-fc31d7e38331',
            distributionPercentage: 100.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
        ],
        hasGiftContingentBeneficiaries: null,
        hasSpecificGifts: null,
        cashGifts: null,
        assetGifts: null,
        otherGifts: null,
      },
      currentTrustees: null,
      keyPeople: null,
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: false,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: null,
        hasGiftContingentBeneficiaries: false,
        hasSpecificGifts: null,
        cashGifts: null,
        assetGifts: null,
        otherGifts: null,
      },
      associatedTrustForPourOverWill: null,
      survivingSpouseOutright: null,
      survivingSpouseOutrightPercentageOfResiduary: null,
      survivingSpouseInTrust: null,
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: null,
      otherBeneficiariesInTrust: null,
      secondDeathBeneficiariesOutrightPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrust: null,
      secondDeathCustom: null,
      annuityDetails: {
        term: null,
        rate: null,
        recipient: null,
        recipients: null,
        payments: [{ year: 5, date: '2025-01-01T00:00:00', amount: 123.0 }],
      },
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: [
        {
          distributionType: 'other',
          otherDistributionDescription:
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce molestie mollis enim id euismod. Maecenas in est in ex mattis blandit quis ut lorem. Etiam non nisi porttitor, venenatis massa in, gravida ante. Sed at dictum lorem, quis accumsan ipsum. Morbi feugiat aliquam libero eget ultricies. Quisque non orci ut elit ultricies ultrices. Sed et quam condimentum, facilisis orci placerat, sollicitudin odio. Etiam tristique purus id est semper, id consectetur ante vestibulum. Maecenas sed dolor dictum, sodales diam quis, luctus est. Curabitur sem ex, ornare congue blandit non, fringilla rhoncus eros.  Mauris mattis tellus nec condimentum placerat. Vestibulum gravida urna nec laoreet viverra. Integer hendrerit interdum metus at dictum. Donec dapibus diam vitae nisl placerat vestibulum. Proin hendrerit eros ullamcorper, porta lacus sed, efficitur urna. Praesent elementum dapibus dui, ut efficitur augue blandit ut. Mauris fermentum, dui ut suscipit lacinia, dolor ex vehicula tellus, id tempor ex quam sit amet ex. Nullam ac elementum dui. Nam accumsan at justo vitae pretium. Duis tempor lobortis mi vitae accumsan. Nam porta, ex vitae fringilla dignissim, tellus sem iaculis velit, sit amet fringilla est ante sed mauris. Nulla pharetra, nunc ac placerat rutrum, erat erat ullamcorper massa, sit amet varius eros lorem a urna. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent in tellus rhoncus, eleifend velit eget, accumsan elit. Morbi iaculis mauris sed malesuada tincidunt.  Quisque non elit quis ex porta varius. Donec sit amet diam pretium, vulputate augue vitae, volutpat turpis. Suspendisse molestie volutpat erat, tincidunt egestas urna porta et. Pellentesque egestas sollicitudin massa et semper. Aliquam luctus congue quam, sit amet tristique sapien interdum at. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Suspendisse in malesuada mi. Duis tellus libero, accumsan quis odio in, accumsan dignissim orci. Ut massa nisi, tempor id venenatis sit amet, pulvinar gravida lacus. Donec maximus dapibus ipsum nec dictum.  Ut mi est, sagittis at varius vel, faucibus a nibh. Cras ligula elit, pharetra vitae consequat non, consequat id est. Aliquam volutpat fringilla elit non condimentum. Cras congue arcu neque, in molestie tortor molestie ut. Curabitur suscipit eu tortor ut feugiat. In hac habitasse platea dictumst. Etiam ullamcorper sem tortor, eu laoreet orci aliquet et. Sed libero justo, tincidunt et vehicula eget, efficitur vel nunc. Nullam quis purus sapien. Cras in tincidunt purus, et convallis orci. Aenean purus justo, porttitor et elit non, commodo scelerisque ipsum. In sagittis cursus dui, sed porta orci viverra eu. Integer sagittis vehicula risus, eu fermentum felis dapibus id. Nullam sapien ipsum, luctus sit amet tincidunt sodales, pharetra ac quam. Mauris et orci massa.  Maecenas euismod libero at eros elementum, eget aliquet ex feugiat. Ut condimentum ultrices lacus, quis hendrerit enim volutpat nec. Proin consectetur blandit finibus. Sed tincidunt varius semper. Duis ut tristique dolor, a euismod tellus. Suspendisse ac ullamcorper purus, quis egestas erat. Morbi a nibh euismod, fringilla enim vitae, semper lorem. Ut maximus nulla eu justo viverra lacinia dapibus vitae nisi. Donec vehicula metus ut convallis eleifend.',
        },
      ],
      principalDistribution:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce molestie mollis enim id euismod. Maecenas in est in ex mattis blandit quis ut lorem. Etiam non nisi porttitor, venenatis massa in, gravida ante. Sed at dictum lorem, quis accumsan ipsum. Morbi feugiat aliquam libero eget ultricies. Quisque non orci ut elit ultricies ultrices. Sed et quam condimentum, facilisis orci placerat, sollicitudin odio. Etiam tristique purus id est semper, id consectetur ante vestibulum. Maecenas sed dolor dictum, sodales diam quis, luctus est. Curabitur sem ex, ornare congue blandit non, fringilla rhoncus eros.\\n\\n\\n',
      distributionEvents: null,
      distributionEventsV2: [],
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      hasNoContestClause: null,
      hasSpendThriftClause: null,
      hasSpecialNeedsProvision: null,
      hasSpousalDisclaimer: null,
      hasQualifiedDomesticTrust: null,
      hasQualifiedSubChapterProvision: null,
      hasDiscretionaryDistributionProvision: null,
      hasDigitalAssetsPowers: null,
      hasGstProvision: null,
      hasConduitTrust: null,
      hasAccumulationTrust: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: null,
      displayInformation: { information: null, displayInformationType: 'Notes' },
    }),
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:38:29',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'Fredrick D.',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '6ec157d3-6bfc-480d-ae6e-66a2b850cc9b',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: 'Cogburn',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-06-02T15:30:22',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:38:30',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Carol J.',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'ea3da758-f0a8-4315-a28e-788e49290cd4',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Cogburn',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-01-31T08:38:30',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:38:30',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Jt',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '69e49f8e-bc05-4bcc-9f3a-9da237072daa',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Cogburn',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-01-31T08:38:30',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:38:30',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Tina M.',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'c58e6d4f-e038-44fe-b473-5d2769a768e2',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Hoffman',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-01-31T08:38:30',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:38:31',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Mark O. Sutton,',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '51112c17-ee37-41cd-9da4-432d04d7452e',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Jr.',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-01-31T08:38:31',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:38:31',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Margaret Angeline',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '615d46f9-4541-4100-83a1-d84a18e59287',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Atkinson',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-01-31T08:38:31',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:38:31',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Noel H.',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '103bd4ec-3902-4fbb-b17c-fc31d7e38331',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Sutton',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-01-31T08:38:31',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:38:33',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Valerie',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '5c9b04ca-e584-4395-9d81-16c7eb315b44',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Britt',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-01-31T08:38:33',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'Oklahoma',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:39:00',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: 'fc065ed8-96e9-4f89-b488-cb26b2b4602d',
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '6d6c14bd-384c-4e95-86fa-0959e81685c8',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Will 1',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-06-04T15:02:18',
    nickname: null,
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: ['67f33567-48d1-417f-a525-c0b2516b9533'],
      isGrantorTrust: null,
      isGenerationSkippingTransferTaxExempt: false,
      currentBeneficiaries: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: null,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: null,
        hasGiftContingentBeneficiaries: null,
        hasSpecificGifts: true,
        cashGifts: [],
        assetGifts: [],
        otherGifts: 'The testator leaves all of their estate to the JT Cogburn and Carol J. Cogburn Revocable Living Trust.',
      },
      currentTrustees: ['67f33567-48d1-417f-a525-c0b2516b9533', '67f33567-48d1-417f-a525-c0b2516b9533'],
      keyPeople: [
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'CurrentTrustee' },
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'InvestmentAdvisor' },
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'CurrentTrustee' },
      ],
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: false,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: null,
        hasGiftContingentBeneficiaries: false,
        hasSpecificGifts: false,
        cashGifts: null,
        assetGifts: null,
        otherGifts: null,
      },
      associatedTrustForPourOverWill: null,
      survivingSpouseOutright: null,
      survivingSpouseOutrightPercentageOfResiduary: 50.0,
      survivingSpouseInTrust: {
        percentageOfResiduary: 50.0,
        maritalTrust: {
          distributionPercentage: null,
          name: 'Marital Trust',
          beneficiariesDuringTrustPeriod: null,
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          fundingDirectiveOther: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          displayInformation: {
            information:
              '## Trust Administration\\\\n- Grantor: Paul Y. Pop, Jr.\\\\n- Initial Trustee(s): Paul Y. Pop, Jr.\\\\n- Successor Trustees:\\\\n   - Each individual Trustee can designate their successor by written instrument.\\\\n   - If no designated successor is available, Josie Pop and Joe Pop will serve as Trustees if able and willing. \\\\n',
            displayInformationType: 'Notes',
          },
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        creditShelterTrust: null,
        survivorTrust: null,
        hasDifferentSecondLevelDistributions: false,
      },
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: null,
      otherBeneficiariesInTrust: null,
      secondDeathBeneficiariesOutrightPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrust: null,
      secondDeathCustom: null,
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      distributionEventsV2: [],
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      hasNoContestClause: null,
      hasSpendThriftClause: null,
      hasSpecialNeedsProvision: null,
      hasSpousalDisclaimer: null,
      hasQualifiedDomesticTrust: null,
      hasQualifiedSubChapterProvision: null,
      hasDiscretionaryDistributionProvision: null,
      hasDigitalAssetsPowers: null,
      hasGstProvision: null,
      hasConduitTrust: null,
      hasAccumulationTrust: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: [],
      displayInformation: null,
    }),
    trustType: 'PourOverWill',
    trustors: null,
    type: 'Will',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'Oklahoma',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-01-31T08:41:47',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: 'fd6cac7b-a2f6-413f-93b5-5c00e6f9a626',
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'e115d4b9-5818-491c-ab35-08ff3712ff7a',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Jt Cogburn And Carol J. Cogburn Revocable Living Trust',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-06-04T14:59:52',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Joint',
    spouses: null,
    tin: null,
    trustCreationDate: '2018-05-29',
    trustInformation: JSON.stringify({
      trustCreators: ['69e49f8e-bc05-4bcc-9f3a-9da237072daa', 'ea3da758-f0a8-4315-a28e-788e49290cd4'],
      isGrantorTrust: null,
      isGenerationSkippingTransferTaxExempt: false,
      currentBeneficiaries: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: null,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: null,
        hasGiftContingentBeneficiaries: false,
        hasSpecificGifts: false,
        cashGifts: null,
        assetGifts: null,
        otherGifts: null,
      },
      currentTrustees: ['69e49f8e-bc05-4bcc-9f3a-9da237072daa', 'ea3da758-f0a8-4315-a28e-788e49290cd4'],
      keyPeople: [
        { contactId: 'ea3da758-f0a8-4315-a28e-788e49290cd4', role: 'SuccessorTrustee' },
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'CurrentTrustee' },
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'InitialTrustee' },
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'SecondSuccessorTrustee' },
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'ThirdSuccessorTrustee' },
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'TrustProtector' },
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'Guardian' },
        { contactId: '67f33567-48d1-417f-a525-c0b2516b9533', role: 'DirectedAdministrativeTrustee' },
      ],
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: false,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: null,
        hasGiftContingentBeneficiaries: false,
        hasSpecificGifts: false,
        cashGifts: null,
        assetGifts: null,
        otherGifts: null,
      },
      associatedTrustForPourOverWill: null,
      survivingSpouseOutright: null,
      survivingSpouseOutrightPercentageOfResiduary: 50.0,
      survivingSpouseInTrust: {
        percentageOfResiduary: 50.0,
        maritalTrust: {
          distributionPercentage: null,
          name: 'Marital Trust',
          beneficiariesDuringTrustPeriod: null,
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          fundingDirectiveOther: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: [{ distributionType: 'other', otherDistributionDescription: 'Other Distribution Standard\\n' }],
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          displayInformation: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        creditShelterTrust: null,
        survivorTrust: null,
        hasDifferentSecondLevelDistributions: false,
      },
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: null,
      otherBeneficiariesInTrust: null,
      secondDeathBeneficiariesOutrightPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrust: null,
      secondDeathCustom:
        'The trust assets will be distributed to the beneficiaries of the JT Cogburn and Carol J. Cogburn Revocable Living Trust, as outlined in the trust document.',
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      distributionEventsV2: [],
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      hasNoContestClause: null,
      hasSpendThriftClause: null,
      hasSpecialNeedsProvision: null,
      hasSpousalDisclaimer: null,
      hasQualifiedDomesticTrust: null,
      hasQualifiedSubChapterProvision: null,
      hasDiscretionaryDistributionProvision: null,
      hasDigitalAssetsPowers: null,
      hasGstProvision: null,
      hasConduitTrust: null,
      hasAccumulationTrust: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: [],
      displayInformation: null,
    }),
    trustType: 'RevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-03-12T13:22:14',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '95540a1d-1709-46d7-9c41-61bc3e5b325a',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Other 1',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-12T13:28:53',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Other',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: ['d899cd6f-4bbc-410b-9ca1-93f4edd68617'],
      isGrantorTrust: null,
      isGenerationSkippingTransferTaxExempt: false,
      currentBeneficiaries: null,
      currentTrustees: null,
      keyPeople: null,
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: {
        distributionType: 'TimeBasedEvent',
        distributesBasedOn: null,
        distributesBasedOnCondition: null,
        timeBasedLengthYears: null,
        otherDescription: null,
        isTimeBasedEventTrigger: false,
        deathTriggerContactId: null,
        termTrigger: null,
      },
      beneficiariesUponTermination: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: false,
        contingentBeneficiariesDistributionEvent: {
          distributionType: 'TimeBasedEvent',
          distributesBasedOn: null,
          distributesBasedOnCondition: null,
          timeBasedLengthYears: null,
          otherDescription: null,
          isTimeBasedEventTrigger: false,
          deathTriggerContactId: null,
          termTrigger: null,
        },
        beneficiaries: null,
        hasGiftContingentBeneficiaries: false,
        hasSpecificGifts: null,
        cashGifts: null,
        assetGifts: null,
        otherGifts: null,
      },
      associatedTrustForPourOverWill: null,
      survivingSpouseOutright: null,
      survivingSpouseOutrightPercentageOfResiduary: null,
      survivingSpouseInTrust: null,
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: null,
      otherBeneficiariesInTrust: null,
      secondDeathBeneficiariesOutrightPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrust: null,
      secondDeathCustom: null,
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      distributionEventsV2: [],
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      hasNoContestClause: null,
      hasSpendThriftClause: null,
      hasSpecialNeedsProvision: null,
      hasSpousalDisclaimer: null,
      hasQualifiedDomesticTrust: null,
      hasQualifiedSubChapterProvision: null,
      hasDiscretionaryDistributionProvision: null,
      hasDigitalAssetsPowers: null,
      hasGstProvision: null,
      hasConduitTrust: null,
      hasAccumulationTrust: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: null,
      displayInformation: {
        information:
          "# H1\\\\n## H2\\\\n### H3\\\\n#### H4\\\\n##### H5\\\\n###### H6\\\\n\\n## Trust Administration\\\\n- Grantor: Paul Y. Pop, Jr.\\\\n- Initial Trustee(s): Paul Y. Pop, Jr.\\\\n- Successor Trustees:\\\\n   - Each individual Trustee can designate their successor by written instrument.\\\\n   - If no designated successor is available, Josie Pop and Joe Pop will serve as Trustees if able and willing. If both are unable or unwilling, the Primary Beneficiary can select an individual or corporation as Trustee, provided they have reached the age of majority.\\\\n- Right of Withdrawal: No right of withdrawal is mentioned in the document.\\\\n\\\\n## Disposition Details\\\\n- Lifetime Beneficiaries:\\\\n   - During Paul Y. Pop, Jr.'s (Pul's) life, the Trustee has discretion to distribute income and principal to Pul and his descendants for their support in reasonable comfort, education, and healthcare. The Independent Trustee also has discretion for the best interests of Pul and his descendants. Primary consideration should be given to Pul.\\\\n- Disposition on Death:\\\\n   - Upon Pul's death, Pul has a limited power of appointment to direct distribution of the trust to anyone other than himself, his creditors, his estate, or his estate's creditors, exercisable by written instrument or will with specific reference to the trust.\\\\n   - If Pul does not exercise his power of appointment, the trust will be divided in shares per stirpes for his then-living descendants, or if none, in shares per stirpes for the Grantor's then-living descendants.\\\\n   - Each share will be held as a separate Descendant's Trust for the primary benefit of such descendant.\\\\n   - Upon the death of the Primary Beneficiary of a Descendant's Trust, the Primary Beneficiary has a limited power of appointment to direct distribution of the trust to anyone other than himself/herself, his/her creditors, his/her estate, or his/her estate's creditors, exercisable by written instrument or will with specific reference to the trust.\\\\n   - If the Primary Beneficiary does not exercise his/her power of appointment, the trust will be divided in shares per stirpes for such Primary Beneficiary's then living descendants, or if none, in shares per stirpes for the then living descendants of such Primary Beneficiary's nearest ancestor who has one or more descendants then living and who was either the Grantor or a descendant of the Grantor.\\\\n   - Each share will be held as a separate Descendant's Trust for the primary benefit of such descendant.\\\\n",
        displayInformationType: 'Notes',
      },
    }),
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-03-12T13:22:14',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '95540a1d-1709-46d7-9c41-61bc3e5b3251',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Other 1',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-12T13:28:53',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Other',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      hasNoContestClause: false,
      hasSpendThriftClause: false,
      hasSpecialNeedsProvision: false,
      hasSpousalDisclaimer: false,
      hasQualifiedDomesticTrust: false,
      hasQualifiedSubChapterProvision: false,
      hasDiscretionaryDistributionProvision: false,
      hasDigitalAssetsPowers: false,
      hasGstProvision: false,
      hasConduitTrust: false,
      hasAccumulationTrust: false,
      trustCreators: ['d3cf3ab0-85d4-4125-9333-1f1caa502805'],
      keyPeople: [
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
      ],
    }),
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-03-12T13:22:14',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '95540a1d-1709-46d7-9c41-61bc3e5b3252',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Other 2',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-12T13:28:53',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Other',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      hasNoContestClause: false,
      hasSpendThriftClause: false,
      hasSpecialNeedsProvision: false,
      hasSpousalDisclaimer: false,
      hasQualifiedDomesticTrust: false,
      hasQualifiedSubChapterProvision: false,
      hasDiscretionaryDistributionProvision: false,
      hasDigitalAssetsPowers: false,
      hasGstProvision: false,
      hasConduitTrust: false,
      hasAccumulationTrust: false,
      trustCreators: ['d3cf3ab0-85d4-4125-9333-1f1caa502805'],
      keyPeople: [
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
      ],
    }),
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-03-12T13:22:14',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '95540a1d-1709-46d7-9c41-61bc3e5b3253',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Other 3',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-12T13:28:53',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Other',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      hasNoContestClause: false,
      hasSpendThriftClause: false,
      hasSpecialNeedsProvision: false,
      hasSpousalDisclaimer: false,
      hasQualifiedDomesticTrust: false,
      hasQualifiedSubChapterProvision: false,
      hasDiscretionaryDistributionProvision: false,
      hasDigitalAssetsPowers: false,
      hasGstProvision: false,
      hasConduitTrust: false,
      hasAccumulationTrust: false,
      trustCreators: ['d3cf3ab0-85d4-4125-9333-1f1caa502805'],
      keyPeople: [
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
      ],
    }),
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-03-12T13:22:14',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '95540a1d-1709-46d7-9c41-61bc3e5b3254',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Other 4',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-12T13:28:53',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Other',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      hasNoContestClause: false,
      hasSpendThriftClause: false,
      hasSpecialNeedsProvision: false,
      hasSpousalDisclaimer: false,
      hasQualifiedDomesticTrust: false,
      hasQualifiedSubChapterProvision: false,
      hasDiscretionaryDistributionProvision: false,
      hasDigitalAssetsPowers: false,
      hasGstProvision: false,
      hasConduitTrust: false,
      hasAccumulationTrust: false,
      trustCreators: ['d3cf3ab0-85d4-4125-9333-1f1caa502805'],
      keyPeople: [
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
      ],
    }),
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-03-12T13:22:14',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '95540a1d-1709-46d7-9c41-61bc3e5b3255',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Other 5',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-12T13:28:53',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Other',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      hasNoContestClause: false,
      hasSpendThriftClause: false,
      hasSpecialNeedsProvision: false,
      hasSpousalDisclaimer: false,
      hasQualifiedDomesticTrust: false,
      hasQualifiedSubChapterProvision: false,
      hasDiscretionaryDistributionProvision: false,
      hasDigitalAssetsPowers: false,
      hasGstProvision: false,
      hasConduitTrust: false,
      hasAccumulationTrust: false,
      trustCreators: ['d3cf3ab0-85d4-4125-9333-1f1caa502805'],
      keyPeople: [
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
      ],
    }),
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-03-12T13:22:14',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '95540a1d-1709-46d7-9c41-61bc3e5b3256',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Other 6',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-12T13:28:53',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Other',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      hasNoContestClause: false,
      hasSpendThriftClause: false,
      hasSpecialNeedsProvision: false,
      hasSpousalDisclaimer: false,
      hasQualifiedDomesticTrust: false,
      hasQualifiedSubChapterProvision: false,
      hasDiscretionaryDistributionProvision: false,
      hasDigitalAssetsPowers: false,
      hasGstProvision: false,
      hasConduitTrust: false,
      hasAccumulationTrust: false,
      trustCreators: ['d3cf3ab0-85d4-4125-9333-1f1caa502805'],
      keyPeople: [
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
      ],
    }),
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-03-12T13:22:14',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '95540a1d-1709-46d7-9c41-61bc3e5b3257',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    lastName: '',
    legalName: 'Other 7',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-12T13:28:53',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Other',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      hasNoContestClause: false,
      hasSpendThriftClause: false,
      hasSpecialNeedsProvision: false,
      hasSpousalDisclaimer: false,
      hasQualifiedDomesticTrust: false,
      hasQualifiedSubChapterProvision: false,
      hasDiscretionaryDistributionProvision: false,
      hasDigitalAssetsPowers: false,
      hasGstProvision: false,
      hasConduitTrust: false,
      hasAccumulationTrust: false,
      trustCreators: ['d3cf3ab0-85d4-4125-9333-1f1caa502805'],
      keyPeople: [
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
        { contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'TrusteeAppointer' },
      ],
    }),
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: null,
    vendorId: null,
    wid: '1fbd1e6f-d320-496f-9a2f-9b3ea2209022',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'California',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-02-10T17:25:18',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '<EMAIL>',
    esterJobId: null,
    familyName: 'Editable Presentation Layer Demo',
    firstName: 'Note',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'd3cf3ab0-85d4-4125-9333-1f1caa502805',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: true,
    lastModifiedByWid: null,
    lastName: 'Test',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-02-10T17:25:18',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: null,
    vendorId: null,
    wid: '52f19914-5bbc-43eb-83ff-b6a6bb81f5ba',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-06-05T10:42:17',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '4e872475-21b1-472d-b0c5-3d4404ac0831',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: '',
    legalName: 'Will 1',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-06-05T10:42:17',
    nickname: null,
    notes: '{"basicInformation":[""],"beneficiaries":[""],"administrators":[""]}',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: ['d3cf3ab0-85d4-4125-9333-1f1caa502805'],
      isGrantorTrust: null,
      isGenerationSkippingTransferTaxExempt: null,
      currentBeneficiaries: null,
      currentTrustees: ['d3cf3ab0-85d4-4125-9333-1f1caa502805', 'd3cf3ab0-85d4-4125-9333-1f1caa502805'],
      keyPeople: [{ contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'FirstAlternateExecutor' }],
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: null,
      associatedTrustForPourOverWill: null,
      survivingSpouseOutright: null,
      survivingSpouseOutrightPercentageOfResiduary: null,
      survivingSpouseInTrust: null,
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: null,
      otherBeneficiariesInTrust: null,
      secondDeathBeneficiariesOutrightPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrust: null,
      secondDeathCustom: null,
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      distributionEventsV2: [],
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      hasNoContestClause: null,
      hasSpendThriftClause: null,
      hasSpecialNeedsProvision: null,
      hasSpousalDisclaimer: null,
      hasQualifiedDomesticTrust: null,
      hasQualifiedSubChapterProvision: null,
      hasDiscretionaryDistributionProvision: null,
      hasDigitalAssetsPowers: null,
      hasGstProvision: null,
      hasConduitTrust: null,
      hasAccumulationTrust: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: null,
      displayInformation: null,
    }),
    trustType: 'LastWillAndTestament',
    trustors: null,
    type: 'Will',
    vaultIds: null,
    vendorId: null,
    wid: '52f19914-5bbc-43eb-83ff-b6a6bb81f5ba',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4df3a8d6-b601-49d6-af4b-df15c17773ea',
    creationDate: '2025-06-05T10:42:52',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '2427471e-7af0-4528-acd1-1c36089a5156',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: '',
    legalName: 'Will 2',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-06-05T10:42:52',
    nickname: null,
    notes: '{"basicInformation":[""],"beneficiaries":[""],"administrators":[""]}',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: ['d3cf3ab0-85d4-4125-9333-1f1caa502805'],
      isGrantorTrust: null,
      isGenerationSkippingTransferTaxExempt: null,
      currentBeneficiaries: null,
      currentTrustees: ['d3cf3ab0-85d4-4125-9333-1f1caa502805', 'd3cf3ab0-85d4-4125-9333-1f1caa502805'],
      keyPeople: [{ contactId: 'd3cf3ab0-85d4-4125-9333-1f1caa502805', role: 'SuccessorTrustee' }],
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: null,
      associatedTrustForPourOverWill: null,
      survivingSpouseOutright: null,
      survivingSpouseOutrightPercentageOfResiduary: null,
      survivingSpouseInTrust: null,
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: null,
      otherBeneficiariesInTrust: null,
      secondDeathBeneficiariesOutrightPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
      secondDeathBeneficiariesInTrust: null,
      secondDeathCustom: null,
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      distributionEventsV2: [],
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      hasNoContestClause: null,
      hasSpendThriftClause: null,
      hasSpecialNeedsProvision: null,
      hasSpousalDisclaimer: null,
      hasQualifiedDomesticTrust: null,
      hasQualifiedSubChapterProvision: null,
      hasDiscretionaryDistributionProvision: null,
      hasDigitalAssetsPowers: null,
      hasGstProvision: null,
      hasConduitTrust: null,
      hasAccumulationTrust: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: null,
      displayInformation: null,
    }),
    trustType: 'PourOverWill',
    trustors: null,
    type: 'Will',
    vaultIds: null,
    vendorId: null,
    wid: '52f19914-5bbc-43eb-83ff-b6a6bb81f5ba',
    __typename: 'ContactCard',
  },
] as ContactCard[];
