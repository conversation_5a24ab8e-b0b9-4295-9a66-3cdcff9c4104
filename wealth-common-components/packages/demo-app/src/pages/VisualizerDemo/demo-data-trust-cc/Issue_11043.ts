import { ContactCard, TrustInformation } from '@wealthcom/visualizer';

const MaxBeneficiaries = 13;

export const contacts = [
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'Florida',
    addressStateShort: 'FL',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: 'USCitizen',
    createdByWid: null,
    creationDate: '2025-03-12T06:44:46',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '<EMAIL>',
    esterJobId: null,
    familyName: '',
    firstName: '11043',
    formOfIncorporation: null,
    geodata: '{}',
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '0e242986-a011-4786-a753-27665fcf580b',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: true,
    lastModifiedByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    lastName: '11043',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: 'Single',
    middleName: null,
    modificationDate: '2025-05-16T16:12:26',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T06:46:47',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'P1',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'f4fd3c58-93f1-4913-8c2e-0502e83ee11a',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-03-12T06:46:47',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T06:47:03',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'P2',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-03-12T06:47:03',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T06:47:35',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'P3',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'b310aa42-c7ca-40c3-b417-52b116508d5c',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-03-12T06:47:35',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T06:49:19',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'P4',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-03-12T06:49:19',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T06:49:46',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '62638113-f1dc-4086-b859-7f791c48f533',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: true,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    lastName: '',
    legalName: 'Irrevocable Trust Other 1',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-05-20T13:47:48',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Other',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation:
      '{"trustCreators":["0e242986-a011-4786-a753-27665fcf580b"],"isGrantorTrust":null,"isGenerationSkippingTransferTaxExempt":false,"currentBeneficiaries":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":null,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":[{"contactId":"f4fd3c58-93f1-4913-8c2e-0502e83ee11a","distributionPercentage":11.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2","distributionPercentage":11.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"b310aa42-c7ca-40c3-b417-52b116508d5c","distributionPercentage":11.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88","distributionPercentage":11.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"1d406bd3-533a-4c11-b64f-e0f2ec8ee199","distributionPercentage":11.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"5323b1db-77a8-4733-9a31-64463f9f727a","distributionPercentage":11.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"38f258ae-c9e4-4b0d-b2dc-c17328fde00d","distributionPercentage":11.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"ac9ce92c-e8f2-47aa-9dc1-6701980b6548","distributionPercentage":11.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"765386bd-a0d4-4d75-a998-77f5ba620616","distributionPercentage":12.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null}],"hasGiftContingentBeneficiaries":null,"hasSpecificGifts":null,"cashGifts":null,"assetGifts":null,"otherGifts":null},"currentTrustees":null,"keyPeople":null,"initialFundingAmount":null,"initialFundingDetails":null,"distributionEvent":{"distributionType":"TimeBasedEvent","distributesBasedOn":null,"distributesBasedOnCondition":null,"timeBasedLengthYears":3,"otherDescription":null,"isTimeBasedEventTrigger":false,"deathTriggerContactId":null,"termTrigger":null},"beneficiariesUponTermination":{"customDistribution":null,"isDistributedEvenly":false,"hasContingentBeneficiaries":false,"contingentBeneficiariesDistributionEvent":{"distributionType":"TimeBasedEvent","distributesBasedOn":null,"distributesBasedOnCondition":null,"timeBasedLengthYears":null,"otherDescription":null,"isTimeBasedEventTrigger":false,"deathTriggerContactId":null,"termTrigger":null},"beneficiaries":[{"contactId":"b310aa42-c7ca-40c3-b417-52b116508d5c","distributionPercentage":15.38,"isDistributionThroughTrust":true,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":"The Trustees may, 1 Beneficiaries Upon Trust Termination Event Principal Distributions Discretionary distributions by the trustee:\\n","incomeDistributions":[{"distributionType":"other","otherDistributionDescription":"\\"P3 L\\" The Trustees may, in its orem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. Nulla consequat massa quis enim. Donec pede justo, fringilla vel, aliquet nec, vulputate eget, arcu. In enim justo, rhoncus ut, imperdiet a, venenatis vitae, justo. Nullam dictum felis eu pede mollis pretium. Integer tincidunt. Cras dapibus. Vivamus elementum semper nisi. Aenean vulputate eleifend tellus. Aenean leo ligula, porttitor eu, consequat vitae, eleifend ac, enim. Aliquam lorem ante, dapibus in,"}],"distributionEvents":[{"distributionType":"interimMandatoryDistribution","distributionAmount":33.33,"distributionAge":"58","is5and5WithdrawalRight":false,"otherDistributionDescription":null,"hasPowerOfAppointment":null,"typeOfPower":null,"appointmentBecomesEffective":null,"whoIsThePowerLimitedTo":null,"ageWhenBeneficiaryCanExercise":null},{"distributionType":"interimMandatoryDistribution","distributionAmount":33.33,"distributionAge":"60","is5and5WithdrawalRight":false,"otherDistributionDescription":null,"hasPowerOfAppointment":null,"typeOfPower":null,"appointmentBecomesEffective":null,"whoIsThePowerLimitedTo":null,"ageWhenBeneficiaryCanExercise":null}]},{"contactId":"ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88","distributionPercentage":15.38,"isDistributionThroughTrust":true,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":"\\"P44 L\\" Beneficiaries Upon Trust Termination Event Principal Distributions Discretionary distributions by the trustee:\\n","incomeDistributions":null,"distributionEvents":null},{"contactId":"1d406bd3-533a-4c11-b64f-e0f2ec8ee199","distributionPercentage":15.38,"isDistributionThroughTrust":true,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":"\\"P5 L\\" 3 Beneficiaries Upon Trust Termination Event Principal Distributions Discretionary distributions by the trustee:\\n","incomeDistributions":null,"distributionEvents":null},{"contactId":"5323b1db-77a8-4733-9a31-64463f9f727a","distributionPercentage":15.38,"isDistributionThroughTrust":true,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":"\\"P6 L\\" Beneficiaries Upon Trust Termination Event Principal Distributions Discretionary distributions by the trustee:\\n","incomeDistributions":null,"distributionEvents":null},{"contactId":"38f258ae-c9e4-4b0d-b2dc-c17328fde00d","distributionPercentage":15.38,"isDistributionThroughTrust":true,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":"","incomeDistributions":null,"distributionEvents":null},{"contactId":"ac9ce92c-e8f2-47aa-9dc1-6701980b6548","distributionPercentage":15.38,"isDistributionThroughTrust":true,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"765386bd-a0d4-4d75-a998-77f5ba620616","distributionPercentage":7.7,"isDistributionThroughTrust":false,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null}],"hasGiftContingentBeneficiaries":false,"hasSpecificGifts":null,"cashGifts":null,"assetGifts":null,"otherGifts":null},"associatedTrustForPourOverWill":null,"survivingSpouseOutright":null,"survivingSpouseOutrightPercentageOfResiduary":null,"survivingSpouseInTrust":null,"otherBeneficiariesOutrightPercentageOfResiduary":null,"otherBeneficiariesInTrustPercentageOfResiduary":null,"otherBeneficiariesInTrust":null,"secondDeathBeneficiariesOutrightPercentageOfResiduary":null,"secondDeathBeneficiariesInTrustPercentageOfResiduary":null,"secondDeathBeneficiariesInTrust":null,"secondDeathCustom":null,"annuityDetails":null,"isSpouseBeneficiaryUponDeath":null,"otherBeneficiaryUponDeath":null,"unitrustRate":null,"note":null,"noteDate":null,"incomeDistributions":[{"distributionType":"other","otherDistributionDescription":"111 As the Trustees. Manor we shall merit by chief wound no or would. Oh towards between subject passage sending mention or it. Sight happy do burst fruit to woody begin at. Assurance perpetual he in oh determine as. The year paid met him does eyes same. Own marianne improved sociable not out. Thing do sight blush mr an. Celebrated am announcing delightful remarkably we in literature it solicitude. Design use say piqued any gay supply. Front sex match vexed her those great."}],"principalDistribution":"As the Trustees. Manor we shall merit by chief wound no or would. Oh towards between subject passage sending mention or it. Sight happy do burst fruit to woody begin at. Assurance perpetual he in oh determine as. The year paid met him does eyes same. Own marianne improved sociable not out. Thing do sight blush mr an. Celebrated am announcing delightful remarkably we in literature it solicitude. Design use say piqued any gay supply. Front sex match vexed her those great.","distributionEvents":[{"distributionType":"powerOfAppointment","distributionAmount":12.0,"distributionAge":"23","is5and5WithdrawalRight":false,"otherDistributionDescription":null,"hasPowerOfAppointment":null,"typeOfPower":"limited","appointmentBecomesEffective":"lifetime","whoIsThePowerLimitedTo":"The Van Foundation, Far two unaffected one alteration apartments celebrated but middletons interested.","ageWhenBeneficiaryCanExercise":30}],"distributionEventsV2":[],"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"hasNoContestClause":null,"hasSpendThriftClause":null,"hasSpecialNeedsProvision":null,"hasSpousalDisclaimer":null,"hasQualifiedDomesticTrust":null,"hasQualifiedSubChapterProvision":null,"hasDiscretionaryDistributionProvision":null,"hasDigitalAssetsPowers":null,"hasGstProvision":null,"hasConduitTrust":null,"hasAccumulationTrust":null,"shouldDistributeToBeneficiariesAfterDonorDeath":null,"policies":null,"displayInformation":{"information":null,"displayInformationType":"DetailsAndNotes"}}',
    trustType: 'IrrevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T06:58:42',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'P5',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-03-12T06:58:42',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T06:59:01',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'P6',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '5323b1db-77a8-4733-9a31-64463f9f727a',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-03-12T06:59:01',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T06:59:14',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'P7',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '38f258ae-c9e4-4b0d-b2dc-c17328fde00d',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-03-12T06:59:14',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T06:59:27',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'P8',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'ac9ce92c-e8f2-47aa-9dc1-6701980b6548',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-03-12T06:59:27',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T07:00:36',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: '',
    esterJobId: null,
    familyName: '',
    firstName: 'P9',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '765386bd-a0d4-4d75-a998-77f5ba620616',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2025-03-12T07:00:36',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-12T07:50:51',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '7659dff1-b3a2-4e47-87af-ddeb67e89802',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    lastName: '',
    legalName: 'IRT 1',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-06-17T19:07:34',
    nickname: '',
    notes: '{"basicInformation":[""],"beneficiaries":[""],"administrators":[""]}',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Individual',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: ['0e242986-a011-4786-a753-27665fcf580b'],
      isGrantorTrust: true,
      isGenerationSkippingTransferTaxExempt: false,
      currentBeneficiaries: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: true,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: [
          {
            contactId: 'f4fd3c58-93f1-4913-8c2e-0502e83ee11a',
            distributionPercentage: 20.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: {
              customDistribution: null,
              isDistributedEvenly: null,
              hasContingentBeneficiaries: null,
              contingentBeneficiariesDistributionEvent: null,
              beneficiaries: [
                {
                  contactId: 'ac9ce92c-e8f2-47aa-9dc1-6701980b6548',
                  distributionPercentage: 100.0,
                  isDistributionThroughTrust: null,
                  contingentBeneficiaries: null,
                  note: null,
                  noteDate: null,
                  principalDistribution: null,
                  incomeDistributions: null,
                  distributionEvents: null,
                },
              ],
              hasGiftContingentBeneficiaries: null,
              hasSpecificGifts: null,
              cashGifts: null,
              assetGifts: null,
              otherGifts: null,
            },
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2',
            distributionPercentage: 20.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: {
              customDistribution: null,
              isDistributedEvenly: null,
              hasContingentBeneficiaries: null,
              contingentBeneficiariesDistributionEvent: null,
              beneficiaries: [
                {
                  contactId: '765386bd-a0d4-4d75-a998-77f5ba620616',
                  distributionPercentage: 100.0,
                  isDistributionThroughTrust: null,
                  contingentBeneficiaries: null,
                  note: null,
                  noteDate: null,
                  principalDistribution: null,
                  incomeDistributions: null,
                  distributionEvents: null,
                },
              ],
              hasGiftContingentBeneficiaries: null,
              hasSpecificGifts: null,
              cashGifts: null,
              assetGifts: null,
              otherGifts: null,
            },
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'b310aa42-c7ca-40c3-b417-52b116508d5c',
            distributionPercentage: 20.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: {
              customDistribution: null,
              isDistributedEvenly: null,
              hasContingentBeneficiaries: null,
              contingentBeneficiariesDistributionEvent: null,
              beneficiaries: [],
              hasGiftContingentBeneficiaries: null,
              hasSpecificGifts: null,
              cashGifts: null,
              assetGifts: null,
              otherGifts: null,
            },
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
            distributionPercentage: 20.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
            distributionPercentage: 20.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
        ],
        hasGiftContingentBeneficiaries: true,
        hasSpecificGifts: true,
        cashGifts: [
          {
            contactId: 'b310aa42-c7ca-40c3-b417-52b116508d5c',
            value: 300,
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
          },
        ],
        assetGifts: [
          {
            contactId: 'f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2',
            assetId: 'gUCv9kROik6WTp3rEw9AZQ',
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: '5323b1db-77a8-4733-9a31-64463f9f727a',
          },
          {
            contactId: 'b310aa42-c7ca-40c3-b417-52b116508d5c',
            assetId: 'lIlpfM27GU6h4lHhUlnwlA',
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: '38f258ae-c9e4-4b0d-b2dc-c17328fde00d',
          },
        ],
        otherGifts: 'Another Car',
      },
      currentTrustees: null,
      keyPeople: null,
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: false,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: [
          {
            contactId: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
            distributionPercentage: 12.5,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
            distributionPercentage: 12.5,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'b310aa42-c7ca-40c3-b417-52b116508d5c',
            distributionPercentage: 12.5,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '765386bd-a0d4-4d75-a998-77f5ba620616',
            distributionPercentage: 12.5,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'ac9ce92c-e8f2-47aa-9dc1-6701980b6548',
            distributionPercentage: 12.5,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '38f258ae-c9e4-4b0d-b2dc-c17328fde00d',
            distributionPercentage: 12.5,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '52b22462-e951-40a9-9014-881a3144bc9e',
            distributionPercentage: 12.5,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '5323b1db-77a8-4733-9a31-64463f9f727a',
            distributionPercentage: 12.5,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
        ],
        hasGiftContingentBeneficiaries: true,
        hasSpecificGifts: true,
        cashGifts: [
          {
            contactId: '765386bd-a0d4-4d75-a998-77f5ba620616',
            value: 900,
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: 'f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2',
          },
        ],
        assetGifts: [
          {
            contactId: 'b310aa42-c7ca-40c3-b417-52b116508d5c',
            assetId: 'lIlpfM27GU6h4lHhUlnwlA',
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: 'b310aa42-c7ca-40c3-b417-52b116508d5c',
          },
        ],
        otherGifts: 'Specific Gift Type\\n Custom',
      },
      associatedTrustForPourOverWill: null,
      survivingSpouseOutright: true,
      survivingSpouseOutrightPercentageOfResiduary: 11.0,
      survivingSpouseInTrust: {
        percentageOfResiduary: 12.0,
        maritalTrust: null,
        creditShelterTrust: {
          distributionPercentage: null,
          name: 'Credit Shelter Trust',
          beneficiariesDuringTrustPeriod: null,
          fundingDirectiveType: 'remainderAfterPecuniaryAmountToSurvivorsTrust',
          fundingDirectiveAmount: null,
          fundingDirectiveOther: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: [{ distributionType: 'atLeastQuarterly', otherDistributionDescription: null }],
          principalDistribution: 'Ctext',
          distributionEvents: [
            {
              distributionType: 'other',
              distributionAmount: null,
              distributionAge: '24',
              is5and5WithdrawalRight: false,
              otherDistributionDescription: 'Ctext',
              hasPowerOfAppointment: null,
              typeOfPower: null,
              appointmentBecomesEffective: null,
              whoIsThePowerLimitedTo: null,
              ageWhenBeneficiaryCanExercise: null,
            },
          ],
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: {
            distributionType: 'OtherEvent',
            distributesBasedOn: null,
            distributesBasedOnCondition: null,
            timeBasedLengthYears: null,
            otherDescription: 'Ctext',
            isTimeBasedEventTrigger: false,
            deathTriggerContactId: null,
            termTrigger: null,
          },
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          displayInformation: { information: 'Ctext', displayInformationType: 'DetailsAndNotes' },
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        survivorTrust: null,
        hasDifferentSecondLevelDistributions: false,
      },
      otherBeneficiariesOutrightPercentageOfResiduary: 13.0,
      otherBeneficiariesInTrustPercentageOfResiduary: 14.0,
      otherBeneficiariesInTrust: [
        {
          distributionPercentage: 100.0,
          name: 'Sub1',
          beneficiariesDuringTrustPeriod: ['f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2', 'b310aa42-c7ca-40c3-b417-52b116508d5c'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          fundingDirectiveOther: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: [{ distributionType: 'fullyDiscretionaryDistributions', otherDistributionDescription: null }],
          principalDistribution: 'Ctext',
          distributionEvents: [
            {
              distributionType: 'powerOfAppointment',
              distributionAmount: null,
              distributionAge: null,
              is5and5WithdrawalRight: false,
              otherDistributionDescription: null,
              hasPowerOfAppointment: null,
              typeOfPower: 'limited',
              appointmentBecomesEffective: 'testamentary',
              whoIsThePowerLimitedTo: 'Ctext',
              ageWhenBeneficiaryCanExercise: 28,
            },
          ],
          hasUltimateBeneficiaries: true,
          ultimateBeneficiaries: {
            trustDistributesTo: 'specificBeneficiaries',
            additionalDetails: 'Ctext',
            customDistribution: null,
            isDistributedEvenly: null,
            hasContingentBeneficiaries: null,
            contingentBeneficiariesDistributionEvent: null,
            beneficiaries: [
              {
                contactId: '765386bd-a0d4-4d75-a998-77f5ba620616',
                distributionPercentage: 33.33,
                isDistributionThroughTrust: false,
                contingentBeneficiaries: null,
                note: null,
                noteDate: null,
                principalDistribution: null,
                incomeDistributions: null,
                distributionEvents: null,
              },
              {
                contactId: 'ac9ce92c-e8f2-47aa-9dc1-6701980b6548',
                distributionPercentage: 33.33,
                isDistributionThroughTrust: false,
                contingentBeneficiaries: null,
                note: null,
                noteDate: null,
                principalDistribution: null,
                incomeDistributions: null,
                distributionEvents: null,
              },
              {
                contactId: '38f258ae-c9e4-4b0d-b2dc-c17328fde00d',
                distributionPercentage: 33.33,
                isDistributionThroughTrust: null,
                contingentBeneficiaries: null,
                note: null,
                noteDate: null,
                principalDistribution: null,
                incomeDistributions: null,
                distributionEvents: null,
              },
            ],
            hasGiftContingentBeneficiaries: null,
            hasSpecificGifts: null,
            cashGifts: null,
            assetGifts: null,
            otherGifts: null,
          },
          isThisTrustRevocable: null,
          distributionEvent: {
            distributionType: 'AgeBasedEvent',
            distributesBasedOn: 'PrimaryBeneficiary',
            distributesBasedOnCondition: null,
            timeBasedLengthYears: 22,
            otherDescription: null,
            isTimeBasedEventTrigger: false,
            deathTriggerContactId: null,
            termTrigger: null,
          },
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          displayInformation: { information: 'Sub1 - Additional Details Notes', displayInformationType: 'DetailsAndNotes' },
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
      ],
      secondDeathBeneficiariesOutrightPercentageOfResiduary: 44.0,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: 55.0,
      secondDeathBeneficiariesInTrust: [
        {
          distributionPercentage: 50.0,
          name: 'Sub2',
          beneficiariesDuringTrustPeriod: ['f4fd3c58-93f1-4913-8c2e-0502e83ee11a'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          fundingDirectiveOther: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: 'f4fd3c58-93f1-4913-8c2e-0502e83ee11a',
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          displayInformation: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        {
          distributionPercentage: 50.0,
          name: 'Sub3',
          beneficiariesDuringTrustPeriod: ['f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          fundingDirectiveOther: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: 'f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2',
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          displayInformation: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
      ],
      secondDeathCustom: 'Upon Spouse’s Death Custom',
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      distributionEventsV2: [],
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      hasNoContestClause: null,
      hasSpendThriftClause: null,
      hasSpecialNeedsProvision: null,
      hasSpousalDisclaimer: null,
      hasQualifiedDomesticTrust: null,
      hasQualifiedSubChapterProvision: null,
      hasDiscretionaryDistributionProvision: null,
      hasDigitalAssetsPowers: null,
      hasGstProvision: null,
      hasConduitTrust: null,
      hasAccumulationTrust: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: null,
      displayInformation: null,
    } as TrustInformation),
    trustType: 'RevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-26T18:01:17',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '9d4e6720-4dcf-4b16-be12-9f22c922e8d3',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: '',
    legalName: 'LWT 1',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-26T18:01:17',
    nickname: null,
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation:
      '{"trustCreators":null,"isGrantorTrust":null,"isGenerationSkippingTransferTaxExempt":null,"currentBeneficiaries":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":null,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":null,"hasGiftContingentBeneficiaries":true,"hasSpecificGifts":true,"cashGifts":[{"contactId":"f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2","value":2345,"isDistributionThroughTrust":null,"contingentBeneficiaryContactId":"b310aa42-c7ca-40c3-b417-52b116508d5c"}],"assetGifts":[{"contactId":"f4fd3c58-93f1-4913-8c2e-0502e83ee11a","assetId":"gUCv9kROik6WTp3rEw9AZQ","isDistributionThroughTrust":null,"contingentBeneficiaryContactId":"b310aa42-c7ca-40c3-b417-52b116508d5c"}],"otherGifts":"Specific Gift Custom"},"currentTrustees":null,"keyPeople":null,"initialFundingAmount":null,"initialFundingDetails":null,"distributionEvent":null,"beneficiariesUponTermination":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":false,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":null,"hasGiftContingentBeneficiaries":false,"hasSpecificGifts":false,"cashGifts":null,"assetGifts":null,"otherGifts":null},"associatedTrustForPourOverWill":null,"survivingSpouseOutright":null,"survivingSpouseOutrightPercentageOfResiduary":null,"survivingSpouseInTrust":null,"otherBeneficiariesOutrightPercentageOfResiduary":null,"otherBeneficiariesInTrustPercentageOfResiduary":null,"otherBeneficiariesInTrust":null,"secondDeathBeneficiariesOutrightPercentageOfResiduary":null,"secondDeathBeneficiariesInTrustPercentageOfResiduary":null,"secondDeathBeneficiariesInTrust":null,"secondDeathCustom":null,"annuityDetails":null,"isSpouseBeneficiaryUponDeath":null,"otherBeneficiaryUponDeath":null,"unitrustRate":null,"note":null,"noteDate":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"distributionEventsV2":[],"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"hasNoContestClause":null,"hasSpendThriftClause":null,"hasSpecialNeedsProvision":null,"hasSpousalDisclaimer":null,"hasQualifiedDomesticTrust":null,"hasQualifiedSubChapterProvision":null,"hasDiscretionaryDistributionProvision":null,"hasDigitalAssetsPowers":null,"hasGstProvision":null,"hasConduitTrust":null,"hasAccumulationTrust":null,"shouldDistributeToBeneficiariesAfterDonorDeath":null,"policies":null,"displayInformation":null}',
    trustType: 'LastWillAndTestament',
    trustors: null,
    type: 'Will',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-26T18:02:49',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'a8829836-53ef-45d9-b22c-b0c233c86fd6',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: '',
    legalName: 'POW 1',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-26T18:02:49',
    nickname: null,
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation:
      '{"trustCreators":null,"isGrantorTrust":null,"isGenerationSkippingTransferTaxExempt":null,"currentBeneficiaries":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":null,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":null,"hasGiftContingentBeneficiaries":true,"hasSpecificGifts":true,"cashGifts":[{"contactId":"f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2","value":43434,"isDistributionThroughTrust":null,"contingentBeneficiaryContactId":"b310aa42-c7ca-40c3-b417-52b116508d5c"}],"assetGifts":[{"contactId":"f4fd3c58-93f1-4913-8c2e-0502e83ee11a","assetId":"lIlpfM27GU6h4lHhUlnwlA","isDistributionThroughTrust":null,"contingentBeneficiaryContactId":"ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88"}],"otherGifts":null},"currentTrustees":null,"keyPeople":null,"initialFundingAmount":null,"initialFundingDetails":null,"distributionEvent":null,"beneficiariesUponTermination":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":false,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":null,"hasGiftContingentBeneficiaries":false,"hasSpecificGifts":false,"cashGifts":null,"assetGifts":null,"otherGifts":null},"associatedTrustForPourOverWill":"7659dff1-b3a2-4e47-87af-ddeb67e89802","survivingSpouseOutright":null,"survivingSpouseOutrightPercentageOfResiduary":null,"survivingSpouseInTrust":null,"otherBeneficiariesOutrightPercentageOfResiduary":null,"otherBeneficiariesInTrustPercentageOfResiduary":null,"otherBeneficiariesInTrust":null,"secondDeathBeneficiariesOutrightPercentageOfResiduary":null,"secondDeathBeneficiariesInTrustPercentageOfResiduary":null,"secondDeathBeneficiariesInTrust":null,"secondDeathCustom":null,"annuityDetails":null,"isSpouseBeneficiaryUponDeath":null,"otherBeneficiaryUponDeath":null,"unitrustRate":null,"note":null,"noteDate":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"distributionEventsV2":[],"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"hasNoContestClause":null,"hasSpendThriftClause":null,"hasSpecialNeedsProvision":null,"hasSpousalDisclaimer":null,"hasQualifiedDomesticTrust":null,"hasQualifiedSubChapterProvision":null,"hasDiscretionaryDistributionProvision":null,"hasDigitalAssetsPowers":null,"hasGstProvision":null,"hasConduitTrust":null,"hasAccumulationTrust":null,"shouldDistributeToBeneficiariesAfterDonorDeath":null,"policies":null,"displayInformation":null}',
    trustType: 'PourOverWill',
    trustors: null,
    type: 'Will',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'Kansas',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-03-31T18:15:09',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: '8ecff3a5-9b02-4659-af76-3af7aea8957c',
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '1df0e864-3b8b-4a78-badf-137ceca45e06',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    lastName: '',
    legalName: 'Will of Angela R. Brill',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-06-27T14:31:00',
    nickname: null,
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: '2018-02-09',
    trustInformation:
      '{"trustCreators":["023023e7-291d-4f38-9dcb-8731083c99b6"],"isGrantorTrust":null,"isGenerationSkippingTransferTaxExempt":false,"currentBeneficiaries":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":null,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":null,"hasGiftContingentBeneficiaries":null,"hasSpecificGifts":false,"cashGifts":null,"assetGifts":null,"otherGifts":null},"currentTrustees":["f4fd3c58-93f1-4913-8c2e-0502e83ee11a","f24fa8d2-3c2b-45b1-9b76-1d7fd3f630a2"],"keyPeople":[{"contactId":"f971f1df-bb5b-419c-baaf-027fd7842307","role":"FirstAlternateExecutor"},{"contactId":"d86f8a18-fc4a-4b8f-8266-68915d6f3238","role":"SecondAlternateExecutor"},{"contactId":"1e9268e0-ba3b-47bf-bbe3-f3abdb97c3e9","role":"ThirdAlternateExecutor"},{"contactId":"52b22462-e951-40a9-9014-881a3144bc9e","role":"FifthAlternateExecutor"}],"initialFundingAmount":null,"initialFundingDetails":null,"distributionEvent":null,"beneficiariesUponTermination":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":false,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":null,"hasGiftContingentBeneficiaries":false,"hasSpecificGifts":false,"cashGifts":null,"assetGifts":null,"otherGifts":null},"associatedTrustForPourOverWill":null,"survivingSpouseOutright":null,"survivingSpouseOutrightPercentageOfResiduary":null,"survivingSpouseInTrust":null,"otherBeneficiariesOutrightPercentageOfResiduary":null,"otherBeneficiariesInTrustPercentageOfResiduary":100.0,"otherBeneficiariesInTrust":[{"distributionPercentage":null,"name":"Trust for Other Beneficiaries","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":[{"distributionType":"other","otherDistributionDescription":"For Eric and Mitchell Brill, until age 25, the Independent Trustee may distribute income/principal at their discretion. After 25, a 10% Unitrust Amount."}],"principalDistribution":"Until 25, the Independent Trustee has discretion. After 25, a unitrust amount of 10% is paid. Additional distributions for health, education, and maintenance.","distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":{"trustDistributesTo":null,"additionalDetails":null,"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":null,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":[],"hasGiftContingentBeneficiaries":null,"hasSpecificGifts":false,"cashGifts":null,"assetGifts":null,"otherGifts":null},"isThisTrustRevocable":null,"distributionEvent":{"distributionType":"OtherEvent","distributesBasedOn":null,"distributesBasedOnCondition":null,"timeBasedLengthYears":null,"otherDescription":"Eric and Mitchell Brill\'s trusts terminate at ages 30 and 35 with the trustee\'s agreement. Descendants\' trusts are under the same terms.","isTimeBasedEventTrigger":false,"deathTriggerContactId":null,"termTrigger":null},"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null}],"secondDeathBeneficiariesOutrightPercentageOfResiduary":null,"secondDeathBeneficiariesInTrustPercentageOfResiduary":null,"secondDeathBeneficiariesInTrust":[],"secondDeathCustom":"- Remaining trust property is divided into separate, equal shares for **Eric Thomas Brill** and **Mitchell Reed Brill**, in trust.\\\\n- If either son dies before complete distribution, their share goes to their descendants, per stirpes, in trust. If no descendants, then to grantor\'s descendants, per stirpes, in trust.\\\\n- If no descendants of grantor, then to **Monica Westhoven**, **Erica Garrett**, and **Alexander White**, 1/3 share each, outright. If any of them are deceased, then to their descendants, per stirpes.","annuityDetails":null,"isSpouseBeneficiaryUponDeath":null,"otherBeneficiaryUponDeath":null,"unitrustRate":null,"note":null,"noteDate":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"distributionEventsV2":[],"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"hasNoContestClause":null,"hasSpendThriftClause":null,"hasSpecialNeedsProvision":null,"hasSpousalDisclaimer":null,"hasQualifiedDomesticTrust":null,"hasQualifiedSubChapterProvision":null,"hasDiscretionaryDistributionProvision":null,"hasDigitalAssetsPowers":null,"hasGstProvision":null,"hasConduitTrust":null,"hasAccumulationTrust":null,"shouldDistributeToBeneficiariesAfterDonorDeath":null,"policies":[],"displayInformation":null}',
    trustType: 'LastWillAndTestament',
    trustors: null,
    type: 'Will',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '4a02fe08-a87f-40fe-969c-3b5325954217',
    creationDate: '2025-04-15T09:09:35',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'fbf17983-e56f-414b-b0e5-648f829e9b95',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '4a02fe08-a87f-40fe-969c-3b5325954217',
    lastName: '',
    legalName: 'JRT 1',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-04-15T09:10:43',
    nickname: '',
    notes: '[""]',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Joint',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation:
      '{"trustCreators":null,"isGrantorTrust":true,"isGenerationSkippingTransferTaxExempt":null,"currentBeneficiaries":null,"currentTrustees":null,"keyPeople":null,"initialFundingAmount":null,"initialFundingDetails":null,"distributionEvent":null,"beneficiariesUponTermination":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":false,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":null,"hasGiftContingentBeneficiaries":false,"hasSpecificGifts":false,"cashGifts":null,"assetGifts":null,"otherGifts":null},"associatedTrustForPourOverWill":null,"survivingSpouseOutright":null,"survivingSpouseOutrightPercentageOfResiduary":null,"survivingSpouseInTrust":null,"otherBeneficiariesOutrightPercentageOfResiduary":null,"otherBeneficiariesInTrustPercentageOfResiduary":null,"otherBeneficiariesInTrust":[{"distributionPercentage":14.28,"name":"S1","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":14.28,"name":"S2","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":14.28,"name":"S3","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":14.28,"name":"S4","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":14.28,"name":"S5","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":14.28,"name":"S6","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":14.28,"name":"S7","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null}],"secondDeathBeneficiariesOutrightPercentageOfResiduary":null,"secondDeathBeneficiariesInTrustPercentageOfResiduary":null,"secondDeathBeneficiariesInTrust":null,"secondDeathCustom":null,"annuityDetails":null,"isSpouseBeneficiaryUponDeath":null,"otherBeneficiaryUponDeath":null,"unitrustRate":null,"note":null,"noteDate":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"distributionEventsV2":[],"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"hasNoContestClause":false,"hasSpendThriftClause":false,"hasSpecialNeedsProvision":false,"hasSpousalDisclaimer":false,"hasQualifiedDomesticTrust":false,"hasQualifiedSubChapterProvision":false,"hasDiscretionaryDistributionProvision":false,"hasDigitalAssetsPowers":false,"hasGstProvision":false,"hasConduitTrust":false,"hasAccumulationTrust":false,"shouldDistributeToBeneficiariesAfterDonorDeath":null,"policies":null,"displayInformation":null}',
    trustType: 'RevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-06-10T09:46:58',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '52b22462-e951-40a9-9014-881a3144bc9e',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    lastName: '',
    legalName: 'Entity Test',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-06-10T13:31:08',
    nickname: '',
    notes: '{"basicInformation":[""],"beneficiaries":[""],"administrators":[""]}',
    otherRevocableTrustType: null,
    owners: '[{"id":"f4fd3c58-93f1-4913-8c2e-0502e83ee11a","type":"Individual","name":null,"percentage":100.0,"subOwners":null}]',
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Entity',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    creationDate: '2025-06-17T16:02:22',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'ebd31c19-bbe4-4025-b5fe-2497ec890f3c',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '05a3fadd-5bcc-47c5-bd34-ec4420769cec',
    lastName: '',
    legalName: 'IRT 2',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-07-09T15:58:50',
    nickname: '',
    notes: '{"basicInformation":[""],"beneficiaries":[""],"administrators":[""]}',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Individual',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: null,
      isGrantorTrust: true,
      isGenerationSkippingTransferTaxExempt: false,
      currentBeneficiaries: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: null,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: null,
        hasGiftContingentBeneficiaries: false,
        hasSpecificGifts: false,
        cashGifts: null,
        assetGifts: [
          { contactId: null, assetId: 'gUCv9kROik6WTp3rEw9AZQ', isDistributionThroughTrust: null, contingentBeneficiaryContactId: null },
        ],
        otherGifts: null,
      },
      currentTrustees: null,
      keyPeople: null,
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: {
        customDistribution: null,
        isDistributedEvenly: true,
        hasContingentBeneficiaries: false,
        contingentBeneficiariesDistributionEvent: {
          distributionType: null,
          distributesBasedOn: 'TrustorsSpouse',
          distributesBasedOnCondition: null,
          timeBasedLengthYears: null,
          otherDescription: null,
          isTimeBasedEventTrigger: false,
          deathTriggerContactId: null,
          termTrigger: null,
        },
        beneficiaries: [
          {
            contactId: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: '1d406bd3-533a-4c11-b64f-e0f2ec8ee199',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
          {
            contactId: 'ec1ebd96-52a9-4a98-aac1-3f1b3c42dc88',
            distributionPercentage: 100 / MaxBeneficiaries,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
            principalDistribution: null,
            incomeDistributions: null,
            distributionEvents: null,
          },
        ],
        hasGiftContingentBeneficiaries: false,
        hasSpecificGifts: false,
        cashGifts: null,
        assetGifts: null,
        otherGifts: null,
      },
      associatedTrustForPourOverWill: null,
      survivingSpouseOutright: true,
      survivingSpouseOutrightPercentageOfResiduary: 50.0,
      survivingSpouseInTrust: null,
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: null,
      otherBeneficiariesInTrust: null,
      secondDeathBeneficiariesOutrightPercentageOfResiduary: 20.0,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: 80.0,
      secondDeathBeneficiariesInTrust: [
        {
          distributionPercentage: 50.0,
          name: 'Spouse Death InTrust1',
          beneficiariesDuringTrustPeriod: ['765386bd-a0d4-4d75-a998-77f5ba620616'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          fundingDirectiveOther: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: '765386bd-a0d4-4d75-a998-77f5ba620616',
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          displayInformation: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        {
          distributionPercentage: 50.0,
          name: 'Spouse Death InTrust2',
          beneficiariesDuringTrustPeriod: ['ac9ce92c-e8f2-47aa-9dc1-6701980b6548'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          fundingDirectiveOther: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: 'ac9ce92c-e8f2-47aa-9dc1-6701980b6548',
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          displayInformation: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
      ],
      secondDeathCustom: null,
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      distributionEventsV2: [],
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      hasNoContestClause: false,
      hasSpendThriftClause: false,
      hasSpecialNeedsProvision: false,
      hasSpousalDisclaimer: false,
      hasQualifiedDomesticTrust: false,
      hasQualifiedSubChapterProvision: false,
      hasDiscretionaryDistributionProvision: false,
      hasDigitalAssetsPowers: false,
      hasGstProvision: false,
      hasConduitTrust: false,
      hasAccumulationTrust: false,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: null,
      displayInformation: null,
    } as TrustInformation),
    trustType: 'RevocableTrust',
    trustors: null,
    type: 'Trust',
    vaultIds: [],
    vendorId: null,
    wid: '93b565e1-8e05-4a38-8c24-308ea9a0998f',
    __typename: 'ContactCard',
  },
] as ContactCard[];
