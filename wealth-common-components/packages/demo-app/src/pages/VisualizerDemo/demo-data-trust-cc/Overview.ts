import { ContactCard } from '@wealthcom/visualizer';

export const CC_IRT = {
  addressCity: null,
  addressCountry: null,
  addressCountryShort: null,
  addressCounty: null,
  addressFormatted: null,
  addressLine1: null,
  addressLine2: null,
  addressState: 'Illinois',
  addressStateShort: null,
  addressStreetName: null,
  addressStreetNumber: null,
  addressZipCode: null,
  aliases: [],
  children: null,
  childrenIds: null,
  citizenship: null,
  createdByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
  creationDate: '2024-06-21T10:35:46',
  directBeneficiaries: null,
  dob: null,
  ein: null,
  email: null,
  firstName: '',
  formOfIncorporation: null,
  geodata: null,
  hasChildren: false,
  hasFamilyMemberWithSpecialNeeds: false,
  hasPets: false,
  id: 'ed7fa307-091b-4407-bf40-46ffe7733c44',
  isDeceased: false,
  isDonorAdvisedFund: null,
  isGrantorTrust: false,
  isHideFromHeritageMap: false,
  isLegalNameConfirmed: false,
  isOutsideOfTaxableEstate: true,
  isPreviousChild: false,
  isPrimary: false,
  lastModifiedByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
  lastName: '',
  legalName: 'Daniel A. Berman Revocable Trust',
  manualValue: 4567,
  manualValueAsOfDate: '2024-06-01',
  maritalStatus: null,
  middleName: null,
  modificationDate: '2024-06-24T11:53:59',
  nickname: 'Trust Nickname',
  notes: 'Additional Notes',
  owners: null,
  partner: null,
  partnerId: null,
  phoneNumber: null,
  relationship: null,
  revocableTrustType: 'Individual',
  spouses: null,
  tin: *********,
  trustCreationDate: '2024-06-01',
  trustInformation: JSON.stringify({
    // trustCreators: ['363d21c0-c770-446b-936d-8561aa680b4e', '363d21c0-c770-446b-936d-8561aa680b4e'],
    isGrantorTrust: false,
    isGenerationSkippingTransferTaxExempt: false,
    currentBeneficiaries: {
      isDistributedEvenly: true,
      hasContingentBeneficiaries: true,
      contingentBeneficiariesDistributionEvent: null,
      beneficiaries: [
        {
          contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
          distributionPercentage: 100,
          isDistributionThroughTrust: null,
          contingentBeneficiaries: {
            isDistributedEvenly: null,
            hasContingentBeneficiaries: null,
            contingentBeneficiariesDistributionEvent: null,
            beneficiaries: [
              {
                contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
                distributionPercentage: 100,
                isDistributionThroughTrust: null,
                contingentBeneficiaries: null,
                note: null,
                noteDate: null,
              },
            ],
            hasGiftContingentBeneficiaries: null,
            hasSpecificGifts: null,
            cashGifts: null,
            assetGifts: null,
          },
          note: null,
          noteDate: null,
        },
      ],
      hasGiftContingentBeneficiaries: true,
      hasSpecificGifts: true,
      cashGifts: [
        {
          contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
          value: 34,
          isDistributionThroughTrust: null,
          contingentBeneficiaryContactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        },
      ],
      assetGifts: [
        {
          contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
          assetId: '1gIPagbYF0i2QWmudrM6eg',
          isDistributionThroughTrust: null,
          contingentBeneficiaryContactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        },
      ],
    },
    currentTrustees: ['363d21c0-c770-446b-936d-8561aa680b4e', 'ed7fa307-091b-4407-bf40-46ffe7733c44'],
    keyPeople: [
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'InitialTrustee',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'SuccessorTrustee',
      },
    ],
    initialFundingAmount: 234,
    initialFundingDetails: 'Additional Funding Details',
    distributionEvent: null,
    beneficiariesUponTermination: {
      isDistributedEvenly: true,
      hasContingentBeneficiaries: false,
      contingentBeneficiariesDistributionEvent: null,
      beneficiaries: null,
      hasGiftContingentBeneficiaries: null,
      hasSpecificGifts: true,
      cashGifts: null,
      assetGifts: null,
    },
    survivingSpouseOutright: null,
    survivingSpouseOutrightPercentageOfResiduary: 34,
    survivingSpouseInTrust: {
      percentageOfResiduary: 34,
      hasDifferentSecondLevelDistributions: false,
      maritalTrust: {
        distributionEvent: {
          distributionType: 'DeathEvent',
          distributesBasedOn: 'PrimaryBeneficiary',
          distributesBasedOnCondition: null,
          timeBasedLengthYears: null,
          otherDescription: null,
          isTimeBasedEventTrigger: false,
          deathTriggerContactId: null,
          termTrigger: null,
        },
        distributionPercentage: null,
        name: 'Name of the Marital Trust',
        fundingDirectiveType: 'beneficiaryActionReverseQTIP',
        fundingDirectiveAmount: null,
        flowchartFundingAmount: null,
        beneficiaryDuringTrustPeriod: null,
        otherBeneficiaryUponDeath: null,
        mandatoryIncomeDistribution: 'atLeastAnnually',
        principalDistribution: 'abc',
        otherIncomeDistributionStandard: null,
        powerOfAppointment: {
          hasPowerOfAppointment: true,
          typeOfPower: 'general',
          appointmentBecomesEffective: 'lifetime',
          whoIsThePowerLimitedTo: null,
        },
        ultimateBeneficiaries: null,
        isThisTrustRevocable: null,
        isGenerationSkippingTransferTaxExempt: true,
        inclusionRatio: 'efg',
      },
      creditShelterTrust: {
        distributionEvent: {
          distributionType: 'DeathEvent',
          distributesBasedOn: 'PrimaryBeneficiary',
          distributesBasedOnCondition: null,
          timeBasedLengthYears: null,
          otherDescription: null,
          isTimeBasedEventTrigger: false,
          deathTriggerContactId: null,
          termTrigger: null,
        },
        distributionPercentage: null,
        name: 'Name of the Credit Shelter Trust',
        fundingDirectiveType: 'beneficiaryActionClaytonQTIP',
        fundingDirectiveAmount: null,
        flowchartFundingAmount: null,
        beneficiaryDuringTrustPeriod: null,
        otherBeneficiaryUponDeath: null,
        mandatoryIncomeDistribution: 'atLeastQuarterly',
        principalDistribution: 'asd',
        otherIncomeDistributionStandard: null,
        powerOfAppointment: {
          hasPowerOfAppointment: true,
          typeOfPower: 'limited',
          appointmentBecomesEffective: 'lifetime',
          whoIsThePowerLimitedTo: 'ert',
        },
        ultimateBeneficiaries: null,
        isThisTrustRevocable: null,
        isGenerationSkippingTransferTaxExempt: true,
        inclusionRatio: 'iuyu',
      },
      survivorTrust: null,
    },
    otherBeneficiariesOutrightPercentageOfResiduary: 1,
    otherBeneficiariesInTrustPercentageOfResiduary: 1,
    otherBeneficiariesInTrust: [
      {
        distributionEvent: {
          distributionType: 'OtherEvent',
          distributesBasedOn: null,
          distributesBasedOnCondition: null,
          timeBasedLengthYears: null,
          otherDescription: 'Additional Information',
          isTimeBasedEventTrigger: false,
          deathTriggerContactId: null,
          termTrigger: null,
        },
        distributionPercentage: 50,
        name: 'Name of Sub-Trust',
        fundingDirectiveType: null,
        fundingDirectiveAmount: null,
        flowchartFundingAmount: null,
        beneficiaryDuringTrustPeriod: null,
        otherBeneficiaryUponDeath: null,
        mandatoryIncomeDistribution: 'atLeastQuarterly',
        principalDistribution: 'trtr',
        otherIncomeDistributionStandard: null,
        powerOfAppointment: {
          hasPowerOfAppointment: true,
          typeOfPower: 'general',
          appointmentBecomesEffective: 'lifetime',
          whoIsThePowerLimitedTo: null,
        },
        ultimateBeneficiaries: null,
        isThisTrustRevocable: null,
        isGenerationSkippingTransferTaxExempt: true,
        inclusionRatio: 'str',
      },
      {
        distributionEvent: {
          distributionType: 'OtherEvent',
          distributesBasedOn: null,
          distributesBasedOnCondition: null,
          timeBasedLengthYears: null,
          otherDescription: 'Additional Information',
          isTimeBasedEventTrigger: false,
          deathTriggerContactId: null,
          termTrigger: null,
        },
        distributionPercentage: 50,
        name: 'Name of Sub-Trust 2',
        fundingDirectiveType: null,
        fundingDirectiveAmount: null,
        flowchartFundingAmount: null,
        beneficiaryDuringTrustPeriod: null,
        otherBeneficiaryUponDeath: null,
        mandatoryIncomeDistribution: 'atLeastQuarterly',
        principalDistribution: '2',
        otherIncomeDistributionStandard: null,
        powerOfAppointment: {
          hasPowerOfAppointment: true,
          typeOfPower: 'general',
          appointmentBecomesEffective: 'lifetime',
          whoIsThePowerLimitedTo: null,
        },
        ultimateBeneficiaries: null,
        isThisTrustRevocable: null,
        isGenerationSkippingTransferTaxExempt: true,
        inclusionRatio: 'str22',
      },
    ],
    secondDeathBeneficiariesOutrightPercentageOfResiduary: 12,
    secondDeathBeneficiariesInTrustPercentageOfResiduary: 12,
    secondDeathBeneficiariesInTrust: null,
    policies: null,
    annuityDetails: null,
    shouldDistributeToBeneficiariesAfterDonorDeath: null,
    isSpouseBeneficiaryUponDeath: null,
    otherBeneficiaryUponDeath: null,
    unitrustRate: null,
    note: null,
    noteDate: null,
  }),
  trustType: 'RevocableTrust',
  trustors: null,
  type: 'Trust',
  wid: '4c824c2c-1a9d-4d4b-9235-1df4ed27e6da',
} as ContactCard;

export const CC_JRT = {
  addressCity: null,
  addressCountry: null,
  addressCountryShort: null,
  addressCounty: null,
  addressFormatted: null,
  addressLine1: null,
  addressLine2: null,
  addressState: 'Florida',
  addressStateShort: null,
  addressStreetName: null,
  addressStreetNumber: null,
  addressZipCode: null,
  aliases: [],
  children: null,
  childrenIds: null,
  citizenship: null,
  createdByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
  creationDate: '2024-06-21T10:43:54',
  directBeneficiaries: null,
  dob: null,
  ein: null,
  email: null,
  firstName: '',
  formOfIncorporation: null,
  geodata: null,
  hasChildren: false,
  hasFamilyMemberWithSpecialNeeds: false,
  hasPets: false,
  id: '3355b36b-273a-4459-882b-09d219ab1090',
  isDeceased: false,
  isDonorAdvisedFund: null,
  isGrantorTrust: false,
  isHideFromHeritageMap: false,
  isLegalNameConfirmed: false,
  isOutsideOfTaxableEstate: false,
  isPreviousChild: false,
  isPrimary: false,
  lastModifiedByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
  lastName: '',
  legalName: 'JRT Trust Legal Name',
  manualValue: 5435,
  manualValueAsOfDate: '2024-06-06',
  maritalStatus: null,
  middleName: null,
  modificationDate: '2024-06-24T12:03:43',
  nickname: 'Trust Nickname',
  notes: 'Additional Notes',
  owners: null,
  partner: null,
  partnerId: null,
  phoneNumber: null,
  relationship: null,
  revocableTrustType: 'Joint',
  spouses: null,
  tin: *********,
  trustCreationDate: '2024-06-02',
  trustInformation: JSON.stringify({
    trustCreators: ['363d21c0-c770-446b-936d-8561aa680b4e', '363d21c0-c770-446b-936d-8561aa680b4e'],
    isGrantorTrust: true,
    isGenerationSkippingTransferTaxExempt: false,
    currentBeneficiaries: null,
    currentTrustees: ['363d21c0-c770-446b-936d-8561aa680b4e', '363d21c0-c770-446b-936d-8561aa680b4e'],
    keyPeople: [
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'SuccessorTrustee' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'SecondSuccessorTrustee' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'ThirdSuccessorTrustee' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'FourthSuccessorTrustee' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'TrustProtector' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'SuccessorTrustProtector' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'TrusteeAppointer' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'SuccessorTrusteeAppointer' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'TrusteeRemover' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'DirectedAdministrativeTrustee' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'InvestmentAdvisor' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'DistributionAdvisor' },
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'FifthSuccessorTrustee' },
    ],
    initialFundingAmount: 8887.0,
    initialFundingDetails: 'Additional Funding Details',
    distributionEvent: null,
    beneficiariesUponTermination: null,
    survivingSpouseOutright: null,
    survivingSpouseOutrightPercentageOfResiduary: null,
    survivingSpouseInTrust: null,
    otherBeneficiariesOutrightPercentageOfResiduary: null,
    otherBeneficiariesInTrustPercentageOfResiduary: null,
    otherBeneficiariesInTrust: null,
    secondDeathBeneficiariesOutrightPercentageOfResiduary: null,
    secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
    secondDeathBeneficiariesInTrust: null,
    policies: null,
    annuityDetails: null,
    shouldDistributeToBeneficiariesAfterDonorDeath: null,
    isSpouseBeneficiaryUponDeath: null,
    otherBeneficiaryUponDeath: null,
    unitrustRate: null,
    note: null,
    noteDate: null,
  }),
  trustType: 'RevocableTrust',
  trustors: null,
  type: 'Trust',
  wid: '4c824c2c-1a9d-4d4b-9235-1df4ed27e6da',
} as ContactCard;

export const CC_GRAT = {
  addressCity: null,
  addressCountry: null,
  addressCountryShort: null,
  addressCounty: null,
  addressFormatted: null,
  addressLine1: null,
  addressLine2: null,
  addressState: 'Colorado',
  addressStateShort: null,
  addressStreetName: null,
  addressStreetNumber: null,
  addressZipCode: null,
  aliases: [],
  children: null,
  childrenIds: null,
  citizenship: null,
  createdByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
  creationDate: '2024-06-21T10:49:22',
  directBeneficiaries: null,
  dob: null,
  ein: null,
  email: null,
  firstName: '',
  formOfIncorporation: null,
  geodata: null,
  hasChildren: false,
  hasFamilyMemberWithSpecialNeeds: false,
  hasPets: false,
  id: '6b71ed9e-35f1-4301-a913-1809a8f7ad2e',
  isDeceased: false,
  isDonorAdvisedFund: null,
  isGrantorTrust: false,
  isHideFromHeritageMap: false,
  isLegalNameConfirmed: false,
  isOutsideOfTaxableEstate: false,
  isPreviousChild: false,
  isPrimary: false,
  lastModifiedByWid: null,
  lastName: '',
  legalName: 'GRAT Trust Legal Name',
  manualValue: 8787,
  manualValueAsOfDate: '2024-06-04',
  maritalStatus: null,
  middleName: null,
  modificationDate: '2024-06-21T10:49:22',
  nickname: 'Trust Nickname',
  notes: 'Additional Notes',
  owners: null,
  partner: null,
  partnerId: null,
  phoneNumber: null,
  relationship: null,
  revocableTrustType: 'GRAT',
  spouses: null,
  tin: *********,
  trustCreationDate: '2024-06-07',
  trustInformation: JSON.stringify({
    trustCreators: ['363d21c0-c770-446b-936d-8561aa680b4e', '363d21c0-c770-446b-936d-8561aa680b4e'],
    isGrantorTrust: true,
    isGenerationSkippingTransferTaxExempt: false,
    currentBeneficiaries: {
      isDistributedEvenly: true,
      hasContingentBeneficiaries: null,
      contingentBeneficiariesDistributionEvent: null,
      beneficiaries: [
        {
          contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
          distributionPercentage: 100,
          isDistributionThroughTrust: null,
          contingentBeneficiaries: null,
          note: null,
          noteDate: null,
        },
      ],
      hasGiftContingentBeneficiaries: null,
      hasSpecificGifts: null,
      cashGifts: null,
      assetGifts: null,
    },
    currentTrustees: ['363d21c0-c770-446b-936d-8561aa680b4e', '363d21c0-c770-446b-936d-8561aa680b4e'],
    keyPeople: [
      { contactId: '363d21c0-c770-446b-936d-8561aa680b4e', role: 'CurrentTrustee' },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'InitialTrustee',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'SuccessorTrustee',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'SecondSuccessorTrustee',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'ThirdSuccessorTrustee',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'FourthSuccessorTrustee',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'FifthSuccessorTrustee',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'TrustProtector',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'SuccessorTrustProtector',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'TrusteeAppointer',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'SuccessorTrusteeAppointer',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'TrusteeRemover',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'DirectedAdministrativeTrustee',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'InvestmentAdvisor',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'DistributionAdvisor',
      },
      {
        contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
        role: 'InvestmentAdvisor',
      },
    ],
    initialFundingAmount: 7676,
    initialFundingDetails: 'Additional Funding Details',
    distributionEvent: {
      distributionType: 'TimeBasedEvent',
      distributesBasedOn: null,
      distributesBasedOnCondition: null,
      timeBasedLengthYears: null,
      otherDescription: null,
      isTimeBasedEventTrigger: false,
      deathTriggerContactId: null,
      termTrigger: null,
    },
    beneficiariesUponTermination: {
      isDistributedEvenly: true,
      hasContingentBeneficiaries: true,
      contingentBeneficiariesDistributionEvent: {
        distributionType: 'TimeBasedEvent',
        distributesBasedOn: null,
        distributesBasedOnCondition: null,
        timeBasedLengthYears: null,
        otherDescription: null,
        isTimeBasedEventTrigger: false,
        deathTriggerContactId: null,
        termTrigger: null,
      },
      beneficiaries: [
        {
          contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
          distributionPercentage: 100,
          isDistributionThroughTrust: null,
          contingentBeneficiaries: {
            isDistributedEvenly: true,
            hasContingentBeneficiaries: true,
            contingentBeneficiariesDistributionEvent: null,
            beneficiaries: [
              {
                contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
                distributionPercentage: 50,
                isDistributionThroughTrust: false,
                contingentBeneficiaries: null,
                note: null,
                noteDate: null,
              },
              {
                contactId: '363d21c0-c770-446b-936d-8561aa680b4e',
                distributionPercentage: 50,
                isDistributionThroughTrust: null,
                contingentBeneficiaries: null,
                note: null,
                noteDate: null,
              },
            ],
            hasGiftContingentBeneficiaries: null,
            hasSpecificGifts: null,
            cashGifts: null,
            assetGifts: null,
          },
          note: null,
          noteDate: null,
        },
      ],
      hasGiftContingentBeneficiaries: null,
      hasSpecificGifts: null,
      cashGifts: null,
      assetGifts: null,
    },
    survivingSpouseOutright: null,
    survivingSpouseOutrightPercentageOfResiduary: null,
    survivingSpouseInTrust: null,
    otherBeneficiariesOutrightPercentageOfResiduary: null,
    otherBeneficiariesInTrustPercentageOfResiduary: null,
    otherBeneficiariesInTrust: null,
    secondDeathBeneficiariesOutrightPercentageOfResiduary: null,
    secondDeathBeneficiariesInTrustPercentageOfResiduary: null,
    secondDeathBeneficiariesInTrust: null,
    policies: null,
    annuityDetails: {
      term: '7',
      rate: 12,
      recipient: '363d21c0-c770-446b-936d-8561aa680b4e',
      recipients: null,
    },
    shouldDistributeToBeneficiariesAfterDonorDeath: null,
    isSpouseBeneficiaryUponDeath: null,
    otherBeneficiaryUponDeath: null,
    unitrustRate: null,
    note: null,
    noteDate: null,
  }),
  trustType: 'IrrevocableTrust',
  trustors: null,
  type: 'Trust',
  wid: '4c824c2c-1a9d-4d4b-9235-1df4ed27e6da',
} as ContactCard;

export const CC_Primary = {
  addressCity: null,
  addressCountry: null,
  addressCountryShort: null,
  addressCounty: null,
  addressFormatted: null,
  addressLine1: null,
  addressLine2: null,
  addressState: null,
  addressStateShort: null,
  addressStreetName: null,
  addressStreetNumber: null,
  addressZipCode: null,
  aliases: null,
  children: null,
  childrenIds: null,
  citizenship: null,
  createdByWid: null,
  creationDate: '2024-06-21T10:27:40',
  directBeneficiaries: null,
  dob: null,
  ein: null,
  email: '<EMAIL>',
  firstName: '8722',
  formOfIncorporation: null,
  geodata: null,
  hasChildren: false,
  hasFamilyMemberWithSpecialNeeds: false,
  hasPets: false,
  id: '363d21c0-c770-446b-936d-8561aa680b4e',
  isDeceased: false,
  isDonorAdvisedFund: null,
  isGrantorTrust: false,
  isHideFromHeritageMap: false,
  isLegalNameConfirmed: false,
  isOutsideOfTaxableEstate: false,
  isPreviousChild: false,
  isPrimary: true,
  lastModifiedByWid: null,
  lastName: 'L',
  legalName: null,
  manualValue: null,
  manualValueAsOfDate: null,
  maritalStatus: null,
  middleName: null,
  modificationDate: '2024-06-21T10:27:40',
  nickname: null,
  notes: null,
  owners: null,
  partner: null,
  partnerId: null,
  phoneNumber: null,
  relationship: null,
  revocableTrustType: null,
  spouses: null,
  tin: null,
  trustCreationDate: null,
  trustInformation: null,
  trustType: null,
  trustors: null,
  type: 'Individual',
  wid: '4c824c2c-1a9d-4d4b-9235-1df4ed27e6da',
} as ContactCard;
