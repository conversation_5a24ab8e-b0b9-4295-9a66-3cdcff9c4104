import { Asset, ContactCard, TrustInformation } from '@wealthcom/visualizer';

export const contacts = [
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2024-07-17T02:47:21',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: '<EMAIL>',
    firstName: '8872',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '5b164674-b6af-475e-aacf-3f2206968b14',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: true,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-07-17T02:47:21',
    nickname: null,
    notes: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: '6adfb5fa-c446-4fcd-98c5-0e520c106d97',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
    creationDate: '2024-07-17T03:05:18',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'cb905a78-8984-479f-b90b-3fd824a6cc79',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
    lastName: '',
    legalName: 'Joseph Edmonds Living Trust',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-07-17T11:15:18',
    nickname: '',
    notes: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Individual',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: JSON.stringify({
      trustCreators: null,
      isGrantorTrust: true,
      isGenerationSkippingTransferTaxExempt: false,
      currentBeneficiaries: null,
      currentTrustees: null,
      keyPeople: null,
      initialFundingAmount: null,
      initialFundingDetails: null,
      distributionEvent: null,
      beneficiariesUponTermination: {
        isDistributedEvenly: true,
        hasContingentBeneficiaries: false,
        contingentBeneficiariesDistributionEvent: null,
        beneficiaries: [
          {
            contactId: '5b164674-b6af-475e-aacf-3f2206968b14',
            distributionPercentage: 33.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
          },
          {
            contactId: '5b164674-b6af-475e-aacf-3f2206968b14',
            distributionPercentage: 33.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
          },
          {
            contactId: '5b164674-b6af-475e-aacf-3f2206968b14',
            distributionPercentage: 33.0,
            isDistributionThroughTrust: null,
            contingentBeneficiaries: null,
            note: null,
            noteDate: null,
          },
        ],
        hasGiftContingentBeneficiaries: null,
        hasSpecificGifts: true,
        cashGifts: [
          {
            contactId: '5b164674-b6af-475e-aacf-3f2206968b14',
            value: 5000,
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: null,
          },
        ],
        assetGifts: [
          {
            contactId: '5b164674-b6af-475e-aacf-3f2206968b14',
            assetId: 'YgFAkdi1kEuO8aUc2JR1Cw',
            isDistributionThroughTrust: null,
            contingentBeneficiaryContactId: null,
          },
        ],
      },
      survivingSpouseOutright: null,
      survivingSpouseOutrightPercentageOfResiduary: null,
      survivingSpouseInTrust: {
        percentageOfResiduary: 90.0,
        maritalTrust: {
          distributionPercentage: null,
          name: 'Marital Trust',
          beneficiariesDuringTrustPeriod: null,
          fundingDirectiveType: 'pecuniaryAmount',
          fundingDirectiveAmount: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: [
            { distributionType: 'atLeastAnnually', otherDistributionDescription: null },
            { distributionType: 'atLeastQuarterly', otherDistributionDescription: null },
            { distributionType: 'fullyDiscretionaryDistributions', otherDistributionDescription: null },
            { distributionType: 'other', otherDistributionDescription: 'Other Distribution Standard' },
          ],
          principalDistribution: 'Principal Distributions - Discretionary distributions by the trustee:',
          distributionEvents: [
            {
              distributionType: 'powerOfAppointment',
              distributionAmount: null,
              distributionAge: null,
              is5and5WithdrawalRight: false,
              hasPowerOfAppointment: null,
              typeOfPower: 'limited',
              appointmentBecomesEffective: 'lifetime',
              whoIsThePowerLimitedTo: 'Spouse has the ability to redistribute assets to any individual or organization',
              ageWhenBeneficiaryCanExercise: null,
            },
          ],
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: {
            distributionType: 'DeathEvent',
            distributesBasedOn: 'PrimaryBeneficiary',
            distributesBasedOnCondition: null,
            timeBasedLengthYears: null,
            otherDescription: null,
            isTimeBasedEventTrigger: false,
            deathTriggerContactId: null,
            termTrigger: null,
          },
          isGenerationSkippingTransferTaxExempt: false,
          inclusionRatio: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        creditShelterTrust: {
          distributionPercentage: null,
          name: 'Credit Shelter Trust',
          beneficiariesDuringTrustPeriod: null,
          fundingDirectiveType: 'remainderAfterPecuniaryAmountToMaritalTrust',
          fundingDirectiveAmount: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: [{ distributionType: 'atLeastAnnually', otherDistributionDescription: null }],
          principalDistribution: 'LoremlpsumLoremLoremlpsumLoremLoremlpsumLoremLoremlpsumLorem',
          distributionEvents: [
            {
              distributionType: 'powerOfAppointment',
              distributionAmount: null,
              distributionAge: null,
              is5and5WithdrawalRight: false,
              hasPowerOfAppointment: null,
              typeOfPower: 'limited',
              appointmentBecomesEffective: 'lifetime',
              whoIsThePowerLimitedTo: 'Limited to any individual or organization.',
              ageWhenBeneficiaryCanExercise: 30,
            },
          ],
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: {
            distributionType: 'DeathEvent',
            distributesBasedOn: 'TrustorsSpouse',
            distributesBasedOnCondition: null,
            timeBasedLengthYears: null,
            otherDescription: null,
            isTimeBasedEventTrigger: false,
            deathTriggerContactId: null,
            termTrigger: null,
          },
          isGenerationSkippingTransferTaxExempt: false,
          inclusionRatio: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        survivorTrust: null,
        hasDifferentSecondLevelDistributions: false,
      },
      otherBeneficiariesOutrightPercentageOfResiduary: null,
      otherBeneficiariesInTrustPercentageOfResiduary: 10.0,
      otherBeneficiariesInTrust: [
        {
          distributionPercentage: 33.0,
          name: 'Trust for Linda Smith',
          beneficiariesDuringTrustPeriod: ['5b164674-b6af-475e-aacf-3f2206968b14'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        {
          distributionPercentage: 33.0,
          name: 'Trust for Finn Edmonds',
          beneficiariesDuringTrustPeriod: ['5b164674-b6af-475e-aacf-3f2206968b14'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        {
          distributionPercentage: 33.0,
          name: 'Trust for Olive Edmonds',
          beneficiariesDuringTrustPeriod: ['5b164674-b6af-475e-aacf-3f2206968b14'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: null,
          incomeDistributions: null,
          principalDistribution: null,
          distributionEvents: null,
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: null,
          isGenerationSkippingTransferTaxExempt: null,
          inclusionRatio: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
      ],
      secondDeathBeneficiariesOutrightPercentageOfResiduary: 10.0,
      secondDeathBeneficiariesInTrustPercentageOfResiduary: 90.0,
      secondDeathBeneficiariesInTrust: [
        {
          distributionPercentage: 33.0,
          name: 'Trust fbo Maia Griffin',
          beneficiariesDuringTrustPeriod: ['5b164674-b6af-475e-aacf-3f2206968b14'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: '5b164674-b6af-475e-aacf-3f2206968b14',
          incomeDistributions: [{ distributionType: 'atLeastQuarterly', otherDistributionDescription: null }],
          principalDistribution: null,
          distributionEvents: [
            {
              distributionType: 'powerOfAppointment',
              distributionAmount: null,
              distributionAge: null,
              is5and5WithdrawalRight: false,
              hasPowerOfAppointment: null,
              typeOfPower: 'limited',
              appointmentBecomesEffective: null,
              whoIsThePowerLimitedTo: null,
              ageWhenBeneficiaryCanExercise: null,
            },
          ],
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: {
            distributionType: 'OtherEvent',
            distributesBasedOn: null,
            distributesBasedOnCondition: null,
            timeBasedLengthYears: null,
            otherDescription: 'Beneficiary reaches age 28.',
            isTimeBasedEventTrigger: false,
            deathTriggerContactId: null,
            termTrigger: null,
          },
          isGenerationSkippingTransferTaxExempt: false,
          inclusionRatio: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
        {
          distributionPercentage: 66.0,
          name: 'Trust fbo Henry Griffin',
          beneficiariesDuringTrustPeriod: ['5b164674-b6af-475e-aacf-3f2206968b14'],
          fundingDirectiveType: null,
          fundingDirectiveAmount: null,
          flowchartFundingAmount: null,
          beneficiaryDuringTrustPeriod: null,
          otherBeneficiaryUponDeath: '5b164674-b6af-475e-aacf-3f2206968b14',
          incomeDistributions: [{ distributionType: 'atLeastQuarterly', otherDistributionDescription: null }],
          principalDistribution: null,
          distributionEvents: [
            {
              distributionType: 'powerOfAppointment',
              distributionAmount: null,
              distributionAge: null,
              is5and5WithdrawalRight: false,
              hasPowerOfAppointment: null,
              typeOfPower: 'limited',
              appointmentBecomesEffective: null,
              whoIsThePowerLimitedTo: null,
              ageWhenBeneficiaryCanExercise: null,
            },
          ],
          hasUltimateBeneficiaries: null,
          ultimateBeneficiaries: null,
          isThisTrustRevocable: null,
          distributionEvent: {
            distributionType: 'OtherEvent',
            distributesBasedOn: null,
            distributesBasedOnCondition: null,
            timeBasedLengthYears: null,
            otherDescription: 'bbbb 20',
            isTimeBasedEventTrigger: false,
            deathTriggerContactId: null,
            termTrigger: null,
          },
          isGenerationSkippingTransferTaxExempt: false,
          inclusionRatio: null,
          mandatoryIncomeDistribution: null,
          otherIncomeDistributionStandard: null,
          powerOfAppointment: null,
        },
      ],
      annuityDetails: null,
      isSpouseBeneficiaryUponDeath: null,
      otherBeneficiaryUponDeath: null,
      unitrustRate: null,
      note: null,
      noteDate: null,
      incomeDistributions: null,
      principalDistribution: null,
      distributionEvents: null,
      hasUltimateBeneficiaries: null,
      ultimateBeneficiaries: null,
      shouldDistributeToBeneficiariesAfterDonorDeath: null,
      policies: null,
    } as TrustInformation),
    trustType: 'RevocableTrust',
    trustors: null,
    type: 'Trust',
    wid: '6adfb5fa-c446-4fcd-98c5-0e520c106d97',
  },
] as ContactCard[];

export const assets = [
  {
    assetDescription: null,
    assetId: 'YgFAkdi1kEuO8aUc2JR1Cw',
    assetInfo:
      '{"streetAddress":" Rolex Lane","city":"Lexington","state":"Kentucky","zipCode":"40511","countryCode":"United States","useZillow":false,"nickname":"1994 Rolex Datejust","descriptionEstatePlan":"","estimateValue":0.0,"purchaseCost":18000.0,"asOfDate":"2024-07-17T03:01:40.451+00:00","isFavorite":false}',
    assetInfoType: 'ManualRealEstate',
    assetMask: null,
    assetName: null,
    assetOwnerName: null,
    balanceAsOf: '2024-07-17T03:02:24+00:00',
    balanceCostBasis: 18000,
    balanceCostFrom: 'UserManual',
    balanceCurrent: 0,
    balanceFrom: 'UserManual',
    balancePrice: null,
    balancePriceFrom: 'UserManual',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: '5b164674-b6af-475e-aacf-3f2206968b14',
    creationDate: '2024-07-17T03:02:24+00:00',
    currencyCode: null,
    deactivateBy: null,
    descriptionEstatePlan: '',
    hasInvestment: null,
    includeInNetWorth: true,
    institutionId: 101,
    institutionName: null,
    integration: null,
    integrationAccountId: null,
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: null,
    lastUpdate: '2024-07-17T03:01:40+00:00',
    lastUpdateAttempt: '2024-07-17T03:02:24+00:00',
    logoName: null,
    modificationDate: null,
    nextUpdate: null,
    nickname: '1994 Rolex Datejust',
    note: null,
    noteDate: null,
    ownership: null,
    primaryAssetCategory: 'RealEstate',
    status: null,
    statusCode: null,
    userInstitutionId: 'auDNSMlKmEunomXMBdiYQA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: null,
    vendorResponseType: 'Other',
    wealthAssetType: 'RealEstate',
    wid: '6adfb5fa-c446-4fcd-98c5-0e520c106d97',
  },
] as Asset[];
