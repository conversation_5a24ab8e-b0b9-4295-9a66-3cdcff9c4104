import { Asset, ContactCard } from '@wealthcom/visualizer';

export const Contacts = [
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:25',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Mokiao',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '7cc0bfde-c885-4c6f-be4e-3c1898c4f592',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 24',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: 'Married',
    middleName: null,
    modificationDate: '2025-03-13T01:35:25',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369515',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:26',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Piotrowski',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '27fbc763-72c2-4686-9719-a0d848acce8e',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Kanthak 26',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:26',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369530',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:27',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Grumbach',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '565c4822-fce5-4610-a22c-66c2003b5ffa',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Czyzewski 2',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:27',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '453217',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:28',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Piotrowski',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'd5533c65-65b0-4f4c-bec2-066e4c86c7e9',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Kanthak 25',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:28',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369522',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:28',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '839fe32b-ad36-4ae4-aa9d-14f9673b5f4a',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 15',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:28',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369436',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:28',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '50f7e381-e322-4d6e-825a-5de162f824d5',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 10',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:28',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369438',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:29',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Piotrowski',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '0fb5144c-b061-49e2-8f84-51dd3bbe1e4d',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Kanthak 23',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:29',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369531',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:31',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'a8655bc3-5124-4880-ac96-6005dc6bb4f9',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 12',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:31',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369381',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:32',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Piotrowski',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '4dfca0fc-9a74-42f0-b656-137c3f668b5a',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Kanthak 1',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:32',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369519',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:33',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'ab53e4f3-162f-41eb-a2fc-2e406b307fe4',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 16',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:33',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369395',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:33',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Mokiao',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '9a28e1d3-bc9b-4e62-8710-bb1294aa46f3',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 28',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:33',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369512',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:32',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'a40eea81-d68d-4689-b2bc-c39edb596bb8',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 11',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:32',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369402',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:33',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'a5110f06-383d-481b-8505-0d9d81140ee6',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 8',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:33',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369378',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:33',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Debose',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'a44d539d-4f7f-44af-9709-f621aa244fad',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Roever 5700',
    legalName: 'Debose Roever 5700',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:33',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '185296',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:33',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Grumbach',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '4e18372d-0299-42d6-a096-a09defd96998',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Czyzewski 1',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:33',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '453216',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:34',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Mokiao',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'bbbb659e-7d3d-449f-a125-bb2fdff5774e',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 5',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:34',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369373',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:34',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '********-4f5b-4591-87d1-d1ca3e6f1601',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 4',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:34',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369390',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:34',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'ABC',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '9779556a-3f1d-4404-9752-27d3d30bbef4',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Test',
    legalName: 'Mr. ABC Test',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:34',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '579246',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:34',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Piotrowski',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '0aa554b3-233e-4220-809a-1d784b04592a',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Kanthak 22',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:34',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369527',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:34',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Piotrowski',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '3064ea78-b500-4425-a4cb-58fe5e9a790a',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Kanthak 21',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:34',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369516',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:34',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '7e5af51f-f5f4-4a69-8085-e107f2a9a75f',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 7',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:34',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369380',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:34',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Mokiao',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '0f82d14f-097c-4bd3-b87b-aa27a1c09fe5',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 13',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:34',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369357',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:34',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '0d8c4684-e89b-442f-b9e7-f919b1ed5877',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 14',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:34',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369383',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:35',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Piotrowski',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'ff41347a-6c89-4779-9229-3d72775d3a86',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Kanthak 18',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:35',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369739',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:35',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '85b61d9a-6834-481f-a53c-09dd840d4329',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 9',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:35',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369440',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:35',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'd9d77e97-a8b6-4a7e-bdca-aac446ebc447',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 3',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:35',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369375',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:35',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Mokiao',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'ff29b47e-f1bf-4dc4-96e1-8692905d9aaa',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 2',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:35',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369359',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:35',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Weiner',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '4d7f765c-da48-4955-b353-67d8aabce2dd',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Runion 6',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:35',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369442',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:35',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Piotrowski',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '34e2eab7-bc57-428b-9309-d20d18998f9b',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Kanthak 20',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:35',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369525',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2025-03-13T01:35:36',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: null,
    familyName: null,
    firstName: 'Piotrowski',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '527d34ea-19fb-49e2-8f01-221a9e02396f',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Kanthak 27',
    legalName: '',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-13T01:35:36',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    vendorId: '369785',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'New York',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '78090f0d-2c4a-4dd6-819d-ecda72eae2b0',
    creationDate: '2025-03-18T11:42:59',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: 'a3f080c4-247d-40f5-ac97-0714cbbb7581',
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '40cdf69e-42cb-49a7-92b3-4dd133e9acc8',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: '',
    legalName: 'Will of Nancy L. Colarusso',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-18T11:42:59',
    nickname: null,
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: '2022-03-14',
    trustInformation:
      '{"trustCreators":["65ff80c7-5816-4f41-855e-e6905ab4b827"],"isGrantorTrust":null,"isGenerationSkippingTransferTaxExempt":null,"currentBeneficiaries":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":null,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":[],"hasGiftContingentBeneficiaries":null,"hasSpecificGifts":true,"cashGifts":[],"assetGifts":[],"otherGifts":"- Michael E. Dianda, Jr.: $200,000 - Lester John Andrew Zaborski, III: $150,000 - Manuel E. Carreras: $100,000 - Calista F. Dianda: $25,000 - Talika L. Tuczynski: $50,000 - Frank J. Dianda: $200,000 - International Fellowship of Christians and Jews: $100,000 - Rescue Mission in Albany, New York: $100,000 - Louis J. Tuczynski, III and Jill K. Tuczynski: Tangible personal property"},"currentTrustees":["4f0abbc3-435a-415a-8075-b79ea3efdbc6"],"keyPeople":[{"contactId":"586ea5dc-3192-43e4-80ff-7d6c0e3a727e","role":"FirstAlternateExecutor"},{"contactId":"7a429664-cdd7-428d-b564-6ee7337d412e","role":"SecondAlternateExecutor"}],"initialFundingAmount":null,"initialFundingDetails":null,"distributionEvent":null,"beneficiariesUponTermination":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":false,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":null,"hasGiftContingentBeneficiaries":null,"hasSpecificGifts":false,"cashGifts":null,"assetGifts":null,"otherGifts":null},"associatedTrustForPourOverWill":null,"survivingSpouseOutright":null,"survivingSpouseOutrightPercentageOfResiduary":0.0,"survivingSpouseInTrust":{"percentageOfResiduary":null,"maritalTrust":{"distributionPercentage":null,"name":null,"beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},"creditShelterTrust":{"distributionPercentage":null,"name":null,"beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},"survivorTrust":{"distributionPercentage":null,"name":null,"beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},"hasDifferentSecondLevelDistributions":false},"otherBeneficiariesOutrightPercentageOfResiduary":100.0,"otherBeneficiariesInTrustPercentageOfResiduary":0.0,"otherBeneficiariesInTrust":[],"secondDeathBeneficiariesOutrightPercentageOfResiduary":null,"secondDeathBeneficiariesInTrustPercentageOfResiduary":null,"secondDeathBeneficiariesInTrust":[],"secondDeathCustom":null,"annuityDetails":null,"isSpouseBeneficiaryUponDeath":null,"otherBeneficiaryUponDeath":null,"unitrustRate":null,"note":null,"noteDate":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"hasNoContestClause":null,"hasSpendThriftClause":null,"hasSpecialNeedsProvision":null,"hasSpousalDisclaimer":null,"hasQualifiedDomesticTrust":null,"hasQualifiedSubChapterProvision":null,"hasDiscretionaryDistributionProvision":null,"hasDigitalAssetsPowers":null,"hasGstProvision":null,"hasConduitTrust":null,"hasAccumulationTrust":null,"shouldDistributeToBeneficiariesAfterDonorDeath":null,"policies":[],"displayInformation":null}',
    trustType: 'PourOverWill',
    trustors: null,
    type: 'Will',
    vendorId: null,
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'Florida',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '78090f0d-2c4a-4dd6-819d-ecda72eae2b0',
    creationDate: '2025-03-18T11:45:52',
    currentParties: null,
    directBeneficiaries: null,
    dob: null,
    economicSchedule: null,
    ein: null,
    email: null,
    esterJobId: '6e7871f8-1e69-4b16-990f-4882ecdbafaa',
    familyName: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: 'cd766c52-ad25-4b6c-ac6c-642c1850ca26',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: '',
    legalName: 'Revocable Trust Agreement, Of, Frank Distel',
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2025-03-18T11:45:52',
    nickname: '',
    notes: '',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Individual',
    spouses: null,
    tin: null,
    trustCreationDate: '1993-02-04',
    trustInformation:
      '{"trustCreators":["24c6df04-e5f2-420f-aac1-5cc94ec0a9c6"],"isGrantorTrust":null,"isGenerationSkippingTransferTaxExempt":null,"currentBeneficiaries":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":null,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":[],"hasGiftContingentBeneficiaries":null,"hasSpecificGifts":false,"cashGifts":null,"assetGifts":null,"otherGifts":null},"currentTrustees":["24c6df04-e5f2-420f-aac1-5cc94ec0a9c6"],"keyPeople":[{"contactId":"41262bbc-dc71-457c-97f0-137c29fc9531","role":"SuccessorTrustee"},{"contactId":"5729bad5-3265-49dd-aafb-c770c86fa42c","role":"SecondSuccessorTrustee"}],"initialFundingAmount":null,"initialFundingDetails":null,"distributionEvent":null,"beneficiariesUponTermination":{"customDistribution":null,"isDistributedEvenly":true,"hasContingentBeneficiaries":false,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":null,"hasGiftContingentBeneficiaries":null,"hasSpecificGifts":false,"cashGifts":null,"assetGifts":null,"otherGifts":null},"associatedTrustForPourOverWill":null,"survivingSpouseOutright":null,"survivingSpouseOutrightPercentageOfResiduary":0.0,"survivingSpouseInTrust":{"percentageOfResiduary":null,"maritalTrust":{"distributionPercentage":null,"name":null,"beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},"creditShelterTrust":{"distributionPercentage":null,"name":null,"beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},"survivorTrust":{"distributionPercentage":null,"name":null,"beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"fundingDirectiveOther":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"displayInformation":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},"hasDifferentSecondLevelDistributions":false},"otherBeneficiariesOutrightPercentageOfResiduary":100.0,"otherBeneficiariesInTrustPercentageOfResiduary":0.0,"otherBeneficiariesInTrust":[],"secondDeathBeneficiariesOutrightPercentageOfResiduary":null,"secondDeathBeneficiariesInTrustPercentageOfResiduary":null,"secondDeathBeneficiariesInTrust":[],"secondDeathCustom":"- **Mary Susan Madsen**: 50% outright\\\\n- **John Franklin Distel**: 50% outright\\\\n- **Natural issue of a deceased child**: Share of their parent, per stirpes, outright.\\\\n- **Surviving child if other child dies without natural issue**: 100% outright.","annuityDetails":null,"isSpouseBeneficiaryUponDeath":null,"otherBeneficiaryUponDeath":null,"unitrustRate":null,"note":null,"noteDate":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"hasNoContestClause":null,"hasSpendThriftClause":null,"hasSpecialNeedsProvision":null,"hasSpousalDisclaimer":null,"hasQualifiedDomesticTrust":null,"hasQualifiedSubChapterProvision":null,"hasDiscretionaryDistributionProvision":null,"hasDigitalAssetsPowers":null,"hasGstProvision":null,"hasConduitTrust":null,"hasAccumulationTrust":null,"shouldDistributeToBeneficiariesAfterDonorDeath":null,"policies":[],"displayInformation":null}',
    trustType: 'RevocableTrust',
    trustors: null,
    type: 'Trust',
    vendorId: null,
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
] as ContactCard[];

export const Assets = [
  {
    assetDescription: '',
    assetId: 'IqDRAuGpCEeNFhj4JBsGQw',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Debose Roever',
    assetOwnerName: 'Debose Roever',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '2505811#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"a44d539d-4f7f-44af-9709-f621aa244fad","type":"Individual","name":"Debose Roever","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'EjhroxVRo0CZ0vEUspbOxA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 3',
    assetOwnerName: 'Piotrowski Runion 3',
    balanceAsOf: '2025-03-10T00:00:00+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 344520.38,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: {
      majorAssetClasses: [
        {
          assetClasses: [
            {
              minorAssetClass: 'UsEquity',
              value: 531660,
              __typename: 'MinorAssetClass',
            },
          ],
          majorClass: 'PublicEquity',
          __typename: 'MajorAssetClass',
        },
        {
          assetClasses: [
            {
              minorAssetClass: 'Cash',
              value: -187139.*********,
              __typename: 'MinorAssetClass',
            },
          ],
          majorClass: 'CashDepositsMoneyMarketFunds',
          __typename: 'MajorAssetClass',
        },
      ],
      __typename: 'Holdings',
    },
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520695#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-10T00:00:00+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"d9d77e97-a8b6-4a7e-bdca-aac446ebc447","type":"Individual","name":"Piotrowski Runion 3","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'ucGO5mISjEeKzNa4NmQ6iQ',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 1',
    assetOwnerName: 'Piotrowski Runion 1',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520646#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"4dfca0fc-9a74-42f0-b656-137c3f668b5a","type":"Individual","name":"Piotrowski Runion 1","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'snvwel2mD0Kglys2ruFvbg',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 11',
    assetOwnerName: 'Piotrowski Runion 11',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520682#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"a40eea81-d68d-4689-b2bc-c39edb596bb8","type":"Individual","name":"Piotrowski Runion 11","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '0SVjkIYuJ0KGLa0AbfqGaQ',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 20',
    assetOwnerName: 'Piotrowski Runion 20',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520652#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"34e2eab7-bc57-428b-9309-d20d18998f9b","type":"Individual","name":"Piotrowski Runion 20","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'gaIRQ0j8QUaTxM0Ooe4eyA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 24',
    assetOwnerName: 'Piotrowski Runion 24',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520670#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"7cc0bfde-c885-4c6f-be4e-3c1898c4f592","type":"Individual","name":"Piotrowski Runion 24","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'q1DZuZkg3UOP3dOEk31AtA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 2',
    assetOwnerName: 'Piotrowski Runion 2',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520668#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"ff29b47e-f1bf-4dc4-96e1-8692905d9aaa","type":"Individual","name":"Piotrowski Runion 2","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '8BGxzsQaFUuhM5QI5k06yA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 23',
    assetOwnerName: 'Piotrowski Runion 23',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520658#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"0fb5144c-b061-49e2-8f84-51dd3bbe1e4d","type":"Individual","name":"Piotrowski Runion 23","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '3rSGlsSiXUWST7s6UqHxJA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 7',
    assetOwnerName: 'Piotrowski Runion 7',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520705#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"7e5af51f-f5f4-4a69-8085-e107f2a9a75f","type":"Individual","name":"Piotrowski Runion 7","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'Lz5aNr6PnUudQw4T8KoQaA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 9',
    assetOwnerName: 'Piotrowski Runion 9',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520712#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"85b61d9a-6834-481f-a53c-09dd840d4329","type":"Individual","name":"Piotrowski Runion 9","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'bCzGbuglikO0hn9yDQle5Q',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 14',
    assetOwnerName: 'Piotrowski Runion 14',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520687#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"0d8c4684-e89b-442f-b9e7-f919b1ed5877","type":"Individual","name":"Piotrowski Runion 14","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'ufR5goJg306jADa6HhMSMA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Grumbach Czyzewski 2',
    assetOwnerName: 'Grumbach Czyzewski 2',
    balanceAsOf: '2025-03-10T00:00:00+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 3253.46,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: {
      majorAssetClasses: [
        {
          assetClasses: [
            {
              minorAssetClass: 'UsEquity',
              value: 3253.46,
              __typename: 'MinorAssetClass',
            },
          ],
          majorClass: 'PublicEquity',
          __typename: 'MajorAssetClass',
        },
      ],
      __typename: 'Holdings',
    },
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '2468717#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-10T00:00:00+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"565c4822-fce5-4610-a22c-66c2003b5ffa","type":"Individual","name":"Grumbach Czyzewski 2","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'EKAWe5pmAEaS8mpdV579ig',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 5',
    assetOwnerName: 'Piotrowski Runion 5',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520678#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"bbbb659e-7d3d-449f-a125-bb2fdff5774e","type":"Individual","name":"Piotrowski Runion 5","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'VfTgVztCIkKTc1SjvyxjwQ',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 22',
    assetOwnerName: 'Piotrowski Runion 22',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520656#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"0aa554b3-233e-4220-809a-1d784b04592a","type":"Individual","name":"Piotrowski Runion 22","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '9fUXeDjY20OwHrMSqOgPFw',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 2',
    assetOwnerName: 'Piotrowski Runion 2',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520668#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"ff29b47e-f1bf-4dc4-96e1-8692905d9aaa","type":"Individual","name":"Piotrowski Runion 2","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '6JwnJvOOs0mRqh8D9Ri49g',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 16',
    assetOwnerName: 'Piotrowski Runion 16',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520693#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"ab53e4f3-162f-41eb-a2fc-2e406b307fe4","type":"Individual","name":"Piotrowski Runion 16","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'dAa6tmSpJUGOcWxehWC0Zg',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 25',
    assetOwnerName: 'Piotrowski Runion 25',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520660#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"d5533c65-65b0-4f4c-bec2-066e4c86c7e9","type":"Individual","name":"Piotrowski Runion 25","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'P6wHSKgwKEe1DDVr67tzVA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 6',
    assetOwnerName: 'Piotrowski Runion 6',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520703#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"4d7f765c-da48-4955-b353-67d8aabce2dd","type":"Individual","name":"Piotrowski Runion 6","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'N3Fgx9KkaEeuSVdFBLTwAA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 27',
    assetOwnerName: 'Piotrowski Runion 27',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520664#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"527d34ea-19fb-49e2-8f01-221a9e02396f","type":"Individual","name":"Piotrowski Runion 27","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'MYFlkMppx0GR7MhVl3OERg',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 10',
    assetOwnerName: 'Piotrowski Runion 10',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520680#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"50f7e381-e322-4d6e-825a-5de162f824d5","type":"Individual","name":"Piotrowski Runion 10","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'lauF9QuKdUm0FkrJ2wwrKw',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Grumbach Czyzewski 1',
    assetOwnerName: 'Grumbach Czyzewski 1',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '2468714#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"4e18372d-0299-42d6-a096-a09defd96998","type":"Individual","name":"Grumbach Czyzewski 1","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'UMN8tLWj70yjphhcOuUavA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 3',
    assetOwnerName: 'Piotrowski Runion 3',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520695#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"d9d77e97-a8b6-4a7e-bdca-aac446ebc447","type":"Individual","name":"Piotrowski Runion 3","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'ZTe46950g0GStPaGtUTg7g',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 18',
    assetOwnerName: 'Piotrowski Runion 18',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520648#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"ff41347a-6c89-4779-9229-3d72775d3a86","type":"Individual","name":"Piotrowski Runion 18","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '0a90mfxQk0eNGUXHmURlvg',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 8',
    assetOwnerName: 'Piotrowski Runion 8',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520710#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"a5110f06-383d-481b-8505-0d9d81140ee6","type":"Individual","name":"Piotrowski Runion 8","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'rZkAyiCey0CJdRhIBGKX0A',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Mr. ABC',
    assetOwnerName: 'Mr. ABC Test',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '2532980#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"9779556a-3f1d-4404-9752-27d3d30bbef4","type":"Individual","name":"Mr. ABC Test","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'P4b33CT1uEmYpc4bofu8UA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 26',
    assetOwnerName: 'Piotrowski Runion 26',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520662#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"27fbc763-72c2-4686-9719-a0d848acce8e","type":"Individual","name":"Piotrowski Runion 26","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'GK2ISxFXQUSFchgGDdy4kA',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 28',
    assetOwnerName: 'Piotrowski Runion 28',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520672#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"9a28e1d3-bc9b-4e62-8710-bb1294aa46f3","type":"Individual","name":"Piotrowski Runion 28","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '7rSkq0yXGESDszSv5HP8dw',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 13',
    assetOwnerName: 'Piotrowski Runion 13',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520666#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"0f82d14f-097c-4bd3-b87b-aa27a1c09fe5","type":"Individual","name":"Piotrowski Runion 13","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'Iq8oIfQ390SO1JAaloVnzg',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 15',
    assetOwnerName: 'Piotrowski Runion 15',
    balanceAsOf: '2025-03-13T01:36:08+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:08+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520691#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:08+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:08+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:08+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"839fe32b-ad36-4ae4-aa9d-14f9673b5f4a","type":"Individual","name":"Piotrowski Runion 15","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '1woIjaBxTEmceffeNfBMbQ',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 5',
    assetOwnerName: 'Piotrowski Runion 5',
    balanceAsOf: '2025-03-13T01:36:09+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:09+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520678#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:09+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:09+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:09+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"bbbb659e-7d3d-449f-a125-bb2fdff5774e","type":"Individual","name":"Piotrowski Runion 5","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'BD6cxPEVokKhEuSilg7l3g',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 4',
    assetOwnerName: 'Piotrowski Runion 4',
    balanceAsOf: '2025-03-13T01:36:09+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:09+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520699#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:09+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:09+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:09+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"********-4f5b-4591-87d1-d1ca3e6f1601","type":"Individual","name":"Piotrowski Runion 4","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'IlYj2MOoeUm7StUqR7I5hQ',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 12',
    assetOwnerName: 'Piotrowski Runion 12',
    balanceAsOf: '2025-03-13T01:36:09+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:09+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520685#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:09+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:09+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:09+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"a8655bc3-5124-4880-ac96-6005dc6bb4f9","type":"Individual","name":"Piotrowski Runion 12","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '3DehrHQzTEq2eWeewkbwvQ',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'PWMUATTEST 2',
    assetOwnerName: '',
    balanceAsOf: '2024-12-20T00:00:00+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:09+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: {
      majorAssetClasses: [
        {
          assetClasses: [
            {
              minorAssetClass: 'SecurityBasedLoans',
              value: 0,
              __typename: 'MinorAssetClass',
            },
          ],
          majorClass: 'Liabilities',
          __typename: 'MajorAssetClass',
        },
      ],
      __typename: 'Holdings',
    },
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: 'Liability#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2024-12-20T00:00:00+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:09+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:09+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership: null,
    primaryAssetCategory: 'Liability',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Loan',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'CP2z9XNbX0KwfxCOOA83hg',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'PWMUATTEST 2',
    assetOwnerName: '',
    balanceAsOf: '2024-12-20T00:00:00+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:09+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: {
      majorAssetClasses: [
        {
          assetClasses: [
            {
              minorAssetClass: 'SecurityBasedLoans',
              value: 0,
              __typename: 'MinorAssetClass',
            },
          ],
          majorClass: 'Liabilities',
          __typename: 'MajorAssetClass',
        },
      ],
      __typename: 'Holdings',
    },
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: 'Liability#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2024-12-20T00:00:00+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:09+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:09+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership: null,
    primaryAssetCategory: 'Liability',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Loan',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: 'MNo9zQeFo0ehsb87zYyxOQ',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 4',
    assetOwnerName: 'Piotrowski Runion 4',
    balanceAsOf: '2025-03-13T01:36:09+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:09+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520699#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:09+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:09+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:09+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"********-4f5b-4591-87d1-d1ca3e6f1601","type":"Individual","name":"Piotrowski Runion 4","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: '',
    assetId: '4DCPSmR8xkenSjJFlp1NVQ',
    assetInfo: null,
    assetInfoType: 'ManualCash',
    assetMask: null,
    assetName: 'Piotrowski Runion 21',
    assetOwnerName: 'Piotrowski Runion 21',
    balanceAsOf: '2025-03-13T01:36:09+00:00',
    balanceCostBasis: null,
    balanceCostFrom: 'Vendor',
    balanceCurrent: 0,
    balanceFrom: 'Vendor',
    balancePrice: null,
    balancePriceFrom: 'Vendor',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: 'd3e8698e-c6c0-44bc-9f78-f76f94e55d94',
    creationDate: '2025-03-13T01:36:09+00:00',
    currencyCode: 'US',
    deactivateBy: null,
    descriptionEstatePlan: null,
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 4000009,
    institutionName: 'Goldman Sachs',
    integration: null,
    integrationAccountId: '520654#*********',
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: true,
    lastUpdate: '2025-03-13T01:36:09+00:00',
    lastUpdateAttempt: '2025-03-13T01:36:09+00:00',
    logoName: 'static/institution/favicon/4000009.svg',
    modificationDate: '2025-03-13T01:36:09+00:00',
    nextUpdate: null,
    nickname: null,
    note: null,
    noteDate: null,
    ownership:
      '{"ownershipType":"NotJoint","owners":[{"id":"3064ea78-b500-4425-a4cb-58fe5e9a790a","type":"Individual","name":"Piotrowski Runion 21","percentage":100.0}]}',
    primaryAssetCategory: 'Investment',
    status: null,
    statusCode: 'AutoUpdateAvailable',
    userInstitutionId: 'r8WoK1HdMEezYs9Ygy4yDA',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: '{"Data":"REPLACE_ME"}',
    vendorResponseType: 'Other',
    wealthAssetType: 'Brokerage',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
  {
    assetDescription: null,
    assetId: 'afJfx7AsikSru7xERBgrZg',
    assetInfo:
      '{"manualAddType":0,"modelYear":0,"nickname":"123","descriptionEstatePlan":"","estimateValue":1111111,"purchaseCost":0,"asOfDate":"2025-03-13T16:33:40.539+00:00","isFavorite":false}',
    assetInfoType: 'ManualVehicle',
    assetMask: null,
    assetName: null,
    assetOwnerName: null,
    balanceAsOf: '2025-03-13T16:33:48+00:00',
    balanceCostBasis: 0,
    balanceCostFrom: 'UserManual',
    balanceCurrent: 1111111,
    balanceFrom: 'UserManual',
    balancePrice: null,
    balancePriceFrom: 'UserManual',
    balanceQuantityCurrent: null,
    beneficiaryComposition: null,
    cognitoId: '1f081cba-7d69-43b7-8718-36a9ca306b44',
    creationDate: '2025-03-13T16:33:48+00:00',
    currencyCode: null,
    deactivateBy: null,
    descriptionEstatePlan: '',
    hasInvestment: null,
    holdings: null,
    includeInNetWorth: true,
    institutionId: 101,
    institutionName: null,
    integration: null,
    integrationAccountId: null,
    isActive: true,
    isAsset: true,
    isFavorite: false,
    isLinkedVendor: null,
    lastUpdate: '2025-03-13T16:33:41+00:00',
    lastUpdateAttempt: '2025-03-13T16:33:48+00:00',
    logoName: null,
    modificationDate: null,
    nextUpdate: null,
    nickname: '123',
    note: null,
    noteDate: null,
    ownership: null,
    primaryAssetCategory: 'OtherProperty',
    status: null,
    statusCode: null,
    userInstitutionId: 'zgzeR5i2aEuAhQdczICgfQ',
    vendorAccountType: null,
    vendorContainer: null,
    vendorResponse: null,
    vendorResponseType: 'Other',
    wealthAssetType: 'Vehicle',
    wid: '2e2be9b2-518d-4831-8717-1c26d634c689',
  },
] as Asset[];
