import { ContactCard } from '@wealthcom/visualizer';

export const contacts = [
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'California',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2024-11-15T17:31:35',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: '<EMAIL>',
    esterJobId: null,
    firstName: 'Estate',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'a38366fd-2082-4c61-aa15-68e34bd1c635',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: true,
    lastModifiedByWid: null,
    lastName: 'Flow',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-11-15T17:31:35',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: '57adc476-00ec-41be-a0e8-97a10e6488ff',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: '',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    creationDate: '2024-11-15T17:33:53',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: null,
    esterJobId: null,
    firstName: '',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '07b01357-aa25-475c-b280-b9f11bdb4d92',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    lastName: '',
    legalName: 'IRT',
    manualValue: 10000,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-11-15T18:43:13',
    nickname: '',
    notes:
      'Income Distributions\nDistributions by the Trustee:\natLeastAnnually\n\nDistributions by the Trustee:\natLeastQuarterly\n\nDistributions by the Trustee:Income Distributions\nDistributions by the Trustee:\natLeastAnnually\n\nDistributions by the Trustee:\natLeastQuarterly\n\nDistributions by the Trustee:Income Distributions\nDistributions by the Trustee:\natLeastAnnually\n\nDistributions by the Trustee:\natLeastQuarterly\n\nDistributions by the Trustee:Income Distributions\nDistributions by the Trustee:\natLeastAnnually\n\nDistributions by the Trustee:\natLeastQuarterly\n\nDistributions by the Trustee:Income Distributions\nDistributions by the Trustee:\natLeastAnnually\n\nDistributions by the Trustee:\natLeastQuarterly\n\nDistributions by the Trustee:',
    otherRevocableTrustType: '',
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: 'Individual',
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation:
      '{"trustCreators":["a38366fd-2082-4c61-aa15-68e34bd1c635"],"isGrantorTrust":true,"isGenerationSkippingTransferTaxExempt":false,"currentBeneficiaries":{"isDistributedEvenly":true,"hasContingentBeneficiaries":null,"contingentBeneficiariesDistributionEvent":null,"beneficiaries":[{"contactId":"cd673f34-240c-4e08-98bc-111f25112561","distributionPercentage":33.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"a38366fd-2082-4c61-aa15-68e34bd1c635","distributionPercentage":33.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null},{"contactId":"cd673f34-240c-4e08-98bc-111f25112561","distributionPercentage":33.0,"isDistributionThroughTrust":null,"contingentBeneficiaries":null,"note":null,"noteDate":null,"principalDistribution":null,"incomeDistributions":null,"distributionEvents":null}],"hasGiftContingentBeneficiaries":false,"hasSpecificGifts":true,"cashGifts":[{"contactId":"a38366fd-2082-4c61-aa15-68e34bd1c635","value":1000,"isDistributionThroughTrust":null,"contingentBeneficiaryContactId":null}],"assetGifts":[{"contactId":"a38366fd-2082-4c61-aa15-68e34bd1c635","assetId":"Vrk8ZoG1IUavY1PHPDnalA","isDistributionThroughTrust":null,"contingentBeneficiaryContactId":null}]},"currentTrustees":null,"keyPeople":null,"initialFundingAmount":null,"initialFundingDetails":null,"distributionEvent":null,"beneficiariesUponTermination":null,"associatedTrustForPourOverWill":null,"survivingSpouseOutright":null,"survivingSpouseOutrightPercentageOfResiduary":10.0,"survivingSpouseInTrust":{"percentageOfResiduary":10.0,"maritalTrust":{"distributionPercentage":null,"name":"Marital Trust","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":"pecuniaryAmount","fundingDirectiveAmount":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":[{"distributionType":"atLeastAnnually","otherDistributionDescription":null},{"distributionType":"atLeastQuarterly","otherDistributionDescription":null},{"distributionType":"fullyDiscretionaryDistributions","otherDistributionDescription":null}],"principalDistribution":"GBFGBGbfbIncome Distributions\\nDistributions by the Trustee:\\natLeastAnnually\\n\\nDistributions by the Trustee:\\natLeastQuarterly\\n\\nDistributions by the Trustee:Income Distributions\\nDistributions by the Trustee:\\natLeastAnnually\\n\\nDistributions by the Trustee:\\natLeastQuarterly\\n\\nDistributions by the Trustee:Income Distributions\\nDistributions by the Trustee:\\natLeastAnnually\\n\\nDistributions by the Trustee:\\natLeastQuarterly\\n\\nDistributions by the Trustee:","distributionEvents":[{"distributionType":"interimMandatoryDistribution","distributionAmount":4.0,"distributionAge":"30","is5and5WithdrawalRight":false,"hasPowerOfAppointment":null,"typeOfPower":null,"appointmentBecomesEffective":null,"whoIsThePowerLimitedTo":null,"ageWhenBeneficiaryCanExercise":null}],"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":{"distributionType":"OtherEvent","distributesBasedOn":null,"distributesBasedOnCondition":null,"timeBasedLengthYears":null,"otherDescription":"52355235","isTimeBasedEventTrigger":false,"deathTriggerContactId":null,"termTrigger":null},"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},"creditShelterTrust":{"distributionPercentage":null,"name":"Credit Shelter Trust","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},"survivorTrust":null,"hasDifferentSecondLevelDistributions":false},"otherBeneficiariesOutrightPercentageOfResiduary":5.0,"otherBeneficiariesInTrustPercentageOfResiduary":30.0,"otherBeneficiariesInTrust":[{"distributionPercentage":10.0,"name":"Sub","beneficiariesDuringTrustPeriod":[],"fundingDirectiveType":null,"fundingDirectiveAmount":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":[{"distributionType":"atLeastAnnually","otherDistributionDescription":null}],"principalDistribution":"Hello","distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":{"distributionType":"DeathEvent","distributesBasedOn":"Trustor","distributesBasedOnCondition":null,"timeBasedLengthYears":null,"otherDescription":null,"isTimeBasedEventTrigger":false,"deathTriggerContactId":null,"termTrigger":null},"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":10.0,"name":"Sub","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":10.0,"name":"Sub","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null}],"secondDeathBeneficiariesOutrightPercentageOfResiduary":null,"secondDeathBeneficiariesInTrustPercentageOfResiduary":20.0,"secondDeathBeneficiariesInTrust":[{"distributionPercentage":10.0,"name":"Horosho","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":10.0,"name":"30","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null},{"distributionPercentage":10.0,"name":"302","beneficiariesDuringTrustPeriod":null,"fundingDirectiveType":null,"fundingDirectiveAmount":null,"flowchartFundingAmount":null,"beneficiaryDuringTrustPeriod":null,"otherBeneficiaryUponDeath":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"isThisTrustRevocable":null,"distributionEvent":null,"isGenerationSkippingTransferTaxExempt":null,"inclusionRatio":null,"mandatoryIncomeDistribution":null,"otherIncomeDistributionStandard":null,"powerOfAppointment":null}],"annuityDetails":null,"isSpouseBeneficiaryUponDeath":null,"otherBeneficiaryUponDeath":null,"unitrustRate":null,"note":null,"noteDate":null,"incomeDistributions":null,"principalDistribution":null,"distributionEvents":null,"hasUltimateBeneficiaries":null,"ultimateBeneficiaries":null,"shouldDistributeToBeneficiariesAfterDonorDeath":null,"policies":null}',
    trustType: 'RevocableTrust',
    trustors: null,
    type: 'Trust',
    wid: '57adc476-00ec-41be-a0e8-97a10e6488ff',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    creationDate: '2024-11-15T17:44:40',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: '',
    esterJobId: null,
    firstName: 'O',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'cd673f34-240c-4e08-98bc-111f25112561',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'P',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2024-11-15T17:44:40',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: '57adc476-00ec-41be-a0e8-97a10e6488ff',
  },
] as ContactCard[];
