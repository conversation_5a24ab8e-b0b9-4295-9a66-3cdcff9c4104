import { ContactCard, makeNotNull } from '@wealthcom/visualizer';

export function fixContactsForClientGroup(contactCards: ContactCard[], groupClientIds?: string[]) {
  if (contactCards.length > 0) {
    const ccs: ContactCard[] = JSON.parse(JSON.stringify(makeNotNull(contactCards)));
    const primaryCC = ccs.find((cc) => cc.id === groupClientIds?.[0]);
    if (primaryCC) {
      primaryCC.isPrimary = true;
    }
    return ccs;
  }
  return contactCards;
}
