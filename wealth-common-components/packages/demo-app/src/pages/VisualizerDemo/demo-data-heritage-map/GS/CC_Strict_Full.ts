import { ContactCard } from '@wealthcom/visualizer';
import { fixContactsForClientGroup } from './util';

const GroupClientIds = ['c6966f5b-4b96-4a62-85a7-cd00c0390b01'];

const CC: ContactCard[] = [];

function createCC(name: string, id?: string): ContactCard {
  const cc = {
    id: id || crypto.randomUUID(),
    firstName: name,
    isLegalNameConfirmed: false,
    wid: '',
  };
  CC.push(cc);
  return cc;
}

function addSpouse(primary: ContactCard, spouse: ContactCard, options?: { isEx?: boolean; childrenIds?: string[] }) {
  primary.spouses = [
    ...(primary.spouses || []),
    {
      // childrenIds: options?.childrenIds,
      id: spouse.id,
      isEx: options?.isEx,
    },
  ];
}

function addChild(primary: ContactCard, child: ContactCard) {
  primary.children = [
    ...(primary.children || []),
    {
      id: child.id,
      firstName: child.firstName,
      middleName: child.middleName,
      lastName: child.lastName,
    },
  ];
}

const primaryCC = createCC('Primary', GroupClientIds[0]);

const spouse1 = createCC('Spouse 1');
addSpouse(primaryCC, spouse1, { isEx: false });
const spouse11 = createCC('Spouse 11');
addSpouse(spouse1, spouse11, { isEx: true });

const spouse2 = createCC('Spouse 2');
addSpouse(primaryCC, spouse2, { isEx: true });
const spouse21 = createCC('Spouse 21');
addSpouse(spouse2, spouse21, { isEx: true });

// Children
const child1 = createCC('Child 1');
addChild(primaryCC, child1);
addChild(spouse1, child1);

const child2 = createCC('Child 2');
addChild(primaryCC, child2);
addChild(spouse1, child2);

const child3 = createCC('Child 3');
addChild(spouse1, child3);

const child4 = createCC('Child 4');
addChild(spouse11, child4);
addChild(spouse1, child4);

const child5 = createCC('Child 5');
addChild(spouse11, child5);

const child6 = createCC('Child 6');
addChild(spouse2, child6);
addChild(primaryCC, child6);

const child7 = createCC('Child 7');
addChild(spouse2, child7);

const child8 = createCC('Child 8');
addChild(primaryCC, child8);

// Parents
const parent1 = createCC('Parent 1');
const parent2 = createCC('Parent 2');
const parent3 = createCC('Parent 3');
const parent4 = createCC('Parent 4');
const parent101 = createCC('Parent 101');
const parent201 = createCC('Parent 201');
addChild(parent1, primaryCC);
addChild(parent2, primaryCC);
addChild(parent3, spouse1);
addChild(parent4, spouse1);
addSpouse(parent1, parent101, { isEx: true });
addSpouse(parent4, parent201, { isEx: true });

// Siblings
const sibling1 = createCC('Sibling 1');
const sibling2 = createCC('Sibling 2');
const sibling3 = createCC('Sibling 3');
const sibling4 = createCC('Sibling 4');
const sibling5 = createCC('Sibling 5');
const sibling6 = createCC('Sibling 6');
// sibling1 is same parents as primaryCC
addChild(parent1, sibling1);
addChild(parent2, sibling1);
// sibling2 is from parent2
addChild(parent2, sibling2);
// sibling3 is from parent3
addChild(parent3, sibling3);
// sibling4 is from parent4
addChild(parent4, sibling4);
// sibling5 is same parents as spouse1
addChild(parent3, sibling5);
addChild(parent4, sibling5);

// ex of parent2, sibling of parent2 & ex of parent2
addChild(parent101, sibling6);
addChild(parent2, sibling6);

// Siblings' Spouses
const siblingSpouse21 = createCC('Sibling Spouse 21');
addSpouse(sibling2, siblingSpouse21, { isEx: false });
const siblingSpouse51 = createCC('Sibling Spouse 51');
addSpouse(sibling5, siblingSpouse51, { isEx: false });

// Newphew
const nephew1 = createCC('Nephew 1');
addChild(sibling1, nephew1);
const nephew2 = createCC('Nephew 2');
addChild(sibling4, nephew2);

// ChildSpouse
const childSpouse21 = createCC('Child Spouse 21');
addSpouse(child2, childSpouse21, { isEx: false });
const childSpouse61 = createCC('Child Spouse 61');
addSpouse(child6, childSpouse61, { isEx: false });

// Grandchildren
const grandChild1 = createCC('GrandChild 1');
addChild(child1, grandChild1);
const grandChild2 = createCC('GrandChild 2');
addChild(child1, grandChild2);
const grandChild3 = createCC('GrandChild 3');
addChild(child5, grandChild3);
const grandChild4 = createCC('GrandChild 4');
addChild(childSpouse61, grandChild4);
addChild(child6, grandChild4);

export const CC_Strict_Full = fixContactsForClientGroup(CC, GroupClientIds);
