import { ContactCard } from '@wealthcom/visualizer';

const CC = [
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: 'California',
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2024-11-18T17:12:57',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: '<EMAIL>',
    esterJobId: null,
    firstName: 'Her',
    formOfIncorporation: null,
    geodata: '{}',
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '3c60be77-ae0d-49f0-b594-f80c1b6f08fb',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: true,
    lastModifiedByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    lastName: 'Map',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-11-18T17:31:41',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: [
      {
        childrenIds: null,
        id: '8dc37791-90ec-405f-aee5-1e19133ab3c5',
        isEx: true,
        __typename: 'Spouse',
      },
    ],
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: 'ad4b52a1-a80f-4ac7-b73b-0d05e702e565',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    creationDate: '2024-11-18T17:31:41',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: null,
    esterJobId: null,
    firstName: 'Ex',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: null,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '8dc37791-90ec-405f-aee5-1e19133ab3c5',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'Spouse',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-11-18T17:31:41',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: 'ExPartner',
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: 'ad4b52a1-a80f-4ac7-b73b-0d05e702e565',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: [
      {
        firstName: 'Her',
        id: '3c60be77-ae0d-49f0-b594-f80c1b6f08fb',
        lastName: 'Map',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
      {
        firstName: 'Sis',
        id: 'b608c020-f378-44fb-afa0-99c30f879ba9',
        lastName: 'Main',
        middleName: '',
        __typename: 'SecondaryContactCard',
      },
    ],
    childrenIds: null,
    citizenship: null,
    createdByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    creationDate: '2024-11-18T17:32:00',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: null,
    esterJobId: null,
    firstName: 'MOM',
    formOfIncorporation: null,
    geodata: '{}',
    hasChildren: true,
    hasFamilyMemberWithSpecialNeeds: null,
    hasPets: null,
    id: '7061d163-48dc-4f8e-bea1-2533ee9d76ee',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    lastName: 'Main',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-11-18T17:37:40',
    nickname: null,
    notes: null,
    otherRevocableTrustType: null,
    owners: null,
    partner: {
      firstName: 'PAPA',
      id: '5f21ce58-ec28-483e-8e88-b8babf8feaa4',
      lastName: 'Main',
      middleName: '',
      __typename: 'SecondaryContactCard',
    },
    partnerId: null,
    phoneNumber: null,
    relationship: 'Parent',
    revocableTrustType: null,
    spouses: [
      {
        childrenIds: null,
        id: '5f21ce58-ec28-483e-8e88-b8babf8feaa4',
        isEx: false,
        __typename: 'Spouse',
      },
    ],
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: 'ad4b52a1-a80f-4ac7-b73b-0d05e702e565',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    creationDate: '2024-11-18T17:32:20',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: '',
    esterJobId: null,
    firstName: 'Sis',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'b608c020-f378-44fb-afa0-99c30f879ba9',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    lastName: 'Main',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2024-11-18T17:37:16',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: {
      firstName: 'Spouse',
      id: 'a3b8048b-16da-416c-9d1d-2a658dc1702b',
      lastName: 'OfSis',
      middleName: '',
      __typename: 'SecondaryContactCard',
    },
    partnerId: null,
    phoneNumber: '',
    relationship: 'Sibling',
    revocableTrustType: null,
    spouses: [
      {
        childrenIds: null,
        id: 'a3b8048b-16da-416c-9d1d-2a658dc1702b',
        isEx: false,
        __typename: 'Spouse',
      },
    ],
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: 'ad4b52a1-a80f-4ac7-b73b-0d05e702e565',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: [
      {
        firstName: 'Sis',
        id: 'b608c020-f378-44fb-afa0-99c30f879ba9',
        lastName: 'Main',
        middleName: '',
        __typename: 'SecondaryContactCard',
      },
      {
        firstName: 'Her',
        id: '3c60be77-ae0d-49f0-b594-f80c1b6f08fb',
        lastName: 'Map',
        middleName: null,
        __typename: 'SecondaryContactCard',
      },
    ],
    childrenIds: null,
    citizenship: null,
    createdByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    creationDate: '2024-11-18T17:35:12',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: '',
    esterJobId: null,
    firstName: 'PAPA',
    formOfIncorporation: null,
    geodata: '{}',
    hasChildren: true,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '5f21ce58-ec28-483e-8e88-b8babf8feaa4',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    lastName: 'Main',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2024-11-18T17:37:41',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: 'Parent',
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: 'ad4b52a1-a80f-4ac7-b73b-0d05e702e565',
  },
  {
    addressCity: '',
    addressCountry: null,
    addressCountryShort: '',
    addressCounty: null,
    addressFormatted: '',
    addressLine1: '',
    addressLine2: '',
    addressState: '',
    addressStateShort: '',
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: '',
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    creationDate: '2024-11-18T17:35:59',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: '',
    esterJobId: null,
    firstName: 'Spouse',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'a3b8048b-16da-416c-9d1d-2a658dc1702b',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: '60f54ce9-00b5-4289-8abd-a0342b35acac',
    lastName: 'OfSis',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: '',
    modificationDate: '2024-11-18T17:37:17',
    nickname: '',
    notes: '',
    otherRevocableTrustType: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: '',
    relationship: 'SiblingInLaw',
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: 'ad4b52a1-a80f-4ac7-b73b-0d05e702e565',
  },
] as ContactCard[];

export default CC;
