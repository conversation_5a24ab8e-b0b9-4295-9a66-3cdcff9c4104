import { ContactCard } from '@wealthcom/visualizer';

const CC = [
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: null,
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: null,
    creationDate: '2024-06-13T08:20:30',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: '<EMAIL>',
    firstName: '8742',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: '5e48abfd-4ed9-4b84-ae2b-6f83a1533068',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: true,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: 'Married',
    middleName: null,
    modificationDate: '2024-06-13T08:20:30',
    nickname: null,
    notes: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: null,
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: 'bcc92b5b-4280-46a1-bd00-623592b9cad4',
  },
  {
    addressCity: null,
    addressCountry: null,
    addressCountryShort: null,
    addressCounty: null,
    addressFormatted: null,
    addressLine1: null,
    addressLine2: null,
    addressState: null,
    addressStateShort: null,
    addressStreetName: null,
    addressStreetNumber: null,
    addressZipCode: null,
    aliases: [],
    children: null,
    childrenIds: null,
    citizenship: null,
    createdByWid: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
    creationDate: '2024-06-13T09:19:47',
    directBeneficiaries: null,
    dob: null,
    ein: null,
    email: null,
    firstName: 'S',
    formOfIncorporation: null,
    geodata: null,
    hasChildren: false,
    hasFamilyMemberWithSpecialNeeds: false,
    hasPets: false,
    id: 'dc5171d2-469e-46eb-a61a-7c8b826e0583',
    isDeceased: false,
    isDonorAdvisedFund: null,
    isGrantorTrust: false,
    isHideFromHeritageMap: false,
    isLegalNameConfirmed: false,
    isOutsideOfTaxableEstate: false,
    isPreviousChild: false,
    isPrimary: false,
    lastModifiedByWid: null,
    lastName: 'L',
    legalName: null,
    manualValue: null,
    manualValueAsOfDate: null,
    maritalStatus: null,
    middleName: null,
    modificationDate: '2024-06-13T09:19:47',
    nickname: null,
    notes: null,
    owners: null,
    partner: null,
    partnerId: null,
    phoneNumber: null,
    relationship: 'Sibling',
    revocableTrustType: null,
    spouses: null,
    tin: null,
    trustCreationDate: null,
    trustInformation: null,
    trustType: null,
    trustors: null,
    type: 'Individual',
    wid: 'bcc92b5b-4280-46a1-bd00-623592b9cad4',
  },
] as ContactCard[];

export default CC;
