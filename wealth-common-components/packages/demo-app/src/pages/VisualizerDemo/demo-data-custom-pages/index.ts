export const CustomPages = [
  {
    id: '010e11f5-dfea-4134-9beb-f0fac887fc01',
    metadata: {
      createdAt: '2025-05-23T03:44:24.561Z',
      createdBy: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
      updatedAt: '2025-05-23T03:44:24.561Z',
    },
    sections: [
      {
        color: '#B90E1C',
        description: '1231sdalfjaoisdfjoasidfja',
        sectionTitle: 'Placeholder',
      },
    ],
    templateType: 'GOALS_AND_OPPORTUNITIES',
    title: 'Goals and Objectives',
  },
  {
    id: 'af1b3993-f5c2-4a49-b546-530b01fc92bb',
    metadata: {
      createdAt: '2025-05-23T03:44:24.561Z',
      createdBy: '2a1eb59a-1a38-4d24-bfa8-f38dc19995b1',
      updatedAt: '2025-05-23T03:44:24.561Z',
    },
    sections: [
      {
        color: '#000000',
        description: 'C\ns\nc\nz\n',
        sectionTitle: 'jafoijasdoif jasoidfj oaisdj foasdjf ',
      },
      {
        color: '#000000',
        description:
          'jafoijasdoif jasoidfj oaisdj foasdjfjafoijasdoifjasoidfjoaisdjfoasdjf foasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjf foasdjfjafoijasdoifjasoidfjoaisdjfoasdjf foasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjf ',
        sectionTitle:
          'jafoijasdoif jasoidfj oaisdj foasdjfjafoijasdoifjasoidfjoaisdjfoasdjf foasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjf foasdjfjafoijasdoifjasoidfjoaisdjfoasdjf foasdjfjafoijasdoifjasoidfjoaisdjfoasdjffoasdjfjafoijasdoifjasoidfjoaisdjfoasdjf ',
      },
      {
        color: '#000000',
        description: `
# <center>sdfsdfa</center>
## asdfaksdfopaksdofkaopsdfkpaoskdfpoaksdfpoka sdfkpaoskdfpoaksdfpoka sdfkpaoskdfpoaksdfpoka sdfkpaoskdfpoaksdfpoka sdfkpaoskdfpoaksdfpoka sdfkpaoskdfpoaksdfpoka sdfkpaoskdfpoaksdfpoka
      `,
        sectionTitle: 'jafoijasdoif jasoidfj oaisdj foasdjf ',
      },
    ],
    templateType: 'GOALS_AND_OPPORTUNITIES',
    title: 'Goals and Objectives',
  },
];
