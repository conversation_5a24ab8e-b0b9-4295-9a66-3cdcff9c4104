import { StoryFn, Meta } from '@storybook/react-vite';
import Projections from '@wealthcom/projections';
import { getNetWorthPredictionDataLiability, assets, pcc, projectionClients } from '../../app/data';
import { useState } from 'react';
import { getDefaultTheme } from 'storybook-host';
export default {
  component: Projections,
  title: 'Projections',
} as Meta<typeof Projections>;

const getNetWorthPrediction = () => {
  return getNetWorthPredictionDataLiability;
};

const Template: StoryFn<typeof Projections> = (args) => {
  const [clientId, setClientId] = useState<string | null | undefined>(null);
  const advisorProps = {
    clients: projectionClients,
    clientId: clientId,
    setClientId: setClientId,
  };
  const defaultTheme = getDefaultTheme();
  return <Projections {...args} advisorProps={advisorProps} theme={defaultTheme} />;
};

export const Primary = Template.bind({});
Primary.args = {
  type: 'client',
  callbacks: { getNetWorthPrediction },
  netWorth: 839958,
  assets,
  selectedContactCard: pcc,
  isJoint: false,
  assetsLoading: false,
  advisorProps: undefined,
};
