import Projections from '@wealthcom/projections';
import { createTheme } from '@mui/material/styles';
import { getNetWorthPredictionDataLiability, assets, pcc, projectionClients } from '../../app/data';

import clayTheme from '../../app/theme';
import { useState } from 'react';
const theme = createTheme(clayTheme);

export function ProjectionsDemo() {
  const [clientId, setClientId] = useState<string | null | undefined>(null);

  const advisorProps = {
    clients: projectionClients,
    clientId: clientId,
    setClientId: setClientId,
  };

  return (
    <Projections
      type="client"
      theme={theme}
      callbacks={{
        getNetWorthPrediction: () => {
          return getNetWorthPredictionDataLiability;
        },
      }}
      netWorth={839958}
      assets={assets}
      selectedContactCard={pcc as never}
      isJoint={false}
      assetsLoading={false}
      advisorProps={advisorProps as never}
    />
  );
}

export default ProjectionsDemo;
