import { Box, BoxProps, Button, Dialog, Stack, Typography, styled, useTheme } from '@mui/material';
import {
  AssetTypes,
  ICON_VAULT_ESTATE_PLANNING_CHARITABLE_GIVING_72,
  Icon,
  IconAsset,
  IconAssetProps,
  IconAssetSize,
  IconVault,
  IconVaultProps,
  IconVaultSize,
  VaultCategories,
} from '@wealthcom/icons';
import { useRef, useState } from 'react';

const AssetIconSizes: IconAssetSize[] = [32, 48, 56, 88, 120];
const VaultIconSizes: IconVaultSize[] = [24, 48, 72];

const IconsPreviewBox = styled(Box)(({ theme }) => ({
  '.IconVaultGroup,.IconAssetGroup': {
    display: 'flex',
    flexWrap: 'wrap',
    gap: 5,
    '.IconGroup': {
      display: 'flex',
      color: '#333',
      cursor: 'pointer',
    },
  },
}));

export default function IconsPreview() {
  const theme = useTheme();
  const [selectedIcon, setSelectedIcon] = useState<{
    catalog: 'asset' | 'vault';
    iconProps: IconAssetProps | IconVaultProps;
    color: string;
  } | null>(null);

  const onSelectIcon = (props: Omit<IconAssetProps, 'iconAssetSize'> | Omit<IconVaultProps, 'iconVaultSize'>) => {
    if ('iconAssetType' in props && props.iconAssetType) {
      setSelectedIcon({
        catalog: 'asset',
        iconProps: {
          ...props,
          iconAssetSize: AssetIconSizes[AssetIconSizes.length - 1],
        },
        color: theme.palette.primary.main,
      });
      return;
    }

    if ('iconVaultCategory' in props) {
      setSelectedIcon({
        catalog: 'asset',
        iconProps: {
          ...props,
          iconVaultSize: VaultIconSizes[VaultIconSizes.length - 1],
        },
        color: theme.palette.primary.main,
      });
      return;
    }

    setSelectedIcon(null);
  };

  return (
    <Box>
      <Stack sx={{ pb: 5 }}>
        <Typography variant="h4">Render Icon by Full Icon name</Typography>
        <ICON_VAULT_ESTATE_PLANNING_CHARITABLE_GIVING_72 />
        <Box width={480}>
          <CodeBlock>{'<ICON_VAULT_ESTATE_PLANNING_CHARITABLE_GIVING_72 />'}</CodeBlock>
        </Box>
      </Stack>
      <Stack>
        <Typography variant="h4">Render Icon by passing props</Typography>
        <IconsPreviewBox>
          <Box className="IconAssetGroup">
            {Object.keys(AssetTypes).map((assetType, i) => {
              return (
                <Box className="IconGroup" key={i} onClick={() => onSelectIcon({ iconAssetType: assetType as never })}>
                  {AssetIconSizes.map((s, j) => (
                    <IconAsset key={j} iconAssetType={assetType as never} iconAssetSize={s} />
                  ))}
                </Box>
              );
            })}
          </Box>
          {Object.entries(VaultCategories).map(([catKey, subCats], k) => {
            return (
              <Box className="IconVaultGroup" key={k}>
                {Object.keys(subCats).map((subCat, j) => {
                  return (
                    <Box
                      className="IconGroup"
                      key={j}
                      onClick={() =>
                        onSelectIcon({
                          iconVaultCategory: catKey as never,
                          iconVaultSubCategory: subCat as never,
                        })
                      }
                    >
                      {VaultIconSizes.map((s, i) => (
                        <IconVault key={i} iconVaultCategory={catKey as never} iconVaultSubCategory={subCat as never} iconVaultSize={s} />
                      ))}
                    </Box>
                  );
                })}
              </Box>
            );
          })}
        </IconsPreviewBox>
      </Stack>
      {selectedIcon?.iconProps ? (
        <Dialog open onClose={() => setSelectedIcon(null)}>
          <Box sx={{ width: 540, height: 360, p: 3, boxSizing: 'border-box' }}>
            <Box sx={{ height: 120, pb: 2 }}>
              <Icon iconKey="" {...selectedIcon?.iconProps} color={selectedIcon?.color} />
            </Box>
            <CodeBlock>{`<Icon ${Object.entries(selectedIcon?.iconProps)
              .map((kv) => `\n  ${kv[0]}="${kv[1]}"`)
              .join(' ')}\n  color="${selectedIcon?.color}"\n/>`}</CodeBlock>
            <Box
              sx={{
                display: 'flex',
                gap: 0.1,
                p: 0.1,
                backgroundColor: '#000',
                '&>.icon-size': {
                  width: '100%',
                  textAlign: 'center',
                  backgroundColor: '#ccc',
                  lineHeight: '3',
                  cursor: 'pointer',
                  '&.icon-size-selected': {
                    backgroundColor: '#fff',
                  },
                },
              }}
            >
              {'iconAssetSize' in selectedIcon.iconProps
                ? AssetIconSizes.map((s) => (
                    <Box
                      onClick={() =>
                        setSelectedIcon(
                          (si) =>
                            ({
                              ...si,
                              iconProps: { ...si?.iconProps, iconAssetSize: s },
                            }) as never,
                        )
                      }
                      key={s}
                      className={(selectedIcon.iconProps as IconAssetProps).iconAssetSize === s ? 'icon-size icon-size-selected' : 'icon-size'}
                    >
                      {s}
                    </Box>
                  ))
                : null}
              {'iconVaultSize' in selectedIcon.iconProps
                ? VaultIconSizes.map((s) => (
                    <Box
                      onClick={() =>
                        setSelectedIcon(
                          (si) =>
                            ({
                              ...si,
                              iconProps: { ...si?.iconProps, iconVaultSize: s },
                            }) as never,
                        )
                      }
                      key={s}
                      className={(selectedIcon.iconProps as IconVaultProps).iconVaultSize === s ? 'icon-size icon-size-selected' : 'icon-size'}
                    >
                      {s}
                    </Box>
                  ))
                : null}
            </Box>
          </Box>
        </Dialog>
      ) : null}
    </Box>
  );
}

function CodeBlock({ children, sx: inputSx, ...props }: BoxProps) {
  const codeRef = useRef<HTMLPreElement>();
  const [copied, setSopied] = useState(false);
  return (
    <Box
      sx={[
        {
          position: 'relative',
          backgroundColor: '#2e2a24',
          p: 1,
          boxSizing: 'border-box',
          '&>button': { position: 'absolute', top: 0, right: 0 },
          '&>code': {
            whiteSpace: 'pre',
            color: '#fff',
          },
        },
        ...(Array.isArray(inputSx) ? inputSx : [inputSx]),
      ]}
      {...props}
    >
      <Box ref={codeRef} component="code">
        {children}
      </Box>
      <Button
        onClick={() => {
          navigator.clipboard.writeText(codeRef.current?.innerText || '');
          setSopied(true);
        }}
      >
        <span role="img" aria-label="Copy to clipboard">
          {copied ? '✅' : '📋'}
        </span>
      </Button>
    </Box>
  );
}
