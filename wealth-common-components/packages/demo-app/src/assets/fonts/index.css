@font-face {
  font-family: 'Reckless';
  font-weight: 400;
  font-style: normal;
  src: url('Reckless-Regular.woff') format('woff');
}
@font-face {
  font-family: 'Neue Haas Unica Pro';
  font-weight: 400;
  font-style: normal;
  src: url('NeueHaasUnicaPro.otf') format('opentype');
}
@font-face {
  font-family: 'Neue Haas Unica Pro';
  font-weight: 500;
  font-style: normal;
  src: url('NeueHaasUnicaPro-Medium.otf') format('opentype');
}

@font-face {
  font-family: 'Neue Haas Unica Pro Medium';
  font-weight: 500;
  font-style: normal;
  src: url('NeueHaasUnicaPro-Medium.otf') format('opentype');
}

@font-face {
  font-family: 'Charles Modern';
  font-weight: 400;
  font-style: normal;
  src: url('Schwab/charlesmodern.woff2') format('woff2');
}

@font-face {
  font-family: 'Charles Modern Medium';
  font-weight: 500;
  font-style: normal;
  src: url('Schwab/charlesmodern-medium.woff2') format('woff2');
}

@font-face {
  font-family: 'Basis Grotesque Pro';
  font-weight: 400;
  font-style: normal;
  src: url('Goldman-Sachs/BasisGrotesque-Regular-Pro.woff2') format('woff2');
}

@font-face {
  font-family: 'Basis Grotesque Pro Light';
  font-weight: 400;
  font-style: normal;
  src: url('Goldman-Sachs/BasisGrotesque-Light-Pro.woff2') format('woff2');
}

@font-face {
  font-family: 'Basis Grotesque Pro Medium';
  font-weight: 500;
  font-style: normal;
  src: url('Goldman-Sachs/BasisGrotesque-Medium-Pro.woff2') format('woff2');
}

@font-face {
  font-family: 'Interstate';
  font-weight: 400;
  font-style: normal;
  src: url('Citi/Interstate-Regular.woff') format('woff');
}

@font-face {
  font-family: 'Interstate Light';
  font-weight: 400;
  font-style: normal;
  src: url('Citi/Interstate-Light.woff') format('woff');
}

@font-face {
  font-family: 'Interstate Bold';
  font-weight: 400;
  font-style: normal;
  src: url('Citi/Interstate-Bold.woff') format('woff');
}

@font-face {
  font-family: 'Sainte Colombe';
  font-weight: 400;
  font-style: normal;
  src: url('Citi/saintecolombe-regular.otf') format('opentype');
}

@font-face {
  font-family: 'Work Sans';
  font-weight: 400;
  font-style: normal;
  src: url('Prime-Capital/WorkSans-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Work Sans Medium';
  font-weight: 500;
  font-style: normal;
  src: url('Prime-Capital/WorkSans-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'Neue Haas Unica W1G';
  src: url('Neue-Haas-Unica-W1G/Neue-Haas-Unica-W1G-Regular.ttf') format('woff2');
  font-weight: 400;
}

@font-face {
  font-family: 'Neue Haas Unica W1G';
  src: url('Neue-Haas-Unica-W1G/Neue-Haas-Unica-W1G-Medium.ttf') format('woff2');
  font-weight: 500;
}

@font-face {
  font-family: 'Neue Haas Unica W1G Medium';
  src: url('Neue-Haas-Unica-W1G/Neue-Haas-Unica-W1G-Medium.ttf') format('woff2');
  font-weight: 500;
}
