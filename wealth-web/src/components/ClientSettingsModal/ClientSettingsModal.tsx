import BasicInfo from './BasicInfo';
import FederalEstateTax from './FederalEstateTax';
import StateEstateTax from './StateEstateTax';
import EstateTaxCalculations from './EstateTaxCalculations';
import Box from '@mui/material/Box';
import Dialog from '@mui/material/Dialog';
import IconButton from '@mui/material/IconButton';
import WealthButton from 'components/@wealth/atoms/Button/Button';
import { ClientSettingsTab } from './types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Typography } from '@wealthcom/ui/atoms';
import {
  clientSettingsModalAtom,
  clientSettingsDataAtom,
} from 'atomStore/clientSettings';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { wealthPalette } from 'theme/wealth-default-theme';
import {
  faBank,
  faBuildings,
  faCalculator,
  faFloppyDisk,
  faSquareUser,
  faXmark,
} from '@fortawesome/pro-regular-svg-icons';
import { logger } from 'utils/logger';
import { clientUserAtom } from 'atomStore/client/client';
import { clientAssetsAtom } from 'atomStore/client/assets';
import useUserContacts from 'hooks/useUserContacts';
import useUserAssetSummary from 'hooks/useUserAssetSummary';
import useEstateFlowAD from 'hooks/useEstateFlowAD';
import { estateFlow } from '@wealthcom/visualizer';
import { ContactCard, Asset } from '@wealthcom/visualizer';
import { MetadataForEstateFlow } from 'generated/visualizer-advisor-api/src/generated/API';
import { fixEstateFlowMetadata } from 'pages/visualizer/common';

export const ESTATE_TAX_STATES = new Set([
  'CT',
  'DC',
  'HI',
  'IL',
  'MA',
  'MD',
  'ME',
  'MN',
  'NY',
  'OR',
  'RI',
  'VT',
  'WA',
]);

const TAB_LIST = [
  {
    label: 'clientSettings.basicInfo.title',
    value: ClientSettingsTab.BasicInfo,
    icon: faSquareUser,
  },
  {
    label: 'clientSettings.federalEstateTax.title',
    value: ClientSettingsTab.FederalEstateTax,
    icon: faBank,
  },
  {
    label: 'clientSettings.stateEstateTax.title',
    value: ClientSettingsTab.StateEstateTax,
    icon: faBuildings,
  },
  {
    label: 'clientSettings.estateTaxCalculations.title',
    value: ClientSettingsTab.EstateTaxCalculations,
    icon: faCalculator,
  },
] as const;

// Custom hook to calculate gross estate value using EstateFlow Tier1
const useGrossEstateCalculation = (
  contacts: ContactCard[],
  assets: Asset[],
  metadata: MetadataForEstateFlow,
  clientIds: string[]
) => {
  return useMemo(() => {
    try {
      const estateFlowResult = estateFlow(
        contacts,
        assets,
        metadata,
        clientIds,
        undefined
      );

      if (!estateFlowResult?.tier1) {
        return {
          memberGrossEstate: 0,
          spouseGrossEstate: 0,
          totalGrossEstate: 0,
        };
      }

      const tier1 = estateFlowResult.tier1;
      const member = tier1.member;
      const spouse = tier1.spouse;

      const memberGrossEstate =
        member.total - (member.irrevocableTrustOutsideEstate || 0);

      const spouseGrossEstate = spouse
        ? spouse.total - (spouse.irrevocableTrustOutsideEstate || 0)
        : 0;

      const totalGrossEstate = memberGrossEstate + spouseGrossEstate;

      return { memberGrossEstate, spouseGrossEstate, totalGrossEstate };
    } catch (error) {
      return {
        memberGrossEstate: 0,
        spouseGrossEstate: 0,
        totalGrossEstate: 0,
      };
    }
  }, [contacts, assets, clientIds, metadata]);
};

export default function ClientSettingsModal() {
  const [modalState, setModalState] = useAtom(clientSettingsModalAtom);
  const clientSettings = useAtomValue(clientSettingsDataAtom);
  const [isSaving, setIsSaving] = useState(false);
  const { t } = useTranslation();
  const clientMember = useAtomValue(clientUserAtom);
  logger.info('clientSettings', clientSettings);

  // Get data sources for gross estate calculation
  const { contactCards: clientContacts } = useUserContacts(
    clientMember?.id || undefined
  );

  const { assets: clientAssets } = useUserAssetSummary(
    clientMember?.id || undefined
  );

  const { clientEstateFlowMetadata } = useEstateFlowAD(clientMember?.id);

  const metadata = useMemo(
    () => fixEstateFlowMetadata(clientEstateFlowMetadata),
    [clientEstateFlowMetadata]
  );

  const assets = useAtomValue(clientAssetsAtom);

  // Calculate gross estate data as soon as modal opens
  const { memberGrossEstate, spouseGrossEstate, totalGrossEstate } =
    useGrossEstateCalculation(
      clientContacts || [],
      clientAssets || assets || [],
      metadata,
      []
    );

  // Store gross estate values in clientSettings atom for child components to access
  const setClientSettingsData = useSetAtom(clientSettingsDataAtom);

  // Default values for federal estate settings
  const getDefaultFederalEstateSettings = useCallback(
    () => ({
      federalTaxRate: 0,
      estimatedAdminCosts: 0,
      federalGiftAndEstateTaxExemption: 0,
      electPortabilityGiftEstateExemption: false,
      federalGiftAndEstateTaxExemptionUsed: { member: 0, spouse: 0 },
      addAvailableExemptionFromPredeceasedSpouse: {
        member: false,
        spouse: false,
      },
      availableDeceasedSpouseUnusedExclusion: { member: 0, spouse: 0 },
      federalGstTaxExemption: 0,
      gstExemptionUsedAtFederal: { member: 0, spouse: 0 },
      estimatedAdminCostsDollarValue: { member: 0, spouse: 0 },
    }),
    []
  );

  React.useEffect(() => {
    if (
      memberGrossEstate > 0 ||
      spouseGrossEstate > 0 ||
      totalGrossEstate > 0
    ) {
      setClientSettingsData((prev) => {
        if (!prev) return prev;

        const defaultSettings = getDefaultFederalEstateSettings();
        const existingSettings = prev.federalEstateSettings || {};

        return {
          ...prev,
          federalEstateSettings: {
            ...defaultSettings,
            ...existingSettings,
            memberGrossEstate,
            spouseGrossEstate,
            totalGrossEstate,
          },
        };
      });
    }
  }, [
    memberGrossEstate,
    spouseGrossEstate,
    totalGrossEstate,
    getDefaultFederalEstateSettings,
  ]);

  // Filter tabs based on user's state of residence - memoized for performance
  const filteredTabList = React.useMemo(() => {
    return TAB_LIST.filter((tab) => {
      if (tab.value === ClientSettingsTab.StateEstateTax) {
        const stateOfResidence = clientSettings?.primary?.stateOfResidence;
        return stateOfResidence && ESTATE_TAX_STATES.has(stateOfResidence);
      }
      return true;
    });
  }, [clientSettings?.primary?.stateOfResidence]);

  // Registry of form submission functions for each tab
  const [formSubmitters, setFormSubmitters] = useState<
    Partial<Record<ClientSettingsTab, (() => Promise<boolean>) | null>>
  >({});

  const handleClose = () => setModalState({ ...modalState, open: false });

  const handleTabChange = (_: any, newValue: ClientSettingsTab) =>
    setModalState({ ...modalState, activeTab: newValue });

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const activeTab = modalState.activeTab;
      const submitFunction = formSubmitters[activeTab as ClientSettingsTab];

      if (submitFunction) {
        const success = await submitFunction();
        if (success) {
          handleClose();
        }
      } else {
        // No form submitter registered for this tab, just close
        handleClose();
      }
    } catch (error) {
      logger.error('Save failed:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const registerFormSubmitter = useCallback(
    (tab: ClientSettingsTab, submitFn: () => Promise<boolean>) => {
      setFormSubmitters((prev) => ({ ...prev, [tab]: submitFn }));
    },
    []
  );

  return (
    <Dialog
      fullScreen
      open={modalState.open}
      onClose={handleClose}
      sx={{ zIndex: 1100 }}
      PaperProps={{ sx: { borderRadius: '0px !important' } }}
    >
      <Box
        sx={{
          display: 'flex',
          flex: 1,
          flexDirection: 'column',
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 3,
            borderBottom: `1px solid ${wealthPalette.stroke.strong}`,
            background: wealthPalette.surface.white,
            minHeight: 80,
          }}
        >
          <Box>
            <Typography variant="h6" sx={{ fontSize: 20 }}>
              {t('clientSettings.title')}
            </Typography>
            <Typography
              variant="body3"
              sx={{ color: wealthPalette.text.subtle }}
            >
              {t('clientSettings.description')}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <WealthButton
              onClick={handleSave}
              variant="contained"
              startIcon={
                <FontAwesomeIcon icon={faFloppyDisk} style={{ fontSize: 18 }} />
              }
              loading={isSaving}
              disabled={isSaving}
            >
              {t('clientSettings.saveChanges')}
            </WealthButton>
            <IconButton
              onClick={handleClose}
              sx={{
                ml: 1,
                border: `1px solid ${wealthPalette.stroke.strong}`,
                borderRadius: '8px',
                height: 32,
                width: 32,
                padding: '8px',
              }}
            >
              <FontAwesomeIcon
                icon={faXmark}
                color={wealthPalette.text.strong}
                style={{ fontSize: 14 }}
              />
            </IconButton>
          </Box>
        </Box>

        {/* Main Content */}
        <Box sx={{ display: 'flex', flex: 1, minHeight: 0 }}>
          {/* Sidebar Tabs */}
          <Box
            sx={{
              background: wealthPalette.surface.soft,
              py: 4,
              px: 2,
              display: 'flex',
              flexDirection: 'column',
              gap: 1,
              minWidth: 376,
              maxWidth: 420,
            }}
          >
            {filteredTabList.map((tab) => (
              <WealthButton
                key={tab.value}
                onClick={() => handleTabChange(null, tab.value)}
                startIcon={
                  <FontAwesomeIcon icon={tab.icon} style={{ fontSize: 18 }} />
                }
                variant={
                  modalState.activeTab === tab.value ? 'contained' : 'text'
                }
                sx={{
                  justifyContent: 'flex-start',
                  px: 2,
                  py: 1,
                  minHeight: 40,
                  color:
                    modalState.activeTab === tab.value
                      ? wealthPalette.primary.base
                      : wealthPalette.text.strong,
                  background:
                    modalState.activeTab === tab.value
                      ? wealthPalette.green[50]
                      : 'transparent',
                  border:
                    modalState.activeTab === tab.value
                      ? `1px solid ${wealthPalette.primary.subtle}`
                      : 'none',
                }}
              >
                {t(tab.label)}
              </WealthButton>
            ))}
          </Box>

          {/* Content Area */}
          <Box
            sx={{
              flexGrow: 4,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'flex-start',
              background: wealthPalette.surface.soft,
              py: 4,
              px: 12,
              minHeight: 0,
            }}
          >
            {modalState.activeTab === ClientSettingsTab.BasicInfo && (
              <BasicInfo registerSubmitter={registerFormSubmitter} />
            )}
            {modalState.activeTab === ClientSettingsTab.FederalEstateTax && (
              <FederalEstateTax registerSubmitter={registerFormSubmitter} />
            )}
            {modalState.activeTab === ClientSettingsTab.StateEstateTax && (
              <StateEstateTax registerSubmitter={registerFormSubmitter} />
            )}
            {modalState.activeTab ===
              ClientSettingsTab.EstateTaxCalculations && (
              <EstateTaxCalculations
                registerSubmitter={registerFormSubmitter}
              />
            )}
          </Box>
        </Box>
      </Box>
    </Dialog>
  );
}
