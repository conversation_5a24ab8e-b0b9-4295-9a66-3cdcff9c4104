import React, { useEffect, useMemo, useRef } from 'react';
import { useAtom, useAtomValue } from 'jotai';
import { useTranslation } from 'react-i18next';
import { useFormik, FormikProvider } from 'formik';
import * as Yup from 'yup';
import { clientSettingsDataAtom } from 'atomStore/clientSettings';
import { usStateNameValue } from 'components/@wealth/organisms/ScenarioAnalysis/BaselineWizard/BasicInformation/BasicInformationForm';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/GridLegacy';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Typography from 'components/@wealth/atoms/Typography/Typography';
import { wealthPalette } from 'theme/wealth-default-theme';
import { clientUserAtom } from 'atomStore/client/client';
import {
  formatContactName,
  getMaritalStatusL10nKey,
} from 'utils/formatContact';
import { get } from 'utils/objectHelpers';
import { MaritalStatus } from '@wealthcom/visualizer';
import EditItem from 'pages/vault/Contacts/_common_v2/EditItem';
import ErrorMessage from 'components/_common/ErrorMessage';
import { ClientSettingsTab } from './types';
import Skeleton from '@mui/material/Skeleton';
import { logger } from 'utils/logger';
import useGetClientSettings from 'hooks/client-settings/useGetClientSettings';
import useUserContacts from 'hooks/useUserContacts';
import useContacts from 'hooks/useContacts';
import { convertToInput } from 'components/_estate/EstateDocument/form/widgets/common/util';
import { ContactCardType, ContactCard } from 'generated/API';
import SearchContactComponent from 'pages/vault/Contacts/BasicInfoModal/SearchContactComponent';
import useEstateFlowAD from 'hooks/useEstateFlowAD';
import { syncClientSettingsToEstateFlow } from 'hooks/client-settings/syncToEstateFlow';
import { US_STATES } from 'models/common/us-states';

// Reusable form field component with error handling
const FormField = ({
  formik,
  name,
  label,
  options,
  type = 'select',
  placeholder,
  onChange,
  showSelectOption = true,
  dataTestId,
  hideLabel = false,
}: {
  formik: any;
  name: string;
  label: string;
  options?: { value: string; name: string }[];
  type?: 'select' | 'text' | 'date';
  placeholder?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  showSelectOption?: boolean;
  dataTestId?: string;
  hideLabel?: boolean;
}) => {
  const { t } = useTranslation();
  const error = get(formik.errors, name);
  const touched = get(formik.touched, name);
  const showError = error && touched;

  if (type === 'date') {
    const editItemProps = {
      formik,
      type: 'date' as const,
      name,
      label: hideLabel ? '' : label,
      dataTestId,
      hideLabel,
    };

    return <EditItem {...editItemProps} />;
  }

  if (type === 'text') {
    return (
      <>
        <TextField
          fullWidth
          label={label}
          name={name}
          placeholder={placeholder}
          value={get(formik.values, name) || ''}
          onChange={onChange || formik.handleChange}
          error={!!showError}
          InputLabelProps={{ shrink: true }}
          data-testid={dataTestId}
        />
      </>
    );
  }

  return (
    <>
      <TextField
        select
        fullWidth
        label={label}
        name={name}
        value={get(formik.values, name) || ''}
        onChange={(e) => {
          formik.handleChange(e);
        }}
        error={!!showError}
        InputLabelProps={{ shrink: true }}
        data-testid={dataTestId}
      >
        {showSelectOption && (
          <MenuItem value="">{t('clientSettings.basicInfo.select')}</MenuItem>
        )}
        {options?.map(({ value, name: optionName }) => (
          <MenuItem key={value} value={value}>
            {optionName}
          </MenuItem>
        ))}
      </TextField>
    </>
  );
};

// Reusable field wrapper with label and error handling
const FieldWrapper = ({
  formik,
  name,
  label,
  children,
  required = false,
}: {
  formik: any;
  name: string;
  label: string;
  children: React.ReactNode;
  required?: boolean;
}) => {
  const error = get(formik.errors, name);
  const touched = get(formik.touched, name);
  const showError = error && touched;

  return (
    <Grid item xs={6}>
      <Typography variant="subtitle2" mb={1}>
        {label}
        {required && '*'}
      </Typography>
      {children}
      {showError && <ErrorMessage message={error} />}
    </Grid>
  );
};

// Reusable card component for client details
const ClientDetailsCard = ({
  title,
  description,
  children,
}: {
  title: string;
  description: string;
  children: React.ReactNode;
}) => (
  <Grid component={Card} sx={{ width: '100%', maxWidth: 720 }}>
    <CardContent>
      <Typography variant="h6">{title}</Typography>
      <Typography variant="body3" color={wealthPalette.text.subtle}>
        {description}
      </Typography>
      <Grid container spacing={2} mt={1}>
        {children}
      </Grid>
    </CardContent>
  </Grid>
);

// Reusable form fields for basic information
const BasicInfoFields = ({
  formik,
  prefix = 'primary',
}: {
  formik: any;
  prefix?: string;
}) => {
  const { t } = useTranslation();

  return (
    <>
      <FieldWrapper
        formik={formik}
        name={`${prefix}.dob`}
        label={t('clientSettings.basicInfo.dateOfBirth')}
        required
      >
        <FormField
          formik={formik}
          name={`${prefix}.dob`}
          label=""
          type="date"
          dataTestId={`${prefix}_dob`}
          hideLabel
        />
      </FieldWrapper>

      {/* <FieldWrapper
        formik={formik}
        name={`${prefix}.lifeExpectancy`}
        label={t('clientSettings.basicInfo.lifeExpectancy')}
        required
      >
        <FormField
          formik={formik}
          name={`${prefix}.lifeExpectancy`}
          label=""
          options={LIFE_EXPECTANCY}
          placeholder={t('clientSettings.basicInfo.lifeExpectancy')}
        />
      </FieldWrapper> */}

      <FieldWrapper
        formik={formik}
        name={`${prefix}.stateOfResidence`}
        label={t('clientSettings.basicInfo.stateOfResidence')}
        required
      >
        <FormField
          formik={formik}
          name={`${prefix}.stateOfResidence`}
          label=""
          options={usStateNameValue}
        />
      </FieldWrapper>

      {prefix === 'primary' && (
        <FieldWrapper
          formik={formik}
          name={`${prefix}.maritalStatus`}
          label={t('clientSettings.basicInfo.maritalStatus')}
          required
        >
          <FormField
            formik={formik}
            name={`${prefix}.maritalStatus`}
            label=""
            options={Object.values(MaritalStatus).map((item) => ({
              value: item,
              name: t(getMaritalStatusL10nKey(item) ?? ''),
            }))}
          />
        </FieldWrapper>
      )}
    </>
  );
};

// Spouse selection component
const SpouseSelection = ({
  formik,
  clientContacts,
  shouldShowSpouseSelection,
  setSelectedSpouseContact,
  refetchContacts,
  contactsRefreshKey,
  setContactsRefreshKey,
  clientMember,
}: {
  formik: any;
  clientContacts: any[];
  shouldShowSpouseSelection: boolean;
  selectedSpouseContact: any;
  setSelectedSpouseContact: (contact: any) => void;
  refetchContacts: () => void;
  contactsRefreshKey: number;
  setContactsRefreshKey: React.Dispatch<React.SetStateAction<number>>;
  clientMember: any;
}) => {
  const { t } = useTranslation();

  // Initialize spouse selection if theres existing spouse data
  React.useEffect(() => {
    if (shouldShowSpouseSelection && clientContacts.length > 0) {
      // First check if there's already a spouse ID in form values
      if (formik.values.spouse?.id) {
        const existingSpouse = clientContacts.find(
          (contact) => contact.id === formik.values.spouse.id
        );

        if (existingSpouse) {
          // Store the spouse contact for use in other functions
          setSelectedSpouseContact(existingSpouse);
          return;
        }
      }

      // If no spouse in form values, check if primary contact card has a partner
      // This will automatically populate spouse data when modal opens
      const primaryContact = clientContacts.find(
        (contact) => contact.isPrimary
      );
      // Use partnerId as primary source, fallback to partner?.id for backward compatibility
      const spouseId = (primaryContact as any)?.partnerId || primaryContact?.partner?.id;
      if (spouseId) {
        const spouseContact = clientContacts.find(
          (contact) => contact.id === spouseId
        );
        if (spouseContact) {
          setSelectedSpouseContact(spouseContact);
          return;
        }
      }

      // If no spouse found, clear the selection
      setSelectedSpouseContact(null);
    } else {
      setSelectedSpouseContact(null);
    }
  }, [
    clientContacts,
    formik.values.spouse?.id,
    shouldShowSpouseSelection,
    setSelectedSpouseContact,
  ]);

  if (!shouldShowSpouseSelection) return null;

  // Get spouse validation error
  const spouseError = formik.errors.spouse;
  const spouseTouched = formik.touched.spouse;
  const showSpouseError = spouseError && spouseTouched;

  // Extract the specific error message for spouse ID
  const spouseIdError = typeof spouseError === 'object' && spouseError?.id ? spouseError.id : null;

  return (
    <Grid item xs={12}>
      <Typography variant="subtitle2" mb={1}>
        {t('clientSettings.basicInfo.selectSpouse')}
        <span style={{ color: wealthPalette.state.danger.base }}>*</span>
      </Typography>
      <SearchContactComponent
        key={`spouse-selection-${contactsRefreshKey}`}
        variant="card"
        fieldName="spouse"
        allowToAdd
        contactCardTypeAllowCreation={ContactCardType.Individual}
        contactCardDisplay={[ContactCardType.Individual]}
        label=""
        items={clientContacts.filter((contact) => !contact.isPrimary)}
        preselect={formik.values.spouse?.id}
        clientId={clientMember?.id}
        error={showSpouseError && spouseIdError ? spouseIdError : undefined}
        setValue={(contact) => {
          if (contact) {
            // Store the selected contact
            setSelectedSpouseContact(contact);

            // Set the complete spouse data including DOB and state from the contact
            formik.setFieldValue('spouse', {
              id: contact.id,
              dob: contact.dob ? contact.dob.slice(0, 10) : '',
              stateOfResidence: contact.addressStateShort || '',
            });
            // Mark spouse as touched to trigger validation
            formik.setFieldTouched('spouse', true);
          } else {
            setSelectedSpouseContact(null);
            formik.setFieldValue('spouse', undefined);
            formik.setFieldTouched('spouse', true);
          }
        }}
        onNewContactCreated={(contact) => {
          if (contact) {
            // Refresh the contacts list to include the newly created contact
            refetchContacts();
            // Increment the refresh key to force re-render
            setContactsRefreshKey((prev: number) => prev + 1);
          }
        }}
      />
    </Grid>
  );
};

const spouseMaritalStatuses = [
  MaritalStatus.CivilUnion,
  MaritalStatus.DomesticPartnership,
  MaritalStatus.Married,
  MaritalStatus.OtherCommittedRelationship,
  MaritalStatus.Widowed,
];

// Add the skeleton component after the imports and before the FormField component
const BasicInfoSkeleton = () => {
  return (
    <Grid container justifyContent="flex-start" gap={2}>
      {/* Client Details Card Skeleton */}
      <Grid component={Card} sx={{ width: '100%', maxWidth: 720 }}>
        <CardContent>
          <Skeleton variant="text" width="60%" height={32} />
          <Skeleton variant="text" width="80%" height={20} sx={{ mt: 1 }} />
          <Grid container spacing={2} mt={1}>
            <Grid item xs={6}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={6}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={6}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={6}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={12}>
              <Skeleton variant="text" width="30%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Grid>
    </Grid>
  );
};

// Update the main BasicInfo component to include loading state logic
export default function BasicInfo({
  registerSubmitter,
}: {
  registerSubmitter: (
    tab: ClientSettingsTab,
    submitFn: () => Promise<boolean>
  ) => void;
}) {
  const { t } = useTranslation();
  const [clientSettings, setClientSettings] = useAtom(clientSettingsDataAtom);
  const clientMember = useAtomValue(clientUserAtom);
  const [selectedSpouseContact, setSelectedSpouseContact] =
    React.useState<any>(null);
  const [contactsRefreshKey, setContactsRefreshKey] = React.useState(0);
  const [saveError, setSaveError] = React.useState<string | null>(null);
  const [isSaving, setIsSaving] = React.useState(false);

  // Get EstateFlow AD hook for syncing
  const { saveEstateFlowConfiguration } = useEstateFlowAD(clientMember?.id);

  // Get client contacts to fetch spouse information
  const {
    contactCards: clientContacts,
    isLoading: contactsLoading,
    refetchContactCards,
  } = useUserContacts(clientMember?.id || undefined);

  // Use primary contact from clientContacts as the source of truth
  const contactCard = useMemo(() => {
    return (
      clientContacts.find((contact) => contact.isPrimary) ||
      clientMember?.contactCard
    );
  }, [clientContacts, clientMember?.contactCard]);

  const {
    addressStateShort = '',
    dob = '',
    maritalStatus = '',
  } = contactCard || {};
  const clientMemberName = contactCard ? formatContactName(contactCard) : '';

  // Get saveContactCard hook for backend updates
  const { saveContactCard } = useContacts();

  // Get client settings hook for fetching life expectancy and other settings
  const {
    clientSettings: fetchedClientSettings,
    loading: clientSettingsLoading,
    fetchClientSettings,
  } = useGetClientSettings(clientMember?.id ?? undefined);

  // Force refetch contacts when modal opens to ensure fresh data
  useEffect(() => {
    if (clientMember?.id) {
      refetchContactCards();
      // Also fetch client settings on modal open
      fetchClientSettings();
    }
  }, [clientMember?.id, refetchContactCards, fetchClientSettings]);

  // Use ref to access latest saveContactCard function
  const saveContactCardRef = useRef(saveContactCard);
  saveContactCardRef.current = saveContactCard;

  // Initial values from contact card data (source of truth), then fallback to client settings atom
  const initialValues = useMemo(() => {
    const values = {
      primary: {
        dob:
          (dob && typeof dob === 'string' ? dob.slice(0, 10) : '') ??
          clientSettings?.primary?.dob ??
          '',
        stateOfResidence:
          addressStateShort ?? clientSettings?.primary?.stateOfResidence ?? '',
        // lifeExpectancy: clientSettings?.primary?.lifeExpectancy ?? '',
        maritalStatus:
          maritalStatus ?? clientSettings?.primary?.maritalStatus ?? '',
      },
      spouse: clientSettings?.spouse ?? undefined,
    };
    return values;
  }, [
    dob,
    addressStateShort,
    maritalStatus,
    clientSettings?.primary?.dob,
    clientSettings?.primary?.stateOfResidence,
    // clientSettings?.primary?.lifeExpectancy,
    clientSettings?.primary?.maritalStatus,
    clientSettings?.spouse,
  ]);

  // Validation schema - memoized to prevent recreation on every render
  const validationSchema = useMemo(
    () =>
      Yup.object({
        primary: Yup.object({
          dob: Yup.string()
            .nullable()
            .required(t('clientSettings.basicInfo.dobRequired'))
            .matches(
              /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/,
              t('clientSettings.basicInfo.dobFormat')
            )
            .test(
              'not-future',
              t('clientSettings.basicInfo.dobFuture'),
              (value) => {
                if (!value) return false;
                const inputDate = new Date(value);
                return inputDate <= new Date();
              }
            ),
          stateOfResidence: Yup.string().required(
            t('clientSettings.basicInfo.stateRequired')
          ),
          maritalStatus: Yup.string().required(
            t('clientSettings.basicInfo.maritalStatusRequired')
          ),
        }),
        // Basic spouse validation - only require spouse ID when marital status needs a spouse
        spouse: Yup.object().when('primary.maritalStatus', {
          is: (maritalStatus: string) =>
            spouseMaritalStatuses.includes(maritalStatus as MaritalStatus),
          then: Yup.object({
            id: Yup.string().required(
              t('clientSettings.basicInfo.spouseRequired')
            ),
          }),
          otherwise: Yup.object().notRequired(),
        }),
      }),
    [t]
  );

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: () => {}, // Empty function since we handle submit manually
    enableReinitialize: false, // Disable reinitialize to prevent form reset
    validateOnChange: false,
    validateOnBlur: false,
  });

  const shouldShowSpouseSelection = useMemo(() => {
    return spouseMaritalStatuses.includes(
      formik.values.primary.maritalStatus as MaritalStatus
    );
  }, [formik.values.primary.maritalStatus]);

  // Determine if we should show loading state
  const isLoading = useMemo(() => {
    // Show loading if:
    // 1. No client member data yet
    // 2. No contact card data yet
    // 3. No client contacts loaded yet
    // 4. Client contacts are still loading
    // 5. Client settings are still loading
    const hasBasicData =
      clientMember &&
      contactCard &&
      clientContacts &&
      clientContacts.length > 0;

    const isClientSettingsLoaded = !clientSettingsLoading;
    const isContactsLoaded = !contactsLoading;

    return !hasBasicData || !isClientSettingsLoaded || !isContactsLoaded;
  }, [
    clientMember,
    contactCard,
    clientContacts,
    clientSettingsLoading,
    contactsLoading,
  ]);

  // Track if we were loading on the previous render to apply a one-time delay after load
  const hadLoadingRef = useRef<boolean>(true);


  // Single useEffect to update form values when data changes
  useEffect(() => {
    if (!isLoading && contactCard) {
      const scheduleSetValues = (values: any) =>
        new Promise<void>((resolve) => setTimeout(() => resolve(formik.setValues(values, false)), 2000));

      const currentValues = formik.values;
      const newValues = {
        primary: {
          dob:
            (contactCard.dob && typeof contactCard.dob === 'string'
              ? contactCard.dob.slice(0, 10)
              : '') ?? '',
          stateOfResidence: contactCard.addressStateShort ?? '',
          // lifeExpectancy:
          //   clientSettings?.primary?.lifeExpectancy ??
          //   currentValues.primary.lifeExpectancy ??
          //   '',
          maritalStatus: contactCard.maritalStatus ?? '',
        },
        spouse: clientSettings?.spouse ?? currentValues.spouse ?? undefined,
      };

      // Always update with contact card data to ensure form reflects the source of truth
      // Only skip if values are exactly the same to prevent unnecessary re-renders
      if (JSON.stringify(currentValues) !== JSON.stringify(newValues)) {
        // Wait 2 seconds before setting values when modal opens or data just loaded
        scheduleSetValues(newValues);
      }
    }
  }, [
    contactCard?.dob,
    contactCard?.addressStateShort,
    contactCard?.maritalStatus,
    clientSettings?.spouse,
    isLoading,
  ]);

  // Don't update form values from initialValues to prevent form reset during submission
  // The form will be initialized with the correct values on first load

  useEffect(() => {
    if (
      formik.values.primary.maritalStatus === MaritalStatus.Single ||
      formik.values.primary.maritalStatus === MaritalStatus.Engaged
    ) {
      formik.setFieldValue('spouse', undefined);
    }
  }, [formik.values.primary.maritalStatus]);

  // Set selectedSpouseContact from clientSettings.spouse if available
  useEffect(() => {
    if (clientSettings?.spouse?.id && clientContacts.length > 0) {
      const match = clientContacts.find(
        (contact) => contact.id === clientSettings?.spouse?.id
      );
      if (match) {
        setSelectedSpouseContact(match);
      }
    }
  }, [clientSettings?.spouse?.id, clientContacts, contactCard]);

  // Update spouse form values when selectedSpouseContact changes
  useEffect(() => {
    if (selectedSpouseContact && shouldShowSpouseSelection) {
      const currentSpouse = formik.values.spouse;
      const newSpouse = {
        id: selectedSpouseContact.id,
        dob: selectedSpouseContact.dob
          ? selectedSpouseContact.dob.slice(0, 10)
          : '',
        // lifeExpectancy: currentSpouse?.lifeExpectancy || '',
        stateOfResidence: selectedSpouseContact.addressStateShort || '',
        maritalStatus: selectedSpouseContact.maritalStatus || '',
      };

      // Only update if values are actually different
      if (JSON.stringify(currentSpouse) !== JSON.stringify(newSpouse)) {
        // Set values without triggering validation
        formik.setFieldValue('spouse', newSpouse, false);
      }
    }
  }, [selectedSpouseContact, shouldShowSpouseSelection]);

  useEffect(() => {
    console.log('formik.values', formik.values)
  }, [formik.values])

  // Update clientSettings atom when fetched client settings are available
  useEffect(() => {
    if (fetchedClientSettings && contactCard && !isLoading) {
      // Find spouse contact from clientContacts if partner exists
      let spouseData = undefined;
      // Use partnerId as primary source, fallback to partner?.id for backward compatibility
      const spouseId = (contactCard as any).partnerId || contactCard.partner?.id;
      if (spouseId && clientContacts.length > 0) {
        const spouseContact = clientContacts.find(
          (contact) => contact.id === spouseId
        );
        if (spouseContact) {
          spouseData = {
            id: spouseContact.id || '',
            dob: spouseContact.dob ? spouseContact.dob.slice(0, 10) : '',
            stateOfResidence: spouseContact.addressStateShort || '',
            // lifeExpectancy:
            //   fetchedClientSettings.basicInfo?.lifeExpectancy?.spouse || '',
            // maritalStatus: spouseContact.maritalStatus || '',
          };
        }
      }

      const updatedClientSettings = {
        primary: {
          id: contactCard.id || '',
          dob: contactCard.dob ? contactCard.dob.slice(0, 10) : '',
          stateOfResidence: contactCard.addressStateShort || '',
          // lifeExpectancy:
          //   fetchedClientSettings.basicInfo?.lifeExpectancy?.member || '',
          maritalStatus: contactCard.maritalStatus || '',
        },
        spouse: spouseData,
      };

      // Only update if the data is actually different to prevent unnecessary re-renders
      if (
        !clientSettings ||
        JSON.stringify(clientSettings) !== JSON.stringify(updatedClientSettings)
      ) {
        setClientSettings((prev) => ({
          ...prev,
          ...updatedClientSettings,
        }));
      }
    }
  }, [
    fetchedClientSettings,
    contactCard,
    clientContacts,
    isLoading,
    setClientSettings,
  ]);

  // Use ref to access latest formik values
  const formikRef = useRef(formik);
  formikRef.current = formik;

  // Handle form submit and atom sync - memoized to prevent infinite loops
  const handleSubmit = React.useCallback(async (): Promise<boolean> => {
    const currentFormik = formikRef.current;

    // Clear any previous errors and set saving state
    setSaveError(null);
    setIsSaving(true);

    try {
      // Run validation first
      const errors = await currentFormik.validateForm();

      if (Object.keys(errors).length > 0) {
        // Mark all fields as touched to show validation errors
        const touchedFields: any = {};
        const markFieldsTouched = (errors: any, prefix = '') => {
          Object.keys(errors).forEach((key) => {
            const fieldName = prefix ? `${prefix}.${key}` : key;
            if (typeof errors[key] === 'object' && errors[key] !== null) {
              // Mark the parent field as touched
              if (prefix) {
                touchedFields[prefix] = true;
              }
              markFieldsTouched(errors[key], fieldName);
            } else {
              touchedFields[fieldName] = true;
              // Also mark the parent field as touched
              if (prefix) {
                touchedFields[prefix] = true;
              }
            }
          });
        };
        markFieldsTouched(errors);
        currentFormik.setTouched(touchedFields, true);
        return false; // Return false to indicate validation failure
      }
      // Capture the current form values before any potential re-renders
      const currentFormValues = {
        primary: {
          id: contactCard?.id || '',
          dob: currentFormik.values.primary.dob,
          stateOfResidence: currentFormik.values.primary.stateOfResidence,
          // lifeExpectancy: currentFormik.values.primary.lifeExpectancy,
          maritalStatus: currentFormik.values.primary
            .maritalStatus as MaritalStatus,
        },
        spouse: currentFormik.values.spouse,
      };

      // Update client settings atom with form values (which are based on contact card data)
      setClientSettings((prev) => ({
        ...prev,
        ...currentFormValues,
        federalEstateSettings: clientSettings?.federalEstateSettings,
        stateEstateSettings: clientSettings?.stateEstateSettings,
      }));

      // Save contact card changes to backend
      if (contactCard?.id) {
        // Merge existing contact card data with updated fields to prevent wiping out other fields
        const updatedContactCard: Omit<ContactCard, 'wid'> = {
          ...contactCard,
          dob: currentFormValues.primary.dob,
          addressState: currentFormValues.primary.stateOfResidence,
          addressStateShort: currentFormValues.primary.stateOfResidence,
          addressFormatted:
            US_STATES.find(
              ({ uspsAbbreviation }) =>
                uspsAbbreviation === currentFormValues.primary.stateOfResidence
            )?.name ||
            currentFormValues.primary.stateOfResidence ||
            '',
          maritalStatus: currentFormValues.primary
            .maritalStatus as MaritalStatus,
          isLegalNameConfirmed: false,
          relationship: contactCard.relationship as any, // Cast to avoid type mismatch
          // Ensure geodata is properly formatted as JSON string
          geodata:
            typeof contactCard?.geodata === 'string' &&
            contactCard?.geodata !== 'null'
              ? contactCard?.geodata
              : null,
        };

        // Add partner information if marital status requires a spouse
        if (
          spouseMaritalStatuses.includes(
            currentFormValues.primary.maritalStatus as MaritalStatus
          )
        ) {
          if (currentFormValues.spouse?.id) {
            updatedContactCard.partner = {
              id: currentFormValues.spouse.id,
            };
            updatedContactCard.partnerId = currentFormValues.spouse.id;
          }
        } else {
          // Clear partner info if marital status doesn't require a spouse
          updatedContactCard.partner = null;
          updatedContactCard.partnerId = null;
        }

        // Save primary contact card
        const primarySaveResult = await saveContactCardRef.current({
          variables: {
            metadata: convertToInput(updatedContactCard as ContactCard),
            ...(clientMember?.id
              ? { authorization: { asAdvisor: { clientId: clientMember.id } } }
              : {}),
          },
        });
        console.log('saved member card');

        if (primarySaveResult.errors) {
          throw new Error(`Failed to save primary contact: ${primarySaveResult.errors.map(e => e.message).join(', ')}`);
        }

        // Save spouse contact card with DOB and state of residence from formik values (same pattern as primary)
        if (currentFormValues.spouse?.id) {
          const spouseContactCard = clientContacts.find(
            (contact) => contact.id === currentFormValues.spouse?.id
          );

          console.log({ spouseContactCard})

          if (spouseContactCard) {
            const spouseState = currentFormValues.spouse?.stateOfResidence || '';
            const spouseStateName =
              US_STATES.find(({ uspsAbbreviation }) => uspsAbbreviation === spouseState)?.name || spouseState || '';

            const updatedSpouseContactCard: Omit<ContactCard, 'wid'> = {
              ...spouseContactCard,
              dob: currentFormValues.spouse?.dob || '',
              addressState: spouseState,
              addressStateShort: spouseState,
              addressFormatted: spouseStateName,
            };

            console.log({ updatedSpouseContactCard});

            const spouseSaveResult = await saveContactCardRef.current({
              variables: {
                metadata: convertToInput(updatedSpouseContactCard as ContactCard),
                ...(clientMember?.id
                  ? { authorization: { asAdvisor: { clientId: clientMember.id } } }
                  : {}),
              },
            });
            console.log('saved spouse card');

            if (spouseSaveResult.errors) {
              throw new Error(
                `Failed to save spouse contact: ${spouseSaveResult.errors.map((e: any) => e.message).join(', ')}`
              );
            }
          }
        }





        const currentYear = new Date().getFullYear();
        const basicInfoInput = {
          lifeExpectancy: {
            member: currentYear,
            spouse: currentYear,
          },
        };
        // await saveBasicInfo(basicInfoInput, clientMember?.id || undefined);

        // Sync client settings to EstateFlow
        if (clientMember?.id && saveEstateFlowConfiguration) {
          await syncClientSettingsToEstateFlow(
            saveEstateFlowConfiguration,
            undefined, // No federal settings for basic info
            undefined, // No state settings for basic info
            basicInfoInput,
            clientMember.id
          );
        }
      }

      // Refresh contact data after successful save
      refetchContactCards();

      return true; // Return true to indicate success
    } catch (error) {
      logger.error('Failed to update client settings:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred while saving. Please try again.';
      setSaveError(errorMessage);
      return false; // Return false to indicate error
    } finally {
      setIsSaving(false);
    }
  }, [
    clientMember?.id,
    clientContacts,
    selectedSpouseContact,
    setClientSettings,
    contactCard,
    clientSettings,
    saveEstateFlowConfiguration,
    refetchContactCards,
    setSaveError,
    setIsSaving,
  ]);

  // Register the form submit function with the modal - only when handleSubmit changes
  useEffect(() => {
    registerSubmitter(ClientSettingsTab.BasicInfo, handleSubmit);
  }, [handleSubmit, registerSubmitter]);

  const shouldShowSpouseCard = useMemo(() => {
    return shouldShowSpouseSelection && formik.values.spouse;
  }, [shouldShowSpouseSelection, formik.values.spouse]);

  // Show skeleton while loading
  if (isLoading) {
    return <BasicInfoSkeleton />;
  }

  return (
    <FormikProvider value={formik}>
      <Grid container justifyContent="flex-start" gap={2}>
        {/* Error Message Display */}
        {saveError && (
          <Grid item xs={12}>
            <ErrorMessage message={saveError} />
          </Grid>
        )}

        <ClientDetailsCard
          title={`${clientMemberName} — ${t(
            'clientSettings.basicInfo.clientDetails'
          )}`}
          description={t('clientSettings.basicInfo.description')}
        >
          <BasicInfoFields formik={formik} prefix="primary" />
          <SpouseSelection
            formik={formik}
            clientContacts={clientContacts}
            shouldShowSpouseSelection={shouldShowSpouseSelection}
            selectedSpouseContact={selectedSpouseContact}
            setSelectedSpouseContact={setSelectedSpouseContact}
            refetchContacts={refetchContactCards}
            contactsRefreshKey={contactsRefreshKey}
            setContactsRefreshKey={setContactsRefreshKey}
            clientMember={clientMember}
          />
        </ClientDetailsCard>

        {/* Spouse Card - Testing with basic validation only */}
        {shouldShowSpouseCard && formik.values.spouse && (
          <ClientDetailsCard
            title={
              selectedSpouseContact
                ? `${formatContactName(selectedSpouseContact)} — ${t(
                    'clientSettings.basicInfo.clientDetails'
                  )}`
                : t('clientSettings.basicInfo.clientDetails')
            }
            description={t('clientSettings.basicInfo.spouseDescription')}
          >
            <BasicInfoFields formik={formik} prefix="spouse" />
          </ClientDetailsCard>
        )}
      </Grid>
    </FormikProvider>
  );
}
