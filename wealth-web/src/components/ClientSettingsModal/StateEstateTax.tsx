import * as Yup from 'yup';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Checkbox from '@mui/material/Checkbox';
import ErrorMessage from 'components/_common/ErrorMessage';
import FormNumberInput from 'components/_vault/_form/FormNumberInput';
import Grid from '@mui/material/GridLegacy';
import React, { useMemo, useEffect, useRef } from 'react';
import Skeleton from '@mui/material/Skeleton';
import TextField from '@mui/material/TextField';
import Typography from 'components/@wealth/atoms/Typography/Typography';
import WealthButton from 'components/@wealth/atoms/Button/Button';
import saveStateEstateSettings from 'hooks/client-settings/saveStateEstateSettings';
import useEstateTaxCalculations from 'hooks/client-settings/useEstateTaxCalculations';
import useContextData from 'hooks/useContextData';
import useGetClientSettings from 'hooks/client-settings/useGetClientSettings';
import useUserContacts from 'hooks/useUserContacts';
import { ClientSettingsTab } from './types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { US_STATES } from 'models/common/us-states';
import { clientSettingsDataAtom } from 'atomStore/clientSettings';
import { clientUserAtom } from 'atomStore/client/client';
import { faCircleInfo, faCircleUser } from '@fortawesome/pro-regular-svg-icons';
import { faRotate } from '@fortawesome/pro-regular-svg-icons';
import { formatContactName } from 'utils/formatContact';
import { get, safeParse } from 'utils/objectHelpers';
import { logger } from 'utils/logger';
import { useAtom, useAtomValue } from 'jotai';
import { useFormik, FormikProvider } from 'formik';
import { useTranslation } from 'react-i18next';
import { wealthPalette } from 'theme/wealth-default-theme';
import {
  Asset,
  ContactCard,
  ManualRealEstateInput,
  OwnerType,
  RevocableTrustType,
  WealthAssetType,
} from 'generated/API';
import useEstateFlowAD from 'hooks/useEstateFlowAD';
import { syncClientSettingsToEstateFlow } from 'hooks/client-settings/syncToEstateFlow';

// State-specific configuration for form fields
const STATE_CONFIG = {
  // States with comprehensive estate tax forms
  NY: {
    name: 'New York',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: true,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  MA: {
    name: 'Massachusetts',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  CT: {
    name: 'Connecticut',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: true,
      outOfStateProperty: true,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  IL: {
    name: 'Illinois',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: true,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  MD: {
    name: 'Maryland',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: true,
    },
  },
  MN: {
    name: 'Minnesota',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: true,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  NJ: {
    name: 'New Jersey',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: true,
      stateEstateTaxPaidToOtherStates: true,
      statePortabilityElection: true,
    },
  },
  OR: {
    name: 'Oregon',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  RI: {
    name: 'Rhode Island',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  VT: {
    name: 'Vermont',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: true,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  WA: {
    name: 'Washington',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  DC: {
    name: 'District of Columbia',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  ME: {
    name: 'Maine',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: true,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  // States without estate tax
  CA: {
    name: 'California',
    hasStateEstateTax: false,
    fields: {
      stateTaxRate: false,
      stateGiftAndEstateTaxExemption: false,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: false,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  TX: {
    name: 'Texas',
    hasStateEstateTax: false,
    fields: {
      stateTaxRate: false,
      stateGiftAndEstateTaxExemption: false,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: false,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  FL: {
    name: 'Florida',
    hasStateEstateTax: false,
    fields: {
      stateTaxRate: false,
      stateGiftAndEstateTaxExemption: false,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: false,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  AZ: {
    name: 'Arizona',
    hasStateEstateTax: false,
    fields: {
      stateTaxRate: false,
      stateGiftAndEstateTaxExemption: false,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: false,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  NV: {
    name: 'Nevada',
    hasStateEstateTax: false,
    fields: {
      stateTaxRate: false,
      stateGiftAndEstateTaxExemption: false,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: false,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
  HI: {
    name: 'Hawaii',
    hasStateEstateTax: true,
    fields: {
      stateTaxRate: true,
      stateGiftAndEstateTaxExemption: true,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: true,
      adjustedGifts: true,
      stateEstateTaxPaidToOtherStates: true,
      statePortabilityElection: true,
    },
  },
  // Default configuration for states not explicitly defined
  DEFAULT: {
    name: 'Unknown State',
    hasStateEstateTax: false,
    fields: {
      stateTaxRate: false,
      stateGiftAndEstateTaxExemption: false,
      stateGiftAndEstateTaxExemptionUsed: false,
      outOfStateProperty: false,
      adjustedGifts: false,
      stateEstateTaxPaidToOtherStates: false,
      statePortabilityElection: false,
    },
  },
};

// Utility function to get state abbreviation from full state name
const getStateAbbr = (stateLong: string | null | undefined) => {
  return US_STATES.find(({ name }) => name === stateLong)?.uspsAbbreviation;
};

// Utility function to calculate out-of-state property value for a member
const calculateOutOfStateProperty = (
  assets: Asset[],
  contactCards: ContactCard[],
  memberId: string,
  memberStateOfResidence: string
): number => {
  return assets.reduce((total, asset) => {
    // Only consider real estate assets
    if (
      asset.wealthAssetType !== WealthAssetType.RealEstate &&
      asset.wealthAssetType !== WealthAssetType.RealEstateInvestment
    ) {
      return total;
    }

    // Parse the asset info to get the state
    const assetInfo = safeParse<ManualRealEstateInput>(asset.assetInfo);
    const assetState = getStateAbbr(assetInfo?.state);

    // Skip if asset is in the same state as member's residence
    if (assetState === memberStateOfResidence) {
      return total;
    }

    // Parse ownership to calculate member's percentage
    const ownership = safeParse<{
      owners: Array<{
        id: string;
        percentage: number;
        type: string;
        subOwners?: Array<{ id: string }>;
      }>;
    }>(asset.ownership);
    if (!ownership?.owners) {
      return total;
    }

    let memberOwnershipPercentage = 0;

    // Calculate member's ownership percentage
    ownership.owners.forEach((owner) => {
      if (owner.type === OwnerType.Individual && owner.id === memberId) {
        // Owner = Client contact card
        // If only a percentage of the asset is owned by the client, consider only that percentage
        memberOwnershipPercentage += owner.percentage || 0;
      } else if (owner.type === OwnerType.Joint && owner.subOwners) {
        // Owner = Joint Ownership
        // Check if member is one of the joint owners
        const isJointOwner = owner.subOwners.some(
          (subOwner) => subOwner.id === memberId
        );
        if (isJointOwner) {
          // If multiple joint owners exist (more than 2+) then split the value of the property in equal shares
          // Otherwise, consider 50% of the value of the property
          const jointOwnerCount = owner.subOwners.length;
          const jointOwnershipPercentage =
            jointOwnerCount > 2
              ? (owner.percentage || 0) / jointOwnerCount
              : (owner.percentage || 0) / 2;
          memberOwnershipPercentage += jointOwnershipPercentage;
        }
      } else if (owner.type === OwnerType.Trust) {
        // Trust ownership - check if member is a beneficiary/owner of the trust
        const trust = contactCards.find((cc) => cc.id === owner.id);
        if (trust) {
          const trustOwnership = safeParse<{
            owners: Array<{ id: string; percentage: number; type: string }>;
          }>(trust.owners);

          // Check the trust type from the contact card
          const isIndividualRevocableTrust =
            trust.revocableTrustType === RevocableTrustType.Individual;
          const isJointRevocableTrust =
            trust.revocableTrustType === RevocableTrustType.Joint;

          if (trustOwnership?.owners) {
            // Find the member's ownership in the trust
            const memberTrustOwnership = trustOwnership.owners.find(
              (o) => o.id === memberId
            );

            if (memberTrustOwnership) {
              let trustOwnershipPercentage = 0;

              if (isIndividualRevocableTrust) {
                // Owner = Individual Revocable Trust where client = trustor
                // If only a percentage of the asset is owned by the IRT, consider only that percentage
                trustOwnershipPercentage = owner.percentage || 0;
              } else if (isJointRevocableTrust) {
                // Owner = Joint Revocable Trust where client = trustor
                // Consider half the value of the property
                // If a percentage of the asset is owned by the JRT, consider only that percentage
                trustOwnershipPercentage = (owner.percentage || 0) / 2;
              }
              // Note: For v1, we are intentionally not including entities that own real estate property

              memberOwnershipPercentage += trustOwnershipPercentage;
            }
          }
        }
      }
      // Note: For v1, we are intentionally not including entities (OwnerType.Entity) that own real estate property
    });

    // Calculate the member's portion of this asset's value
    const assetValue = asset.balanceCurrent || 0;
    const memberAssetValue = (assetValue * memberOwnershipPercentage) / 100;

    return total + memberAssetValue;
  }, 0);
};

// Helper function to get state configuration
const getStateConfig = (stateCode: string) => {
  return (
    STATE_CONFIG[stateCode as keyof typeof STATE_CONFIG] || STATE_CONFIG.DEFAULT
  );
};

// Reusable form field component with error handling
const FormField = ({
  formik,
  name,
  label,
  type = 'text',
  placeholder,
  onChange,
  dataTestId,
  hideLabel = false,
  suffix = '',
  prefix = '',
  isCurrency = false,
  isPercentage = false,
  readOnly = false,
}: {
  formik: any;
  name: string;
  label: string;
  type?: 'text' | 'number';
  placeholder?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  dataTestId?: string;
  hideLabel?: boolean;
  infoIcon?: boolean;
  suffix?: string;
  prefix?: string;
  isCurrency?: boolean;
  isPercentage?: boolean;
  readOnly?: boolean;
}) => {
  const error = get(formik.errors, name);
  const touched = get(formik.touched, name);
  const showError = error && touched;

  // If it's a currency field, use FormNumberInput
  if (isCurrency) {
    return (
      <FormNumberInput
        currency
        name={name}
        label={hideLabel ? '' : label}
        placeholder={placeholder}
        value={get(formik.values, name) || ''}
        onChange={onChange || formik.handleChange}
        error={!!showError}
        helperText={showError ? error : ''}
        data-testid={dataTestId}
        labelId={name}
        disabled={false} // Allow programmatic updates even when readOnly
        sx={
          readOnly
            ? {
                '& .MuiOutlinedInput-root': {
                  backgroundColor: wealthPalette.surface.soft,
                },
                '& .MuiInputBase-input': {
                  cursor: 'not-allowed',
                },
              }
            : undefined
        }
        InputProps={{
          startAdornment: prefix ? (
            <Typography variant="body2" color={wealthPalette.text.subtle}>
              {prefix}
            </Typography>
          ) : undefined,
          endAdornment: suffix ? (
            <Typography variant="body2" color={wealthPalette.text.subtle}>
              {suffix || 'USD'}
            </Typography>
          ) : undefined,
        }}
      />
    );
  }

  // If it's a percentage field, use FormNumberInput with percentage
  if (isPercentage) {
    return (
      <FormNumberInput
        percentage
        name={name}
        label={hideLabel ? '' : label}
        placeholder={placeholder}
        value={get(formik.values, name) || ''}
        onChange={onChange || formik.handleChange}
        error={!!showError}
        helperText={showError ? error : ''}
        data-testid={dataTestId}
        labelId={name}
        disabled={false} // Allow programmatic updates even when readOnly
        sx={
          readOnly
            ? {
                '& .MuiOutlinedInput-root': {
                  backgroundColor: wealthPalette.surface.soft,
                },
                '& .MuiInputBase-input': {
                  cursor: 'not-allowed',
                },
              }
            : undefined
        }
        InputProps={{
          startAdornment: prefix ? (
            <Typography variant="body2" color={wealthPalette.text.subtle}>
              {prefix}
            </Typography>
          ) : undefined,
          endAdornment: suffix ? (
            <Typography variant="body2" color={wealthPalette.text.subtle}>
              {suffix || '%'}
            </Typography>
          ) : undefined,
        }}
      />
    );
  }

  return (
    <TextField
      fullWidth
      label={hideLabel ? '' : label}
      name={name}
      type={type}
      placeholder={placeholder}
      value={get(formik.values, name) || ''}
      onChange={onChange || formik.handleChange}
      error={!!showError}
      helperText={showError ? error : ''}
      InputLabelProps={{ shrink: true }}
      data-testid={dataTestId}
      disabled={false} // Allow programmatic updates even when readOnly
      sx={
        readOnly
          ? {
              '& .MuiOutlinedInput-root': {
                backgroundColor: wealthPalette.surface.soft,
              },
              '& .MuiInputBase-input': {
                cursor: 'not-allowed',
              },
            }
          : undefined
      }
      InputProps={{
        startAdornment: prefix ? (
          <Typography variant="body2" color={wealthPalette.text.subtle}>
            {prefix}
          </Typography>
        ) : undefined,
        endAdornment: suffix ? (
          <Typography variant="body2" color={wealthPalette.text.subtle}>
            {suffix}
          </Typography>
        ) : undefined,
      }}
    />
  );
};

// Reusable field wrapper with label and error handling
const FieldWrapper = ({
  formik,
  name,
  label,
  children,
  required = false,
  infoIcon = false,
  containerSize = 12,
}: {
  formik: any;
  name: string;
  label: string;
  children: React.ReactNode;
  required?: boolean;
  infoIcon?: boolean;
  containerSize?: number;
}) => {
  const error = get(formik.errors, name);
  const touched = get(formik.touched, name);
  const showError = error && touched;

  return (
    <Grid item xs={containerSize}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        <Typography variant="subtitle2">
          {label} {required && '*'}
        </Typography>
        {infoIcon && (
          <FontAwesomeIcon
            icon={faCircleInfo}
            color={wealthPalette.text.subtle}
            style={{ fontSize: 14 }}
          />
        )}
      </Box>
      {children}
      {showError && <ErrorMessage message={error} />}
    </Grid>
  );
};

// Reusable card component for state estate tax sections
const StateEstateCard = ({
  title,
  description,
  children,
  onReset,
  onSave,
  icon,
}: {
  title: string;
  description: string;
  children: React.ReactNode;
  onReset?: () => void;
  onSave?: () => void;
  icon?: React.ReactNode;
}) => (
  <Grid component={Card} sx={{ width: '100%', maxWidth: 720 }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        {icon && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>{icon}</Box>
        )}
        <Typography variant="h6">{title}</Typography>
      </Box>
      <Typography variant="body3" color={wealthPalette.text.subtle}>
        {description}
      </Typography>
      <Grid container spacing={2} mt={1}>
        {children}
      </Grid>
      {(onReset || onSave) && (
        <Box
          sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}
        >
          {onReset && (
            <WealthButton
              variant="outlined"
              startIcon={<FontAwesomeIcon icon={faRotate} />}
              onClick={onReset}
            >
              Reset Assumptions
            </WealthButton>
          )}
        </Box>
      )}
    </CardContent>
  </Grid>
);

// Add the skeleton component
const StateEstateTaxSkeleton = () => {
  return (
    <Grid container justifyContent="flex-start" gap={2}>
      {/* State Estate Tax Assumptions Card Skeleton */}
      <Grid component={Card} sx={{ width: '100%', maxWidth: 720 }}>
        <CardContent>
          <Skeleton variant="text" width="60%" height={32} />
          <Skeleton variant="text" width="80%" height={20} sx={{ mt: 1 }} />
          <Grid container spacing={2} mt={1}>
            <Grid item xs={6}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={6}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={12}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={12}>
              <Skeleton variant="text" width="60%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={24}
                sx={{ mt: 1 }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Grid>
    </Grid>
  );
};

// Update the main StateEstateTax component
export default function StateEstateTax({
  registerSubmitter,
}: {
  registerSubmitter: (
    tab: ClientSettingsTab,
    submitFn: () => Promise<boolean>
  ) => void;
}) {
  const { t } = useTranslation();
  const [clientSettings, setClientSettings] = useAtom(clientSettingsDataAtom);
  const clientMember = useAtomValue(clientUserAtom);
  const contactCard = clientMember?.contactCard;
  const clientMemberName = contactCard ? formatContactName(contactCard) : '';

  // Get EstateFlow AD hook for syncing
  const { saveEstateFlowConfiguration } = useEstateFlowAD(clientMember?.id);

  // Get assets and contact cards for out-of-state property calculation
  const { assets, contactCards } = useContextData();

  // Get client contacts to fetch spouse information
  const { contactCards: clientContacts } = useUserContacts(
    clientMember?.id || undefined
  );

  // Get client settings hook for fetching state estate settings
  const { clientSettings: fetchedClientSettings, fetchClientSettings } =
    useGetClientSettings(clientMember?.id ?? undefined);

  // Use the consolidated estate tax calculations hook
  const { memberStateData, spouseStateData } = useEstateTaxCalculations();

  // Get state configuration based on member's state of residence
  const memberStateCode = clientSettings?.primary?.stateOfResidence || '';
  const memberStateConfig = useMemo(
    () => getStateConfig(memberStateCode),
    [memberStateCode]
  );

  // Get state configuration based on spouse's state of residence (if different)
  const spouseStateCode =
    clientSettings?.spouse?.stateOfResidence || memberStateCode;
  const spouseStateConfig = useMemo(
    () => getStateConfig(spouseStateCode),
    [spouseStateCode]
  );

  // Function to get spouse name from contact cards using spouse ID
  const getSpouseName = useMemo(() => {
    if (!clientSettings?.spouse?.id || !clientContacts.length) {
      return 'Test Contact'; // Fallback for tests
    }

    const spouseContact = clientContacts.find(
      (contact) => contact.id === clientSettings.spouse?.id
    );

    return spouseContact ? formatContactName(spouseContact) : 'Test Contact';
  }, [clientSettings?.spouse?.id, clientContacts]);

  // Force fetch client settings when modal opens
  useEffect(() => {
    if (clientMember?.id) {
      fetchClientSettings();
    }
  }, [clientMember?.id, fetchClientSettings]);

  // Initial values from fetched client settings
  const initialValues = useMemo(() => {
    const stateSettings = fetchedClientSettings?.stateEstateSettings;

    const memberValues: any = {};
    const spouseValues: any = {};

    // Only include fields that are enabled for the member's state
    if (memberStateConfig.fields.stateTaxRate) {
      // Always use fetched tax rate from API, fallback to '0' if not available
      const memberTaxRate = memberStateData?.stateEstateTaxRate
        ? (memberStateData.stateEstateTaxRate * 100).toFixed(2) // Convert to percentage
        : '0';
      memberValues.stateTaxRate = memberTaxRate;
    }
    if (memberStateConfig.fields.stateGiftAndEstateTaxExemption) {
      // Always use fetched exemption from API, fallback to '0' if not available
      const memberExemption = memberStateData?.stateExclusionAmount
        ? memberStateData.stateExclusionAmount.toString()
        : '0';
      memberValues.stateGiftAndEstateTaxExemption = memberExemption;
    }
    if (memberStateConfig.fields.stateGiftAndEstateTaxExemptionUsed) {
      memberValues.stateGiftAndEstateTaxExemptionUsed =
        stateSettings?.stateGiftAndEstateTaxExemptionUsed?.member?.toString() ||
        '0';
    }
    if (memberStateConfig.fields.outOfStateProperty) {
      memberValues.outOfStateProperty =
        stateSettings?.outOfStateProperty?.member?.toString() || '0';
    }
    if (memberStateConfig.fields.adjustedGifts) {
      memberValues.adjustedGifts =
        stateSettings?.adjustedGifts?.member?.toString() || '0';
    }
    if (memberStateConfig.fields.stateEstateTaxPaidToOtherStates) {
      memberValues.stateEstateTaxPaidToOtherStates =
        stateSettings?.stateEstateTaxPaidToOtherStates?.member?.toString() ||
        '0';
    }
    if (memberStateConfig.fields.statePortabilityElection) {
      memberValues.statePortabilityElection =
        stateSettings?.statePortabilityElection?.member || false;
    }

    // Only include fields that are enabled for the spouse's state
    if (spouseStateConfig.fields.stateTaxRate) {
      // Always use fetched tax rate from API, fallback to '0' if not available
      const spouseTaxRate = spouseStateData?.stateEstateTaxRate
        ? (spouseStateData.stateEstateTaxRate * 100).toFixed(2) // Convert to percentage
        : '0';
      spouseValues.stateTaxRate = spouseTaxRate;
    }
    if (spouseStateConfig.fields.stateGiftAndEstateTaxExemption) {
      // Always use fetched exemption from API, fallback to '0' if not available
      const spouseExemption = spouseStateData?.stateExclusionAmount
        ? spouseStateData.stateExclusionAmount.toString()
        : '0';
      spouseValues.stateGiftAndEstateTaxExemption = spouseExemption;
    }
    if (spouseStateConfig.fields.stateGiftAndEstateTaxExemptionUsed) {
      spouseValues.stateGiftAndEstateTaxExemptionUsed =
        stateSettings?.stateGiftAndEstateTaxExemptionUsed?.spouse?.toString() ||
        '0';
    }
    if (spouseStateConfig.fields.outOfStateProperty) {
      spouseValues.outOfStateProperty =
        stateSettings?.outOfStateProperty?.spouse?.toString() || '0';
    }
    if (spouseStateConfig.fields.adjustedGifts) {
      spouseValues.adjustedGifts =
        stateSettings?.adjustedGifts?.spouse?.toString() || '0';
    }
    if (spouseStateConfig.fields.stateEstateTaxPaidToOtherStates) {
      spouseValues.stateEstateTaxPaidToOtherStates =
        stateSettings?.stateEstateTaxPaidToOtherStates?.spouse?.toString() ||
        '0';
    }
    if (spouseStateConfig.fields.statePortabilityElection) {
      spouseValues.statePortabilityElection =
        stateSettings?.statePortabilityElection?.spouse || false;
    }

    return {
      member: memberValues,
      spouse: spouseValues,
    };
  }, [
    fetchedClientSettings,
    memberStateConfig,
    spouseStateConfig,
    memberStateData,
    spouseStateData,
  ]);

  // Validation schema - dynamic based on state configuration
  const validationSchema = useMemo(() => {
    const memberSchema: any = {};
    const spouseSchema: any = {};

    // Only validate fields that are enabled for the member's state
    if (memberStateConfig.fields.stateTaxRate) {
      memberSchema.stateTaxRate = Yup.number()
        .min(0, t('clientSettings.stateEstateTax.stateTaxRateMin'))
        .max(100, t('clientSettings.stateEstateTax.stateTaxRateMax'))
        .required(t('clientSettings.stateEstateTax.stateTaxRateRequired'));
    }
    if (memberStateConfig.fields.stateGiftAndEstateTaxExemption) {
      memberSchema.stateGiftAndEstateTaxExemption = Yup.number()
        .min(
          0,
          t('clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionMin')
        )
        .required(
          t(
            'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionRequired'
          )
        );
    }
    if (memberStateConfig.fields.stateGiftAndEstateTaxExemptionUsed) {
      memberSchema.stateGiftAndEstateTaxExemptionUsed = Yup.number()
        .min(
          0,
          t(
            'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionUsedMin'
          )
        )
        .required(
          t(
            'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionUsedRequired'
          )
        );
    }
    if (memberStateConfig.fields.outOfStateProperty) {
      memberSchema.outOfStateProperty = Yup.number().min(
        0,
        t('clientSettings.stateEstateTax.outOfStatePropertyMin')
      );
    }
    if (memberStateConfig.fields.adjustedGifts) {
      memberSchema.adjustedGifts = Yup.number().min(
        0,
        t('clientSettings.stateEstateTax.adjustedGiftsMin')
      );
    }
    if (memberStateConfig.fields.stateEstateTaxPaidToOtherStates) {
      memberSchema.stateEstateTaxPaidToOtherStates = Yup.number().min(
        0,
        t('clientSettings.stateEstateTax.stateEstateTaxPaidToOtherStatesMin')
      );
    }

    // Only validate fields that are enabled for the spouse's state
    if (spouseStateConfig.fields.stateTaxRate) {
      spouseSchema.stateTaxRate = Yup.number()
        .min(0, t('clientSettings.stateEstateTax.stateTaxRateMin'))
        .max(100, t('clientSettings.stateEstateTax.stateTaxRateMax'))
        .required(t('clientSettings.stateEstateTax.stateTaxRateRequired'));
    }
    if (spouseStateConfig.fields.stateGiftAndEstateTaxExemption) {
      spouseSchema.stateGiftAndEstateTaxExemption = Yup.number()
        .min(
          0,
          t('clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionMin')
        )
        .required(
          t(
            'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionRequired'
          )
        );
    }
    if (spouseStateConfig.fields.stateGiftAndEstateTaxExemptionUsed) {
      spouseSchema.stateGiftAndEstateTaxExemptionUsed = Yup.number()
        .min(
          0,
          t(
            'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionUsedMin'
          )
        )
        .required(
          t(
            'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionUsedRequired'
          )
        );
    }
    if (spouseStateConfig.fields.outOfStateProperty) {
      spouseSchema.outOfStateProperty = Yup.number().min(
        0,
        t('clientSettings.stateEstateTax.outOfStatePropertyMin')
      );
    }
    if (spouseStateConfig.fields.adjustedGifts) {
      spouseSchema.adjustedGifts = Yup.number().min(
        0,
        t('clientSettings.stateEstateTax.adjustedGiftsMin')
      );
    }
    if (spouseStateConfig.fields.stateEstateTaxPaidToOtherStates) {
      spouseSchema.stateEstateTaxPaidToOtherStates = Yup.number().min(
        0,
        t('clientSettings.stateEstateTax.stateEstateTaxPaidToOtherStatesMin')
      );
    }

    return Yup.object({
      member: Yup.object(memberSchema),
      spouse: Yup.object(spouseSchema),
    });
  }, [t, memberStateConfig, spouseStateConfig]);

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: () => {}, // Empty function since we handle submit manually
    enableReinitialize: true,
    validateOnChange: false,
    validateOnBlur: false,
  });

  // Enhanced loading state
  const isLoading = useMemo(() => {
    return !clientMember || !contactCard || !fetchedClientSettings;
  }, [clientMember, contactCard, fetchedClientSettings]);

  // Use ref to access latest formik values
  const formikRef = useRef(formik);
  formikRef.current = formik;

  // Calculate out-of-state property for member
  const memberOutOfStateProperty = useMemo(() => {
    if (
      !clientMember?.id ||
      !memberStateCode ||
      !assets.length ||
      !contactCards.length
    ) {
      return 0;
    }
    return calculateOutOfStateProperty(
      assets,
      contactCards,
      clientMember.id,
      memberStateCode
    );
  }, [clientMember?.id, memberStateCode, assets, contactCards]);

  // Calculate out-of-state property for spouse
  const spouseOutOfStateProperty = useMemo(() => {
    if (
      !clientSettings?.spouse?.id ||
      !spouseStateCode ||
      !assets.length ||
      !contactCards.length
    ) {
      return 0;
    }
    return calculateOutOfStateProperty(
      assets,
      contactCards,
      clientSettings.spouse.id,
      spouseStateCode
    );
  }, [clientSettings?.spouse?.id, spouseStateCode, assets, contactCards]);

  // Update form values when fetched data changes
  useEffect(() => {
    if (fetchedClientSettings?.stateEstateSettings && !isLoading) {
      const stateSettings = fetchedClientSettings.stateEstateSettings;

      // Update form values with fetched data
      formik.setValues({
        member: {
          stateTaxRate: stateSettings.stateTaxRate?.member?.toString() || '0',
          stateGiftAndEstateTaxExemption:
            stateSettings.stateGiftAndEstateTaxExemption?.member?.toString() ||
            '0',
          stateGiftAndEstateTaxExemptionUsed:
            stateSettings.stateGiftAndEstateTaxExemptionUsed?.member?.toString() ||
            '0',
          outOfStateProperty:
            stateSettings.outOfStateProperty?.member?.toString() ||
            memberOutOfStateProperty.toString(),
          adjustedGifts: stateSettings.adjustedGifts?.member?.toString() || '0',
          stateEstateTaxPaidToOtherStates:
            stateSettings.stateEstateTaxPaidToOtherStates?.member?.toString() ||
            '0',
          statePortabilityElection:
            stateSettings.statePortabilityElection?.member || false,
        },
        spouse: {
          stateTaxRate: stateSettings.stateTaxRate?.spouse?.toString() || '0',
          stateGiftAndEstateTaxExemption:
            stateSettings.stateGiftAndEstateTaxExemption?.spouse?.toString() ||
            '0',
          stateGiftAndEstateTaxExemptionUsed:
            stateSettings.stateGiftAndEstateTaxExemptionUsed?.spouse?.toString() ||
            '0',
          outOfStateProperty:
            stateSettings.outOfStateProperty?.spouse?.toString() ||
            spouseOutOfStateProperty.toString(),
          adjustedGifts: stateSettings.adjustedGifts?.spouse?.toString() || '0',
          stateEstateTaxPaidToOtherStates:
            stateSettings.stateEstateTaxPaidToOtherStates?.spouse?.toString() ||
            '0',
          statePortabilityElection:
            stateSettings.statePortabilityElection?.spouse || false,
        },
      });
    }
  }, [
    fetchedClientSettings,
    isLoading,
    memberOutOfStateProperty,
    spouseOutOfStateProperty,
  ]);

  // Update member tax rates and exclusion amounts when member state tax data is fetched (only for initial load)
  useEffect(() => {
    if (memberStateData && !isLoading) {
      // Update member state tax rate if available and field is enabled
      if (
        memberStateData.stateEstateTaxRate &&
        memberStateConfig.fields.stateTaxRate
      ) {
        const taxRatePercentage = (
          memberStateData.stateEstateTaxRate * 100
        ).toFixed(2);
        formik.setFieldValue('member.stateTaxRate', taxRatePercentage);
      }

      // Update member state exclusion amount if available and field is enabled
      if (
        memberStateData.stateExclusionAmount &&
        memberStateConfig.fields.stateGiftAndEstateTaxExemption
      ) {
        formik.setFieldValue(
          'member.stateGiftAndEstateTaxExemption',
          memberStateData.stateExclusionAmount.toString()
        );
      }
    }
  }, [memberStateData, memberStateConfig, isLoading]);

  // Update spouse tax rates and exclusion amounts when spouse state tax data is fetched (only for initial load)
  useEffect(() => {
    if (spouseStateData && !isLoading) {
      // Update spouse state tax rate if available and field is enabled
      if (
        spouseStateData.stateEstateTaxRate &&
        spouseStateConfig.fields.stateTaxRate
      ) {
        const taxRatePercentage = (
          spouseStateData.stateEstateTaxRate * 100
        ).toFixed(2);
        formik.setFieldValue('spouse.stateTaxRate', taxRatePercentage);
      }

      // Update spouse state exclusion amount if available and field is enabled
      if (
        spouseStateData.stateExclusionAmount &&
        spouseStateConfig.fields.stateGiftAndEstateTaxExemption
      ) {
        formik.setFieldValue(
          'spouse.stateGiftAndEstateTaxExemption',
          spouseStateData.stateExclusionAmount.toString()
        );
      }
    }
  }, [spouseStateData, spouseStateConfig, isLoading]);

  // Handle form submit
  const handleSubmit = React.useCallback(async (): Promise<boolean> => {
    console.log('handleSubmit');
    const currentFormik = formikRef.current;

    // Run validation first
    const errors = await currentFormik.validateForm();

    if (Object.keys(errors).length > 0) {
      // Mark all fields as touched to show validation errors
      const touchedFields: any = {};
      const markFieldsTouched = (errors: any, prefix = '') => {
        Object.keys(errors).forEach((key) => {
          const fieldName = prefix ? `${prefix}.${key}` : key;
          if (typeof errors[key] === 'object' && errors[key] !== null) {
            if (prefix) {
              touchedFields[prefix] = true;
            }
            markFieldsTouched(errors[key], fieldName);
          } else {
            touchedFields[fieldName] = true;
            if (prefix) {
              touchedFields[prefix] = true;
            }
          }
        });
      };
      markFieldsTouched(errors);
      currentFormik.setTouched(touchedFields, true);
      return false;
    }

    try {
      // Capture the current form values
      const currentFormValues = currentFormik.values;

      // Prepare the state estate settings input - only include fields that are enabled
      const stateEstateSettingsInput: any = {
        stateCode: clientSettings?.primary?.stateOfResidence || '',
      };

      // Only include member fields that are enabled for the member's state
      if (memberStateConfig.fields.stateTaxRate) {
        if (!stateEstateSettingsInput.stateTaxRate)
          stateEstateSettingsInput.stateTaxRate = {};
        stateEstateSettingsInput.stateTaxRate.member = parseFloat(
          currentFormValues.member.stateTaxRate
        );
      }
      if (memberStateConfig.fields.stateGiftAndEstateTaxExemption) {
        if (!stateEstateSettingsInput.stateGiftAndEstateTaxExemption)
          stateEstateSettingsInput.stateGiftAndEstateTaxExemption = {};
        stateEstateSettingsInput.stateGiftAndEstateTaxExemption.member =
          parseFloat(currentFormValues.member.stateGiftAndEstateTaxExemption);
      }
      if (memberStateConfig.fields.stateGiftAndEstateTaxExemptionUsed) {
        if (!stateEstateSettingsInput.stateGiftAndEstateTaxExemptionUsed)
          stateEstateSettingsInput.stateGiftAndEstateTaxExemptionUsed = {};
        stateEstateSettingsInput.stateGiftAndEstateTaxExemptionUsed.member =
          parseFloat(
            currentFormValues.member.stateGiftAndEstateTaxExemptionUsed
          );
      }
      if (memberStateConfig.fields.outOfStateProperty) {
        if (!stateEstateSettingsInput.outOfStateProperty)
          stateEstateSettingsInput.outOfStateProperty = {};
        stateEstateSettingsInput.outOfStateProperty.member = parseFloat(
          currentFormValues.member.outOfStateProperty
        );
      }
      if (memberStateConfig.fields.adjustedGifts) {
        if (!stateEstateSettingsInput.adjustedGifts)
          stateEstateSettingsInput.adjustedGifts = {};
        stateEstateSettingsInput.adjustedGifts.member = parseFloat(
          currentFormValues.member.adjustedGifts
        );
      }
      if (memberStateConfig.fields.stateEstateTaxPaidToOtherStates) {
        if (!stateEstateSettingsInput.stateEstateTaxPaidToOtherStates)
          stateEstateSettingsInput.stateEstateTaxPaidToOtherStates = {};
        stateEstateSettingsInput.stateEstateTaxPaidToOtherStates.member =
          parseFloat(currentFormValues.member.stateEstateTaxPaidToOtherStates);
      }
      if (memberStateConfig.fields.statePortabilityElection) {
        if (!stateEstateSettingsInput.statePortabilityElection)
          stateEstateSettingsInput.statePortabilityElection = {};
        stateEstateSettingsInput.statePortabilityElection.member =
          currentFormValues.member.statePortabilityElection;
      }

      // Only include spouse fields that are enabled for the spouse's state
      if (spouseStateConfig.fields.stateTaxRate) {
        if (!stateEstateSettingsInput.stateTaxRate)
          stateEstateSettingsInput.stateTaxRate = {};
        stateEstateSettingsInput.stateTaxRate.spouse = parseFloat(
          currentFormValues.spouse.stateTaxRate
        );
      }
      if (spouseStateConfig.fields.stateGiftAndEstateTaxExemption) {
        if (!stateEstateSettingsInput.stateGiftAndEstateTaxExemption)
          stateEstateSettingsInput.stateGiftAndEstateTaxExemption = {};
        stateEstateSettingsInput.stateGiftAndEstateTaxExemption.spouse =
          parseFloat(currentFormValues.spouse.stateGiftAndEstateTaxExemption);
      }
      if (spouseStateConfig.fields.stateGiftAndEstateTaxExemptionUsed) {
        if (!stateEstateSettingsInput.stateGiftAndEstateTaxExemptionUsed)
          stateEstateSettingsInput.stateGiftAndEstateTaxExemptionUsed = {};
        stateEstateSettingsInput.stateGiftAndEstateTaxExemptionUsed.spouse =
          parseFloat(
            currentFormValues.spouse.stateGiftAndEstateTaxExemptionUsed
          );
      }
      if (spouseStateConfig.fields.outOfStateProperty) {
        if (!stateEstateSettingsInput.outOfStateProperty)
          stateEstateSettingsInput.outOfStateProperty = {};
        stateEstateSettingsInput.outOfStateProperty.spouse = parseFloat(
          currentFormValues.spouse.outOfStateProperty
        );
      }
      if (spouseStateConfig.fields.adjustedGifts) {
        if (!stateEstateSettingsInput.adjustedGifts)
          stateEstateSettingsInput.adjustedGifts = {};
        stateEstateSettingsInput.adjustedGifts.spouse = parseFloat(
          currentFormValues.spouse.adjustedGifts
        );
      }
      if (spouseStateConfig.fields.stateEstateTaxPaidToOtherStates) {
        if (!stateEstateSettingsInput.stateEstateTaxPaidToOtherStates)
          stateEstateSettingsInput.stateEstateTaxPaidToOtherStates = {};
        stateEstateSettingsInput.stateEstateTaxPaidToOtherStates.spouse =
          parseFloat(currentFormValues.spouse.stateEstateTaxPaidToOtherStates);
      }
      if (spouseStateConfig.fields.statePortabilityElection) {
        if (!stateEstateSettingsInput.statePortabilityElection)
          stateEstateSettingsInput.statePortabilityElection = {};
        stateEstateSettingsInput.statePortabilityElection.spouse =
          currentFormValues.spouse.statePortabilityElection;
      }

      // Update client settings atom with form values
      setClientSettings({
        primary: clientSettings?.primary || { id: '' },
        spouse: clientSettings?.spouse,
        federalEstateSettings: clientSettings?.federalEstateSettings,
        stateEstateSettings: stateEstateSettingsInput,
      });

      await saveStateEstateSettings(
        stateEstateSettingsInput,
        clientMember?.id || undefined
      );

      // Sync client settings to EstateFlow
      if (clientMember?.id && saveEstateFlowConfiguration) {
        await syncClientSettingsToEstateFlow(
          saveEstateFlowConfiguration,
          undefined, // No federal settings for state
          stateEstateSettingsInput,
          undefined, // No basic info for state
          clientMember.id
        );
      }

      return true;
    } catch (error) {
      logger.error('Failed to update state estate tax settings:', error);
      return false;
    }
  }, [
    clientSettings,
    setClientSettings,
    clientMember?.id,
    memberStateConfig,
    spouseStateConfig,
    saveEstateFlowConfiguration,
  ]);

  // Register the form submit function with the modal
  useEffect(() => {
    registerSubmitter(ClientSettingsTab.StateEstateTax, handleSubmit);
  }, [handleSubmit, registerSubmitter]);

  // Reset handlers for each section - only reset fields that are enabled
  const handleResetMember = () => {
    if (memberStateConfig.fields.stateGiftAndEstateTaxExemptionUsed) {
      formik.setFieldValue('member.stateGiftAndEstateTaxExemptionUsed', '0');
    }
    if (memberStateConfig.fields.outOfStateProperty) {
      formik.setFieldValue('member.outOfStateProperty', '0');
    }
    if (memberStateConfig.fields.adjustedGifts) {
      formik.setFieldValue('member.adjustedGifts', '0');
    }
    if (memberStateConfig.fields.stateEstateTaxPaidToOtherStates) {
      formik.setFieldValue('member.stateEstateTaxPaidToOtherStates', '0');
    }
    if (memberStateConfig.fields.statePortabilityElection) {
      formik.setFieldValue('member.statePortabilityElection', false);
    }
  };

  const handleResetSpouse = () => {
    if (spouseStateConfig.fields.stateGiftAndEstateTaxExemptionUsed) {
      formik.setFieldValue('spouse.stateGiftAndEstateTaxExemptionUsed', '0');
    }
    if (spouseStateConfig.fields.outOfStateProperty) {
      formik.setFieldValue('spouse.outOfStateProperty', '0');
    }
    if (spouseStateConfig.fields.adjustedGifts) {
      formik.setFieldValue('spouse.adjustedGifts', '0');
    }
    if (spouseStateConfig.fields.stateEstateTaxPaidToOtherStates) {
      formik.setFieldValue('spouse.stateEstateTaxPaidToOtherStates', '0');
    }
    if (spouseStateConfig.fields.statePortabilityElection) {
      formik.setFieldValue('spouse.statePortabilityElection', false);
    }
  };

  // Show skeleton while loading
  if (isLoading) {
    return <StateEstateTaxSkeleton />;
  }

  return (
    <FormikProvider value={formik}>
      <Grid container justifyContent="flex-start" gap={2}>
        {/* State Estate Tax Assumptions Header */}
        {(memberStateConfig.hasStateEstateTax ||
          spouseStateConfig.hasStateEstateTax) && (
          <Grid item xs={12}>
            <Card sx={{ width: '100%', maxWidth: 720 }}>
              <CardContent>
                <Typography variant="h6">
                  State Estate Tax Assumptions
                </Typography>
                <Typography variant="body3" color={wealthPalette.text.subtle}>
                  Adjust state tax rate, exemptions and related details
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Member Card - Only show if member's state has estate tax */}
        {memberStateConfig.hasStateEstateTax && (
          <StateEstateCard
            title={`${clientMemberName}`}
            icon={
              <FontAwesomeIcon
                icon={faCircleUser}
                color={wealthPalette.icon.soft}
              />
            }
            description={''}
            onReset={handleResetMember}
          >
            {memberStateConfig.fields.stateTaxRate && (
              <FieldWrapper
                formik={formik}
                name="member.stateTaxRate"
                label={t('clientSettings.stateEstateTax.stateTaxRate')}
                required
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="member.stateTaxRate"
                  label=""
                  type="number"
                  dataTestId="member_state_tax_rate"
                  hideLabel
                  suffix="%"
                  readOnly
                />
              </FieldWrapper>
            )}

            {memberStateConfig.fields.stateGiftAndEstateTaxExemption && (
              <FieldWrapper
                formik={formik}
                name="member.stateGiftAndEstateTaxExemption"
                label={t(
                  'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemption'
                )}
                required
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="member.stateGiftAndEstateTaxExemption"
                  label=""
                  type="number"
                  dataTestId="member_state_gift_and_estate_tax_exemption"
                  hideLabel
                  isCurrency
                  suffix="USD"
                  readOnly
                />
              </FieldWrapper>
            )}

            {memberStateConfig.fields.stateGiftAndEstateTaxExemptionUsed && (
              <FieldWrapper
                formik={formik}
                name="member.stateGiftAndEstateTaxExemptionUsed"
                label={t(
                  'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionUsed'
                )}
                required
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="member.stateGiftAndEstateTaxExemptionUsed"
                  label=""
                  type="number"
                  dataTestId="member_state_gift_and_estate_tax_exemption_used"
                  hideLabel
                  isCurrency
                  suffix="USD"
                />
              </FieldWrapper>
            )}

            {memberStateConfig.fields.outOfStateProperty && (
              <FieldWrapper
                formik={formik}
                name="member.outOfStateProperty"
                label={t('clientSettings.stateEstateTax.outOfStateProperty')}
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="member.outOfStateProperty"
                  label=""
                  type="number"
                  dataTestId="member_out_of_state_property"
                  hideLabel
                  isCurrency
                  suffix="USD"
                />
              </FieldWrapper>
            )}

            {memberStateConfig.fields.adjustedGifts && (
              <FieldWrapper
                formik={formik}
                name="member.adjustedGifts"
                label={t('clientSettings.stateEstateTax.adjustedGifts')}
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="member.adjustedGifts"
                  label=""
                  type="number"
                  dataTestId="member_adjusted_gifts"
                  hideLabel
                  isCurrency
                  suffix="USD"
                />
              </FieldWrapper>
            )}

            {memberStateConfig.fields.stateEstateTaxPaidToOtherStates && (
              <FieldWrapper
                formik={formik}
                name="member.stateEstateTaxPaidToOtherStates"
                label={t(
                  'clientSettings.stateEstateTax.stateEstateTaxPaidToOtherStates'
                )}
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="member.stateEstateTaxPaidToOtherStates"
                  label=""
                  type="number"
                  dataTestId="member_state_estate_tax_paid_to_other_states"
                  hideLabel
                  isCurrency
                  suffix="USD"
                />
              </FieldWrapper>
            )}

            {memberStateConfig.fields.statePortabilityElection && (
              <Grid item xs={12}>
                <Box
                  sx={{
                    border: `1px solid ${wealthPalette.primary.light}`,
                    borderRadius: 1,
                    p: 1,
                    backgroundColor: formik.values.member
                      .statePortabilityElection
                      ? '#E7FFFD'
                      : undefined,
                    borderColor: formik.values.member.statePortabilityElection
                      ? '#5CB1AB'
                      : wealthPalette.primary.light,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  <Checkbox
                    checked={formik.values.member.statePortabilityElection}
                    onChange={formik.handleChange}
                    name="member.statePortabilityElection"
                    size="small"
                    sx={{
                      color: wealthPalette.primary.main,
                      '&.Mui-checked': {
                        color: wealthPalette.state.success.base,
                      },
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{
                      flex: 1,
                      color: wealthPalette.text.primary,
                      fontWeight: 500,
                    }}
                  >
                    {t(
                      'clientSettings.federalEstateTax.electPortabilityGiftEstateExemption'
                    )}
                  </Typography>
                  {/* <FontAwesomeIcon
                    icon={faCircleInfo}
                    color={wealthPalette.text.subtle}
                    style={{ fontSize: 16 }}
                  /> */}
                </Box>
              </Grid>
            )}
          </StateEstateCard>
        )}

        {/* Spouse Card - Only show if there's a spouse and spouse's state has estate tax */}
        {clientSettings?.spouse && spouseStateConfig.hasStateEstateTax && (
          <StateEstateCard
            title={`${getSpouseName}`}
            icon={
              <FontAwesomeIcon
                icon={faCircleUser}
                color={wealthPalette.icon.soft}
              />
            }
            description={''}
            onReset={handleResetSpouse}
          >
            {spouseStateConfig.fields.stateTaxRate && (
              <FieldWrapper
                formik={formik}
                name="spouse.stateTaxRate"
                label={t('clientSettings.stateEstateTax.stateTaxRate')}
                required
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="spouse.stateTaxRate"
                  label=""
                  type="number"
                  dataTestId="spouse_state_tax_rate"
                  hideLabel
                  suffix="%"
                  readOnly
                />
              </FieldWrapper>
            )}

            {spouseStateConfig.fields.stateGiftAndEstateTaxExemption && (
              <FieldWrapper
                formik={formik}
                name="spouse.stateGiftAndEstateTaxExemption"
                label={t(
                  'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemption'
                )}
                required
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="spouse.stateGiftAndEstateTaxExemption"
                  label=""
                  type="number"
                  dataTestId="spouse_state_gift_and_estate_tax_exemption"
                  hideLabel
                  isCurrency
                  suffix="USD"
                  readOnly
                />
              </FieldWrapper>
            )}

            {spouseStateConfig.fields.stateGiftAndEstateTaxExemptionUsed && (
              <FieldWrapper
                formik={formik}
                name="spouse.stateGiftAndEstateTaxExemptionUsed"
                label={t(
                  'clientSettings.stateEstateTax.stateGiftAndEstateTaxExemptionUsed'
                )}
                required
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="spouse.stateGiftAndEstateTaxExemptionUsed"
                  label=""
                  type="number"
                  dataTestId="spouse_state_gift_and_estate_tax_exemption_used"
                  hideLabel
                  isCurrency
                />
              </FieldWrapper>
            )}

            {spouseStateConfig.fields.outOfStateProperty && (
              <FieldWrapper
                formik={formik}
                name="spouse.outOfStateProperty"
                label={t('clientSettings.stateEstateTax.outOfStateProperty')}
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="spouse.outOfStateProperty"
                  label=""
                  type="number"
                  dataTestId="spouse_out_of_state_property"
                  hideLabel
                  isCurrency
                />
              </FieldWrapper>
            )}

            {spouseStateConfig.fields.adjustedGifts && (
              <FieldWrapper
                formik={formik}
                name="spouse.adjustedGifts"
                label={t('clientSettings.stateEstateTax.adjustedGifts')}
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="spouse.adjustedGifts"
                  label=""
                  type="number"
                  dataTestId="spouse_adjusted_gifts"
                  hideLabel
                  isCurrency
                />
              </FieldWrapper>
            )}

            {spouseStateConfig.fields.stateEstateTaxPaidToOtherStates && (
              <FieldWrapper
                formik={formik}
                name="spouse.stateEstateTaxPaidToOtherStates"
                label={t(
                  'clientSettings.stateEstateTax.stateEstateTaxPaidToOtherStates'
                )}
                containerSize={6}
              >
                <FormField
                  formik={formik}
                  name="spouse.stateEstateTaxPaidToOtherStates"
                  label=""
                  type="number"
                  dataTestId="spouse_state_estate_tax_paid_to_other_states"
                  hideLabel
                  isCurrency
                />
              </FieldWrapper>
            )}

            {spouseStateConfig.fields.statePortabilityElection && (
              <Grid item xs={12}>
                <Box
                  sx={{
                    border: `1px solid ${wealthPalette.primary.light}`,
                    borderRadius: 1,
                    p: 1,
                    backgroundColor: formik.values.spouse
                      .statePortabilityElection
                      ? '#E7FFFD'
                      : undefined,
                    borderColor: formik.values.spouse.statePortabilityElection
                      ? '#5CB1AB'
                      : wealthPalette.primary.light,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  <Checkbox
                    checked={formik.values.spouse.statePortabilityElection}
                    onChange={formik.handleChange}
                    name="spouse.statePortabilityElection"
                    size="small"
                    sx={{
                      color: wealthPalette.primary.main,
                      '&.Mui-checked': {
                        color: wealthPalette.state.success.base,
                      },
                    }}
                  />
                  <Typography
                    variant="body2"
                    sx={{
                      flex: 1,
                      color: wealthPalette.text.primary,
                      fontWeight: 500,
                    }}
                  >
                    {t(
                      'clientSettings.stateEstateTax.statePortabilityElection'
                    )}
                  </Typography>
                </Box>
              </Grid>
            )}
          </StateEstateCard>
        )}
      </Grid>
    </FormikProvider>
  );
}
