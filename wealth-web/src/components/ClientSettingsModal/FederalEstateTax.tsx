import React, { useMemo, useEffect, useRef } from 'react';
import { useAtom, useAtomValue } from 'jotai';
import { useTranslation } from 'react-i18next';
import { useFormik, FormikProvider } from 'formik';
import * as Yup from 'yup';
import { clientSettingsDataAtom } from 'atomStore/clientSettings';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/GridLegacy';
import TextField from '@mui/material/TextField';
import Typography from 'components/@wealth/atoms/Typography/Typography';
import { wealthPalette } from 'theme/wealth-default-theme';
import { clientUserAtom } from 'atomStore/client/client';
import { formatContactName } from 'utils/formatContact';
import { get } from 'utils/objectHelpers';
import ErrorMessage from 'components/_common/ErrorMessage';
import { ClientSettingsTab } from './types';
import Skeleton from '@mui/material/Skeleton';
import { logger } from 'utils/logger';
import useGetClientSettings from 'hooks/client-settings/useGetClientSettings';
import saveFederalEstateSettings from 'hooks/client-settings/saveFederalEstateSettings';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleUser } from '@fortawesome/pro-regular-svg-icons';
import Checkbox from '@mui/material/Checkbox';
import WealthButton from 'components/@wealth/atoms/Button/Button';
import { faRotate } from '@fortawesome/pro-regular-svg-icons';
import FormNumberInput from 'components/_vault/_form/FormNumberInput';
import { MAXIMUM_EXEMPTION } from 'components/@wealth/organisms/ScenarioAnalysis/consts';
import useAdminCostsCalculation from 'hooks/client-settings/useAdminCostsCalculation';
import useUserContacts from 'hooks/useUserContacts';
import useEstateFlowAD from 'hooks/useEstateFlowAD';
import { syncClientSettingsToEstateFlow } from 'hooks/client-settings/syncToEstateFlow';

// Reusable form field component
const FormField = ({
  formik,
  name,
  label,
  type = 'text',
  placeholder,
  onChange,
  dataTestId,
  hideLabel = false,
  suffix = '',
  prefix = '',
  isCurrency = false,
  readOnly = false,
}: {
  formik: any;
  name: string;
  label: string;
  type?: 'text' | 'number';
  placeholder?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  dataTestId?: string;
  hideLabel?: boolean;
  suffix?: string;
  prefix?: string;
  isCurrency?: boolean;
  readOnly?: boolean;
}) => {
  const error = get(formik.errors, name);
  const touched = get(formik.touched, name);
  const showError = error && touched;

  if (isCurrency) {
    return (
      <FormNumberInput
        currency
        name={name}
        label={hideLabel ? '' : label}
        placeholder={placeholder}
        value={get(formik.values, name) || ''}
        onChange={onChange || formik.handleChange}
        error={!!showError}
        data-testid={dataTestId}
        labelId={name}
        disabled={readOnly}
        InputProps={{
          startAdornment: prefix ? (
            <Typography variant="body2" color={wealthPalette.text.subtle}>
              {prefix}
            </Typography>
          ) : undefined,
          endAdornment: suffix ? (
            <Typography variant="body2" color={wealthPalette.text.subtle}>
              {suffix || 'USD'}
            </Typography>
          ) : undefined,
        }}
      />
    );
  }

  return (
    <TextField
      fullWidth
      label={hideLabel ? '' : label}
      name={name}
      type={type}
      placeholder={placeholder}
      value={get(formik.values, name) || ''}
      onChange={onChange || formik.handleChange}
      error={!!showError}
      InputLabelProps={{ shrink: true }}
      data-testid={dataTestId}
      disabled={readOnly}
      InputProps={{
        startAdornment: prefix ? (
          <Typography variant="body2" color={wealthPalette.text.subtle}>
            {prefix}
          </Typography>
        ) : undefined,
        endAdornment: suffix ? (
          <Typography variant="body2" color={wealthPalette.text.subtle}>
            {suffix}
          </Typography>
        ) : undefined,
      }}
    />
  );
};

// Reusable field wrapper with label and error handling
const FieldWrapper = ({
  formik,
  name,
  label,
  children,
  required = false,
  containerSize = 12,
}: {
  formik: any;
  name: string;
  label: string;
  children: React.ReactNode;
  required?: boolean;
  containerSize?: number;
}) => {
  const error = get(formik.errors, name);
  const touched = get(formik.touched, name);
  const showError = error && touched;

  return (
    <Grid item xs={containerSize}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        <Typography variant="subtitle2">
          {label}
          {required && '*'}
        </Typography>
      </Box>
      {children}
      {showError && <ErrorMessage message={error} />}
    </Grid>
  );
};

// Reusable card component for federal estate tax sections
const FederalEstateCard = ({
  icon,
  title,
  description,
  children,
  onReset,
  onSave,
}: {
  icon?: React.ReactNode;
  title: string;
  description: string;
  children: React.ReactNode;
  onReset?: () => void;
  onSave?: () => void;
}) => (
  <Grid component={Card} sx={{ width: '100%', maxWidth: 720 }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        {icon && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>{icon}</Box>
        )}
        <Typography variant="h6">{title}</Typography>
      </Box>
      <Typography variant="body3" color={wealthPalette.text.subtle}>
        {description}
      </Typography>
      <Grid container spacing={2} mt={1}>
        {children}
      </Grid>
      {(onReset || onSave) && (
        <Box
          sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}
        >
          {onReset && (
            <WealthButton
              variant="outlined"
              startIcon={<FontAwesomeIcon icon={faRotate} />}
              onClick={onReset}
            >
              Reset Assumptions
            </WealthButton>
          )}
          {onSave && (
            <WealthButton variant="contained" onClick={onSave}>
              Save
            </WealthButton>
          )}
        </Box>
      )}
    </CardContent>
  </Grid>
);

// Skeleton component for loading state
const FederalEstateTaxSkeleton = () => {
  return (
    <Grid container justifyContent="flex-start" gap={2}>
      <Grid component={Card} sx={{ width: '100%', maxWidth: 720 }}>
        <CardContent>
          <Skeleton variant="text" width="60%" height={32} />
          <Skeleton variant="text" width="80%" height={20} sx={{ mt: 1 }} />
          <Grid container spacing={2} mt={1}>
            <Grid item xs={6}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={6}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={12}>
              <Skeleton variant="text" width="40%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ mt: 1 }}
              />
            </Grid>
            <Grid item xs={12}>
              <Skeleton variant="text" width="60%" height={20} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={24}
                sx={{ mt: 1 }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Grid>
    </Grid>
  );
};

export default function FederalEstateTax({
  registerSubmitter,
}: {
  registerSubmitter: (
    tab: ClientSettingsTab,
    submitFn: () => Promise<boolean>
  ) => void;
}) {
  const { t } = useTranslation();
  const [clientSettings, setClientSettings] = useAtom(clientSettingsDataAtom);
  const clientMember = useAtomValue(clientUserAtom);
  const contactCard = clientMember?.contactCard;
  const clientMemberName = contactCard ? formatContactName(contactCard) : '';

  // Get EstateFlow AD hook for syncing
  const { saveEstateFlowConfiguration } = useEstateFlowAD(clientMember?.id);

  // Get gross estate values from clientSettings atom
  const memberGrossEstate =
    clientSettings?.federalEstateSettings?.memberGrossEstate || 0;
  const spouseGrossEstate =
    clientSettings?.federalEstateSettings?.spouseGrossEstate || 0;
  const totalGrossEstate =
    clientSettings?.federalEstateSettings?.totalGrossEstate || 0;

  const { contactCards: clientContacts } = useUserContacts(
    clientMember?.id || undefined
  );

  const { clientSettings: fetchedClientSettings, fetchClientSettings } =
    useGetClientSettings(clientMember?.id ?? undefined);

  const initialValues = useMemo(() => {
    const federalSettings = fetchedClientSettings?.federalEstateSettings;
    return {
      federalTaxRate: federalSettings?.federalTaxRate?.toString() || '40',
      estimatedAdminCosts:
        federalSettings?.estimatedAdminCosts?.toString() || '1',
      federalGiftAndEstateTaxExemption:
        federalSettings?.federalGiftAndEstateTaxExemption?.toString() ||
        MAXIMUM_EXEMPTION,
      electPortabilityGiftEstateExemption:
        federalSettings?.electPortabilityGiftEstateExemption || false,
      member: {
        federalGiftAndEstateTaxExemptionUsed:
          federalSettings?.federalGiftAndEstateTaxExemptionUsed?.member?.toString() ||
          '0',
        addAvailableExemptionFromPredeceasedSpouse:
          federalSettings?.addAvailableExemptionFromPredeceasedSpouse?.member ||
          false,
        availableDeceasedSpouseUnusedExclusion:
          federalSettings?.availableDeceasedSpouseUnusedExclusion?.member?.toString() ||
          '0',
      },
      spouse: {
        federalGiftAndEstateTaxExemptionUsed:
          federalSettings?.federalGiftAndEstateTaxExemptionUsed?.spouse?.toString() ||
          '0',
        addAvailableExemptionFromPredeceasedSpouse:
          federalSettings?.addAvailableExemptionFromPredeceasedSpouse?.spouse ||
          false,
        availableDeceasedSpouseUnusedExclusion:
          federalSettings?.availableDeceasedSpouseUnusedExclusion?.spouse?.toString() ||
          '0',
      },
      gst: {
        federalGstTaxExemption:
          federalSettings?.federalGstTaxExemption?.toString() ||
          MAXIMUM_EXEMPTION,
        memberGstExemptionUsed:
          federalSettings?.gstExemptionUsedAtFederal?.member?.toString() || '0',
        spouseGstExemptionUsed:
          federalSettings?.gstExemptionUsedAtFederal?.spouse?.toString() || '0',
      },
    };
  }, [fetchedClientSettings]);

  const validationSchema = useMemo(
    () =>
      Yup.object({
        federalTaxRate: Yup.number()
          .min(0, t('clientSettings.federalEstateTax.federalTaxRateMin'))
          .max(100, t('clientSettings.federalEstateTax.federalTaxRateMax'))
          .required(
            t('clientSettings.federalEstateTax.federalTaxRateRequired')
          ),
        estimatedAdminCosts: Yup.number()
          .min(0, t('clientSettings.federalEstateTax.estimatedAdminCostsMin'))
          .max(100, t('clientSettings.federalEstateTax.estimatedAdminCostsMax'))
          .required(
            t('clientSettings.federalEstateTax.estimatedAdminCostsRequired')
          ),
        federalGiftAndEstateTaxExemption: Yup.number()
          .min(
            0,
            t(
              'clientSettings.federalEstateTax.federalGiftAndEstateTaxExemptionMin'
            )
          )
          .required(
            t(
              'clientSettings.federalEstateTax.federalGiftAndEstateTaxExemptionRequired'
            )
          ),
        member: Yup.object({
          federalGiftAndEstateTaxExemptionUsed: Yup.number()
            .min(
              0,
              t(
                'clientSettings.federalEstateTax.federalGiftAndEstateTaxExemptionUsedMin'
              )
            )
            .required(
              t(
                'clientSettings.federalEstateTax.federalGiftAndEstateTaxExemptionUsedRequired'
              )
            ),
          availableDeceasedSpouseUnusedExclusion: Yup.number().min(
            0,
            t(
              'clientSettings.federalEstateTax.availableDeceasedSpouseUnusedExclusionMin'
            )
          ),
        }),
        spouse: Yup.object({
          federalGiftAndEstateTaxExemptionUsed: Yup.number()
            .min(
              0,
              t(
                'clientSettings.federalEstateTax.federalGiftAndEstateTaxExemptionUsedMin'
              )
            )
            .required(
              t(
                'clientSettings.federalEstateTax.federalGiftAndEstateTaxExemptionUsedRequired'
              )
            ),
          availableDeceasedSpouseUnusedExclusion: Yup.number().min(
            0,
            t(
              'clientSettings.federalEstateTax.availableDeceasedSpouseUnusedExclusionMin'
            )
          ),
        }),
        gst: Yup.object({
          federalGstTaxExemption: Yup.number()
            .min(
              0,
              t('clientSettings.federalEstateTax.federalGstTaxExemptionMin')
            )
            .required(
              t(
                'clientSettings.federalEstateTax.federalGstTaxExemptionRequired'
              )
            ),
          memberGstExemptionUsed: Yup.number().min(
            0,
            t('clientSettings.federalEstateTax.gstExemptionUsedMin')
          ),
          spouseGstExemptionUsed: Yup.number().min(
            0,
            t('clientSettings.federalEstateTax.gstExemptionUsedMin')
          ),
        }),
      }),
    [t]
  );

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: () => {},
    enableReinitialize: true,
    validateOnChange: false,
    validateOnBlur: false,
  });

  // Calculate admin costs for member and spouse
  const estimatedAdminCostsPercentage = parseFloat(
    formik.values.estimatedAdminCosts || '0.5'
  );
  const { memberAdminCosts, spouseAdminCosts } = useAdminCostsCalculation(
    estimatedAdminCostsPercentage
  );

  // Store gross estate values and admin costs in the atom when they change
  useEffect(() => {
    if (memberGrossEstate > 0 || spouseGrossEstate > 0) {
      setClientSettings((prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          federalEstateSettings: {
            ...prev.federalEstateSettings,
            federalTaxRate: prev.federalEstateSettings?.federalTaxRate || 0,
            estimatedAdminCosts: parseFloat(
              formik.values.estimatedAdminCosts || '1'
            ),
            estimatedAdminCostsDollarValue: {
              member: memberAdminCosts,
              spouse: spouseAdminCosts,
            },
            federalGiftAndEstateTaxExemption:
              prev.federalEstateSettings?.federalGiftAndEstateTaxExemption || 0,
            electPortabilityGiftEstateExemption:
              prev.federalEstateSettings?.electPortabilityGiftEstateExemption ||
              false,
            federalGiftAndEstateTaxExemptionUsed: prev.federalEstateSettings
              ?.federalGiftAndEstateTaxExemptionUsed || {
              member: 0,
              spouse: 0,
            },
            addAvailableExemptionFromPredeceasedSpouse: prev
              .federalEstateSettings
              ?.addAvailableExemptionFromPredeceasedSpouse || {
              member: false,
              spouse: false,
            },
            availableDeceasedSpouseUnusedExclusion: prev.federalEstateSettings
              ?.availableDeceasedSpouseUnusedExclusion || {
              member: 0,
              spouse: 0,
            },
            federalGstTaxExemption:
              prev.federalEstateSettings?.federalGstTaxExemption || 0,
            gstExemptionUsedAtFederal: prev.federalEstateSettings
              ?.gstExemptionUsedAtFederal || { member: 0, spouse: 0 },
            memberGrossEstate,
            spouseGrossEstate,
          },
        };
      });
    }
  }, [
    memberGrossEstate,
    spouseGrossEstate,
    totalGrossEstate,
    memberAdminCosts,
    spouseAdminCosts,
    setClientSettings,
    formik.values.estimatedAdminCosts,
  ]);

  const getSpouseName = useMemo(() => {
    if (!clientSettings?.spouse?.id || !clientContacts.length) {
      return 'Test Contact';
    }

    const spouseContact = clientContacts.find(
      (contact) => contact.id === clientSettings.spouse?.id
    );

    return spouseContact ? formatContactName(spouseContact) : 'Test Contact';
  }, [clientSettings?.spouse?.id, clientContacts]);

  useEffect(() => {
    if (clientMember?.id) {
      fetchClientSettings();
    }
  }, [clientMember?.id, fetchClientSettings]);

  const isLoading = useMemo(() => {
    return !clientMember || !contactCard || !fetchedClientSettings;
  }, [clientMember, contactCard, fetchedClientSettings]);

  const formikRef = useRef(formik);
  formikRef.current = formik;

  useEffect(() => {
    if (fetchedClientSettings?.federalEstateSettings && !isLoading) {
      const federalSettings = fetchedClientSettings.federalEstateSettings;

      formik.setValues({
        federalTaxRate: federalSettings.federalTaxRate?.toString() || '40',
        estimatedAdminCosts:
          federalSettings.estimatedAdminCosts?.toString() || '1',
        federalGiftAndEstateTaxExemption:
          federalSettings.federalGiftAndEstateTaxExemption?.toString() ||
          MAXIMUM_EXEMPTION,
        electPortabilityGiftEstateExemption:
          federalSettings.electPortabilityGiftEstateExemption || false,
        member: {
          federalGiftAndEstateTaxExemptionUsed:
            federalSettings.federalGiftAndEstateTaxExemptionUsed?.member?.toString() ||
            '1000000',
          addAvailableExemptionFromPredeceasedSpouse:
            federalSettings.addAvailableExemptionFromPredeceasedSpouse
              ?.member || false,
          availableDeceasedSpouseUnusedExclusion:
            federalSettings.availableDeceasedSpouseUnusedExclusion?.member?.toString() ||
            '0',
        },
        spouse: {
          federalGiftAndEstateTaxExemptionUsed:
            federalSettings.federalGiftAndEstateTaxExemptionUsed?.spouse?.toString() ||
            '1000000',
          addAvailableExemptionFromPredeceasedSpouse:
            federalSettings.addAvailableExemptionFromPredeceasedSpouse
              ?.spouse || false,
          availableDeceasedSpouseUnusedExclusion:
            federalSettings.availableDeceasedSpouseUnusedExclusion?.spouse?.toString() ||
            '0',
        },
        gst: {
          federalGstTaxExemption:
            federalSettings.federalGstTaxExemption?.toString() ||
            MAXIMUM_EXEMPTION,
          memberGstExemptionUsed:
            federalSettings.gstExemptionUsedAtFederal?.member?.toString() ||
            '1000000',
          spouseGstExemptionUsed:
            federalSettings.gstExemptionUsedAtFederal?.spouse?.toString() ||
            '1000000',
        },
      });
    }
  }, [fetchedClientSettings, isLoading]);

  const handleSubmit = React.useCallback(async (): Promise<boolean> => {
    const currentFormik = formikRef.current;
    const errors = await currentFormik.validateForm();

    if (Object.keys(errors).length > 0) {
      const touchedFields: any = {};
      const markFieldsTouched = (errors: any, prefix = '') => {
        Object.keys(errors).forEach((key) => {
          const fieldName = prefix ? `${prefix}.${key}` : key;
          if (typeof errors[key] === 'object' && errors[key] !== null) {
            if (prefix) {
              touchedFields[prefix] = true;
            }
            markFieldsTouched(errors[key], fieldName);
          } else {
            touchedFields[fieldName] = true;
            if (prefix) {
              touchedFields[prefix] = true;
            }
          }
        });
      };
      markFieldsTouched(errors);
      currentFormik.setTouched(touchedFields, true);
      return false;
    }

    try {
      const currentFormValues = currentFormik.values;

      const federalEstateSettingsInput = {
        federalTaxRate: parseFloat(currentFormValues.federalTaxRate),
        estimatedAdminCosts: parseFloat(currentFormValues.estimatedAdminCosts),
        federalGiftAndEstateTaxExemption: parseFloat(
          currentFormValues.federalGiftAndEstateTaxExemption
        ),
        electPortabilityGiftEstateExemption:
          currentFormValues.electPortabilityGiftEstateExemption,
        federalGiftAndEstateTaxExemptionUsed: {
          member: parseFloat(
            currentFormValues.member.federalGiftAndEstateTaxExemptionUsed
          ),
          spouse: parseFloat(
            currentFormValues.spouse.federalGiftAndEstateTaxExemptionUsed
          ),
        },
        addAvailableExemptionFromPredeceasedSpouse: {
          member:
            currentFormValues.member.addAvailableExemptionFromPredeceasedSpouse,
          spouse:
            currentFormValues.spouse.addAvailableExemptionFromPredeceasedSpouse,
        },
        availableDeceasedSpouseUnusedExclusion: {
          member: parseFloat(
            currentFormValues.member.availableDeceasedSpouseUnusedExclusion
          ),
          spouse: parseFloat(
            currentFormValues.spouse.availableDeceasedSpouseUnusedExclusion
          ),
        },
        federalGstTaxExemption: parseFloat(
          currentFormValues.gst.federalGstTaxExemption
        ),
        gstExemptionUsedAtFederal: {
          member: parseFloat(currentFormValues.gst.memberGstExemptionUsed),
          spouse: parseFloat(currentFormValues.gst.spouseGstExemptionUsed),
        },
      };

      const result = await saveFederalEstateSettings(
        federalEstateSettingsInput,
        clientMember?.id || undefined
      );

      if (result.success) {
        setClientSettings((prev) => {
          if (!prev) {
            return {
              primary: { id: clientMember?.id || '' },
              federalEstateSettings: {
                ...federalEstateSettingsInput,
                estimatedAdminCostsDollarValue: {
                  member: memberAdminCosts,
                  spouse: spouseAdminCosts,
                },
                memberGrossEstate: memberGrossEstate,
                spouseGrossEstate: spouseGrossEstate,
              },
            };
          }

          return {
            primary: prev.primary,
            spouse: prev.spouse,
            federalEstateSettings: {
              ...prev.federalEstateSettings,
              ...federalEstateSettingsInput,
              estimatedAdminCostsDollarValue: {
                member: memberAdminCosts,
                spouse: spouseAdminCosts,
              },
              memberGrossEstate: memberGrossEstate,
              spouseGrossEstate: spouseGrossEstate,
            },
            stateEstateSettings: prev.stateEstateSettings,
          };
        });

        // Sync client settings to EstateFlow
        if (clientMember?.id && saveEstateFlowConfiguration) {
          await syncClientSettingsToEstateFlow(
            saveEstateFlowConfiguration,
            federalEstateSettingsInput,
            undefined, // No state settings for federal
            undefined, // No basic info for federal
            clientMember.id
          );
        }

        return true;
      } else {
        logger.error(
          'Failed to save federal estate tax settings:',
          result.error
        );
        return false;
      }
    } catch (error) {
      logger.error('Failed to update federal estate tax settings:', error);
      return false;
    }
  }, [
    setClientSettings,
    clientMember?.id,
    memberAdminCosts,
    spouseAdminCosts,
    memberGrossEstate,
    spouseGrossEstate,
    saveEstateFlowConfiguration,
  ]);

  useEffect(() => {
    registerSubmitter(ClientSettingsTab.FederalEstateTax, handleSubmit);
  }, [handleSubmit, registerSubmitter]);

  const calculateRemainingExemption = (total: string, used: string) => {
    const totalNum = parseFloat(total) || 0;
    const usedNum = parseFloat(used) || 0;
    return Math.max(0, totalNum - usedNum);
  };

  const handleResetFederalEstate = () => {
    formik.resetForm();
  };

  const handleResetMember = () => {
    formik.setFieldValue('member.federalGiftAndEstateTaxExemptionUsed', '0');
    formik.setFieldValue(
      'member.addAvailableExemptionFromPredeceasedSpouse',
      false
    );
    formik.setFieldValue('member.availableDeceasedSpouseUnusedExclusion', '0');
  };

  const handleResetSpouse = () => {
    formik.setFieldValue('spouse.federalGiftAndEstateTaxExemptionUsed', '0');
    formik.setFieldValue(
      'spouse.addAvailableExemptionFromPredeceasedSpouse',
      false
    );
    formik.setFieldValue('spouse.availableDeceasedSpouseUnusedExclusion', '0');
  };

  const handleResetGST = () => {
    formik.setFieldValue('gst.federalGstTaxExemption', MAXIMUM_EXEMPTION);
    formik.setFieldValue('gst.memberGstExemptionUsed', '0');
    formik.setFieldValue('gst.spouseGstExemptionUsed', '0');
  };

  const handleSaveFederalEstate = async () => {
    const success = await handleSubmit();
    if (success) {
      logger.info('Federal estate tax settings saved successfully');
    }
  };

  const handleSaveMember = async () => {
    const success = await handleSubmit();
    if (success) {
      logger.info('Member settings saved successfully');
    }
  };

  const handleSaveSpouse = async () => {
    const success = await handleSubmit();
    if (success) {
      logger.info('Spouse settings saved successfully');
    }
  };

  const handleSaveGST = async () => {
    const success = await handleSubmit();
    if (success) {
      logger.info('GST settings saved successfully');
    }
  };

  if (isLoading) {
    return <FederalEstateTaxSkeleton />;
  }

  return (
    <FormikProvider value={formik}>
      <Grid container justifyContent="flex-start" gap={2}>
        {/* Federal Estate Tax Assumptions Card */}
        <FederalEstateCard
          title={t(
            'clientSettings.federalEstateTax.federalEstateTaxAssumptions'
          )}
          description={t(
            'clientSettings.federalEstateTax.federalEstateTaxAssumptionsDescription'
          )}
          onReset={handleResetFederalEstate}
          onSave={handleSaveFederalEstate}
        >
          <FieldWrapper
            formik={formik}
            name="federalTaxRate"
            label={t('clientSettings.federalEstateTax.federalTaxRate')}
            required
            containerSize={6}
          >
            <FormField
              formik={formik}
              name="federalTaxRate"
              label=""
              type="number"
              dataTestId="federal_tax_rate"
              hideLabel
              suffix="%"
            />
          </FieldWrapper>

          <FieldWrapper
            formik={formik}
            name="estimatedAdminCosts"
            label={t('clientSettings.federalEstateTax.estimatedAdminCosts')}
            required
            containerSize={6}
          >
            <FormField
              formik={formik}
              name="estimatedAdminCosts"
              label=""
              type="number"
              dataTestId="estimated_admin_costs"
              hideLabel
              suffix="%"
            />
          </FieldWrapper>

          <FieldWrapper
            formik={formik}
            name="federalGiftAndEstateTaxExemption"
            label={t(
              'clientSettings.federalEstateTax.federalGiftAndEstateTaxExemption'
            )}
            required
          >
            <FormField
              formik={formik}
              name="federalGiftAndEstateTaxExemption"
              label=""
              type="number"
              dataTestId="federal_gift_and_estate_tax_exemption"
              hideLabel
              isCurrency
              readOnly
              suffix="USD"
            />
          </FieldWrapper>

          {/* <Grid item xs={12}>
            <Box
              sx={{
                border: `1px solid ${wealthPalette.primary.light}`,
                borderRadius: 1,
                p: 1,
                backgroundColor: formik.values
                  .electPortabilityGiftEstateExemption
                  ? '#E7FFFD'
                  : undefined,
                borderColor: formik.values.electPortabilityGiftEstateExemption
                  ? '#5CB1AB'
                  : wealthPalette.primary.light,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Checkbox
                checked={formik.values.electPortabilityGiftEstateExemption}
                onChange={formik.handleChange}
                name="electPortabilityGiftEstateExemption"
                size="small"
                sx={{
                  color: wealthPalette.primary.main,
                  '&.Mui-checked': {
                    color: wealthPalette.state.success.base,
                  },
                }}
              />
              <Typography
                variant="body2"
                sx={{
                  flex: 1,
                  color: wealthPalette.text.primary,
                  fontWeight: 500,
                }}
              >
                {t(
                  'clientSettings.federalEstateTax.electPortabilityGiftEstateExemption'
                )}
              </Typography>
            </Box>
          </Grid> */}
        </FederalEstateCard>

        {/* Member Card */}
        <FederalEstateCard
          title={`${clientMemberName}`}
          icon={
            <FontAwesomeIcon
              icon={faCircleUser}
              color={wealthPalette.icon.soft}
            />
          }
          description={''}
          onReset={handleResetMember}
          onSave={handleSaveMember}
        >
          <FieldWrapper
            formik={formik}
            name="member.federalGiftAndEstateTaxExemptionUsed"
            label={t(
              'clientSettings.federalEstateTax.federalGiftAndEstateTaxExemptionUsed'
            )}
            required
          >
            <FormField
              formik={formik}
              name="member.federalGiftAndEstateTaxExemptionUsed"
              label=""
              type="number"
              dataTestId="member_federal_gift_and_estate_tax_exemption_used"
              hideLabel
              isCurrency
              suffix="USD"
            />
          </FieldWrapper>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="body2" color={wealthPalette.text.subtle}>
                {t('clientSettings.federalEstateTax.federalExemptionRemaining')}
                : $
                {calculateRemainingExemption(
                  formik.values.federalGiftAndEstateTaxExemption,
                  formik.values.member.federalGiftAndEstateTaxExemptionUsed
                ).toLocaleString()}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Box
              sx={{
                border: `1px solid ${wealthPalette.primary.light}`,
                borderRadius: 1,
                p: 1,
                backgroundColor: formik.values.member
                  .addAvailableExemptionFromPredeceasedSpouse
                  ? '#E7FFFD'
                  : undefined,
                borderColor: formik.values.member
                  .addAvailableExemptionFromPredeceasedSpouse
                  ? '#5CB1AB'
                  : wealthPalette.primary.light,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Checkbox
                checked={
                  formik.values.member
                    .addAvailableExemptionFromPredeceasedSpouse
                }
                onChange={formik.handleChange}
                name="member.addAvailableExemptionFromPredeceasedSpouse"
                size="small"
                sx={{
                  color: wealthPalette.primary.main,
                  '&.Mui-checked': {
                    color: wealthPalette.state.success.base,
                  },
                }}
              />
              <Typography
                variant="body2"
                sx={{
                  flex: 1,
                  color: wealthPalette.text.primary,
                  fontWeight: 500,
                }}
              >
                {t(
                  'clientSettings.federalEstateTax.addAvailableExemptionFromPredeceasedSpouse'
                )}
              </Typography>
            </Box>
          </Grid>

          {formik.values.member.addAvailableExemptionFromPredeceasedSpouse && (
            <FieldWrapper
              formik={formik}
              name="member.availableDeceasedSpouseUnusedExclusion"
              label={t(
                'clientSettings.federalEstateTax.availableDeceasedSpouseUnusedExclusion'
              )}
            >
              <FormField
                formik={formik}
                name="member.availableDeceasedSpouseUnusedExclusion"
                label=""
                type="number"
                dataTestId="member_available_deceased_spouse_unused_exclusion"
                hideLabel
                isCurrency
                suffix="USD"
              />
            </FieldWrapper>
          )}
        </FederalEstateCard>

        {/* Spouse Card - Only show if there's a spouse */}
        {clientSettings?.spouse && (
          <FederalEstateCard
            title={`${getSpouseName}`}
            icon={
              <FontAwesomeIcon
                icon={faCircleUser}
                color={wealthPalette.icon.soft}
              />
            }
            description={''}
            onReset={handleResetSpouse}
            onSave={handleSaveSpouse}
          >
            <FieldWrapper
              formik={formik}
              name="spouse.federalGiftAndEstateTaxExemptionUsed"
              label={t(
                'clientSettings.federalEstateTax.federalGiftAndEstateTaxExemptionUsed'
              )}
              required
            >
              <FormField
                formik={formik}
                name="spouse.federalGiftAndEstateTaxExemptionUsed"
                label=""
                type="number"
                dataTestId="spouse_federal_gift_and_estate_tax_exemption_used"
                hideLabel
                isCurrency
                suffix="USD"
              />
            </FieldWrapper>

            <Grid item xs={12}>
              <Box
                sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}
              >
                <Typography variant="body2" color={wealthPalette.text.subtle}>
                  {t(
                    'clientSettings.federalEstateTax.federalExemptionRemaining'
                  )}
                  : $
                  {calculateRemainingExemption(
                    formik.values.federalGiftAndEstateTaxExemption,
                    formik.values.spouse.federalGiftAndEstateTaxExemptionUsed
                  ).toLocaleString()}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Box
                sx={{
                  border: `1px solid ${wealthPalette.primary.light}`,
                  borderRadius: 1,
                  p: 1,
                  backgroundColor: formik.values.spouse
                    .addAvailableExemptionFromPredeceasedSpouse
                    ? '#E7FFFD'
                    : undefined,
                  borderColor: formik.values.spouse
                    .addAvailableExemptionFromPredeceasedSpouse
                    ? '#5CB1AB'
                    : wealthPalette.primary.light,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                }}
              >
                <Checkbox
                  checked={
                    formik.values.spouse
                      .addAvailableExemptionFromPredeceasedSpouse
                  }
                  onChange={formik.handleChange}
                  name="spouse.addAvailableExemptionFromPredeceasedSpouse"
                  size="small"
                  sx={{
                    color: wealthPalette.primary.main,
                    '&.Mui-checked': {
                      color: wealthPalette.state.success.base,
                    },
                  }}
                />
                <Typography
                  variant="body2"
                  sx={{
                    flex: 1,
                    color: wealthPalette.text.primary,
                    fontWeight: 500,
                  }}
                >
                  {t(
                    'clientSettings.federalEstateTax.addAvailableExemptionFromPredeceasedSpouse'
                  )}
                </Typography>
              </Box>
            </Grid>

            {formik.values.spouse
              .addAvailableExemptionFromPredeceasedSpouse && (
              <FieldWrapper
                formik={formik}
                name="spouse.availableDeceasedSpouseUnusedExclusion"
                label={t(
                  'clientSettings.federalEstateTax.availableDeceasedSpouseUnusedExclusion'
                )}
              >
                <FormField
                  formik={formik}
                  name="spouse.availableDeceasedSpouseUnusedExclusion"
                  label=""
                  type="number"
                  dataTestId="spouse_available_deceased_spouse_unused_exclusion"
                  hideLabel
                  isCurrency
                  suffix="USD"
                />
              </FieldWrapper>
            )}
          </FederalEstateCard>
        )}

        {/* GST Tax Assumptions Card */}
        <FederalEstateCard
          title={t('clientSettings.federalEstateTax.gstTaxAssumptions')}
          description={t(
            'clientSettings.federalEstateTax.gstTaxAssumptionsDescription'
          )}
          onReset={handleResetGST}
          onSave={handleSaveGST}
        >
          <FieldWrapper
            formik={formik}
            name="gst.federalGstTaxExemption"
            label={t('clientSettings.federalEstateTax.federalGstTaxExemption')}
            required
          >
            <FormField
              formik={formik}
              name="gst.federalGstTaxExemption"
              label=""
              type="number"
              dataTestId="federal_gst_tax_exemption"
              hideLabel
              isCurrency
              suffix="USD"
            />
          </FieldWrapper>

          <FieldWrapper
            formik={formik}
            name="gst.memberGstExemptionUsed"
            label={`${clientMemberName} ${t(
              'clientSettings.federalEstateTax.gstTaxExemptionUsed'
            )}`}
          >
            <FormField
              formik={formik}
              name="gst.memberGstExemptionUsed"
              label=""
              type="number"
              dataTestId="member_gst_exemption_used"
              hideLabel
              isCurrency
              suffix="USD"
            />
          </FieldWrapper>

          {clientSettings?.spouse && (
            <FieldWrapper
              formik={formik}
              name="gst.spouseGstExemptionUsed"
              label={`${getSpouseName} ${t(
                'clientSettings.federalEstateTax.gstTaxExemptionUsed'
              )}`}
            >
              <FormField
                formik={formik}
                name="gst.spouseGstExemptionUsed"
                label=""
                type="number"
                dataTestId="spouse_gst_exemption_used"
                hideLabel
                isCurrency
                suffix="USD"
              />
            </FieldWrapper>
          )}
        </FederalEstateCard>
      </Grid>
    </FormikProvider>
  );
}
