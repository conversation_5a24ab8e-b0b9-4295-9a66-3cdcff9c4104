import { useLazyQuery, useMutation } from '@apollo/client';
import { analyticsAtom } from 'atomStore';
import {
  topAccountContactCardsAtom,
  topAccountIdAtom,
} from 'atomStore/client/entitlements';
import { isAuditLogsUpdateAtom } from 'atomStore/contactModalHelper';
import { clientSettingsDataAtom } from 'atomStore/clientSettings';
import {
  ContactCard,
  DeleteContactCardsMutation,
  DeleteContactCardsMutationVariables,
  GetContactCardQuery,
  GetContactCardQueryVariables,
  GetPrimaryContactCardQuery,
  GetPrimaryContactCardQueryVariables,
  ListContactCardsQuery,
  ListContactCardsQueryVariables,
  ListVendorMetadataQuery,
  ListVendorMetadataQueryVariables,
  SaveContactCardMutation,
  SaveContactCardMutationVariables,
  SaveTrustInformationMutation,
  SaveTrustInformationMutationVariables,
} from 'generated/API';
import vaultPrivateClient from 'graphql/clients/vaultPrivateClient';
import {
  deleteContactCardsMutation,
  saveContactCardMutation,
  saveTrustInformationMutation,
} from 'graphql/mutations/vault';
import {
  getContactCardQuery,
  getContactCardV2Query,
  getPrimaryContactCardQuery,
  listContactCardsQuery,
  listVendorMetadataQuery,
} from 'graphql/queries/vault';
import { useAtom, useSetAtom } from 'jotai';
import { HeapCustomActionsEnum } from 'models/heap';
import { useState } from 'react';
import { assetsGQLAuthorization } from 'utils/asset';
import { makeNotNull } from 'utils/objectHelpers';
import { logger } from 'utils/logger';

export default function useContacts(includePrimary = true) {
  const [{ ajsTrack }] = useAtom(analyticsAtom);
  const [topAccountId] = useAtom(topAccountIdAtom);
  const setTopAccountContactCards = useSetAtom(topAccountContactCardsAtom);
  const setClientSettings = useSetAtom(clientSettingsDataAtom);
  const [contactCardsV2, setContactCardsV2] = useState<ContactCard[]>([]);
  const [isAuditLogsUpdate, setIsAuditLogUpdate] = useAtom(
    isAuditLogsUpdateAtom
  );
  const [
    getListContactCards,
    {
      loading: isListingContactCards,
      data: contactCards,
      error: listContactCardsError,
      refetch: refetchListContactCards,
    },
  ] = useLazyQuery<ListContactCardsQuery, ListContactCardsQueryVariables>(
    listContactCardsQuery,
    {
      client: vaultPrivateClient,
      variables: { includePrimary },
      fetchPolicy: 'cache-and-network',
      notifyOnNetworkStatusChange: true,
    }
  );

  const [
    getPrimaryContactCard,
    { loading: isGettingPrimaryContactCard, error: getPrimaryContactCardError },
  ] = useLazyQuery<
    GetPrimaryContactCardQuery,
    GetPrimaryContactCardQueryVariables
  >(getPrimaryContactCardQuery, {
    client: vaultPrivateClient,
    fetchPolicy: 'no-cache',
  });

  // Get primary contact Card (when id is provided)
  const [
    getContactCard,
    { loading: isGettingContactCard, error: getContactCardError },
  ] = useLazyQuery<GetContactCardQuery, GetContactCardQueryVariables>(
    getContactCardQuery,
    {
      client: vaultPrivateClient,
      fetchPolicy: 'no-cache',
    }
  );

  const [
    getContactCardV2,
    { loading: isGettingContactCardV2, error: getContactCardV2Error },
  ] = useLazyQuery(getContactCardV2Query, {
    client: vaultPrivateClient,
    fetchPolicy: 'no-cache',
  });

  const [listVendorMetadata] = useLazyQuery<
    ListVendorMetadataQuery,
    ListVendorMetadataQueryVariables
  >(listVendorMetadataQuery, {
    client: vaultPrivateClient,
    fetchPolicy: 'no-cache',
  });

  const getListContactCardsV2 = async (
    contactIds: string[],
    clientId: string
  ) => {
    const resp = await listVendorMetadata({
      variables: {
        ids: contactIds,
        ...(clientId ? { authorization: { asAdvisor: { clientId } } } : {}),
      },
    });
    const contactCardsData = makeNotNull(
      resp.data?.listVendorMetadata?.payload
    ) as ContactCard[];
    setContactCardsV2(contactCardsData);
  };

  // Function to update client settings from contact cards
  const updateClientSettingsFromContactCards = (
    contactCards: ContactCard[]
  ) => {
    if (!contactCards || contactCards.length === 0) return;

    // Find primary contact card
    const primaryContactCard = contactCards.find(
      (card) => card?.isPrimary && card?.id
    );

    if (!primaryContactCard) return;

    // Find spouse contact card using the partner.id from primary contact card
    let spouseContactCard: ContactCard | undefined;

    if (primaryContactCard.partner?.id) {
      // Find the spouse contact card by partner ID
      spouseContactCard = contactCards.find(
        (card) => card?.id === primaryContactCard.partner?.id
      );
    }

    // Build client settings data
    const clientSettingsData = {
      primary: {
        id: primaryContactCard.id || '',
        dob: primaryContactCard.dob ? primaryContactCard.dob.slice(0, 10) : '',
        stateOfResidence: primaryContactCard.addressStateShort || '',
        lifeExpectancy: '', // This would need to come from a different source
        maritalStatus: primaryContactCard.maritalStatus || '',
      },
      ...(spouseContactCard && {
        spouse: {
          id: spouseContactCard.id || '',
          dob: spouseContactCard.dob ? spouseContactCard.dob.slice(0, 10) : '',
          stateOfResidence: spouseContactCard.addressStateShort || '',
          lifeExpectancy: '', // This would need to come from a different source
          maritalStatus: spouseContactCard.maritalStatus || '',
        },
      }),
    };

    // Update client settings atom
    setClientSettings(clientSettingsData);
  };

  // Save Contact Card
  const [
    saveContactCard,
    {
      loading: isSavingContactCard,
      error: saveContactCardError,
      reset: resetSaveContactCard,
    },
  ] = useMutation<SaveContactCardMutation, SaveContactCardMutationVariables>(
    saveContactCardMutation,
    {
      client: vaultPrivateClient,
      onError: (error) => {
        logger.error(error);
        ajsTrack(HeapCustomActionsEnum.ContactCardFailure, {
          ErrorMessage: error.message || 'Unable to Save',
        });
        resetSaveContactCard();
      },
      update(cache, result, { variables }) {
        // Result contact card ID
        const resultContactCardId = result.data?.saveContactCard?.id;

        // If successful update operation
        if (resultContactCardId) {
          const authorization = assetsGQLAuthorization(
            variables?.authorization?.asAdvisor?.clientId
          );

          // Current cache data
          const existingData = cache.readQuery({
            query: listContactCardsQuery,
            variables: { ...authorization, includePrimary: true },
          }) as ListContactCardsQuery;

          // Check if we've got the items and if the specific item exists
          if (existingData && existingData.listContactCards?.payload) {
            // Find the index of the item with the given ID within the array
            const index = existingData.listContactCards?.payload.findIndex(
              (item) => item?.id === resultContactCardId
            );
            // Clone contact card items
            const clonedContactCardItems = [
              ...existingData.listContactCards?.payload,
            ];
            const contactCardAtIndex = clonedContactCardItems?.[index];

            // New variables
            const updatedContactCardVariables =
              result.data?.saveContactCard?.contactCard ?? variables?.metadata;

            // Contact Card has been found, so we need to update list
            if (index !== -1 && contactCardAtIndex) {
              // Update Cache reference Contact Card Entry e.g. ContactCard:{id} entry
              const currentCache = cache.extract();
              const contactCardCacheKey = `ContactCard:${resultContactCardId}`;
              if (currentCache[contactCardCacheKey]) {
                currentCache[contactCardCacheKey] = {
                  ...contactCardAtIndex,
                  ...updatedContactCardVariables,
                  // This is needed to retain the "old" trust information if it wasn't updated in this request
                  trustInformation:
                    updatedContactCardVariables?.trustInformation ??
                    contactCardAtIndex.trustInformation,
                  spouses: makeNotNull(
                    updatedContactCardVariables?.spouses?.map((s) => ({
                      __typename: 'Spouse',
                      ...s,
                      childrenIds: makeNotNull(s?.childrenIds),
                    }))
                  ),
                };
                cache.restore(currentCache);
              }

              // Update the CC at index
              clonedContactCardItems[index] = {
                ...contactCardAtIndex,
                ...updatedContactCardVariables,
                spouses:
                  updatedContactCardVariables?.spouses?.map((s) => ({
                    __typename: 'Spouse',
                    ...s,
                  })) ?? null,
                partnerId: updatedContactCardVariables?.partnerId ?? null,
              };

              // Write the updated items array back into the cache
              cache.writeQuery({
                query: listContactCardsQuery,
                variables: { ...authorization, includePrimary: true },
                data: {
                  listContactCards: {
                    __typename: 'ListContactCardsResponse',
                    payload: clonedContactCardItems,
                    error: existingData?.listContactCards?.error,
                    success: existingData?.listContactCards?.success,
                  },
                },
              });

              if (
                variables?.authorization?.asAdvisor?.clientId === topAccountId
              ) {
                setTopAccountContactCards(
                  clonedContactCardItems as ContactCard[]
                );
              }
              if (variables?.authorization?.asAdvisor?.clientId) {
                updateClientSettingsFromContactCards(
                  clonedContactCardItems as ContactCard[]
                );
              }
            } else {
              // Write the updated items array back into the cache
              cache.writeQuery({
                query: listContactCardsQuery,
                variables: { ...authorization, includePrimary: true },
                data: {
                  listContactCards: {
                    __typename: 'ListContactCardsResponse',
                    payload: makeNotNull([
                      ...clonedContactCardItems,
                      result.data?.saveContactCard?.contactCard,
                    ]),
                    error: existingData?.listContactCards?.error,
                    success: existingData?.listContactCards?.success,
                  },
                },
              });

              if (
                variables?.authorization?.asAdvisor?.clientId === topAccountId
              ) {
                setTopAccountContactCards(
                  makeNotNull([
                    ...clonedContactCardItems,
                    result.data?.saveContactCard?.contactCard,
                  ])
                );
              }

              // // // Contact Card is new - need to evict in order to update
              //  TODO: Backend should return the new updated item, then we can update the cache
              // cache.evict({
              //   id: 'ROOT_QUERY',
              //   fieldName: 'listContactCards',
              //   args: { ...authorization, includePrimary: true },
              //   broadcast: true,
              // });
              // cache.gc();
            }
          }
        }
      },
      refetchQueries: ['ListContactCards'],
      onCompleted: () => {
        setIsAuditLogUpdate(!isAuditLogsUpdate);
      },
    }
  );

  // Save Trust Information
  const [saveTrustInformation, { loading: isSavingTrustInformation }] =
    useMutation<
      SaveTrustInformationMutation,
      SaveTrustInformationMutationVariables
    >(saveTrustInformationMutation, {
      client: vaultPrivateClient,
      onError: (error) => {
        logger.error(error);
        ajsTrack(HeapCustomActionsEnum.TrustInformationFailure, {
          ErrorMessage: error.message || 'Unable to Save',
        });
        resetSaveContactCard();
      },
      update(cache, result, { variables }) {
        const clientId = variables?.authorization?.asAdvisor?.clientId;
        // Result contact card ID
        const resultContactCardId = result.data?.saveTrustInformation?.id;

        // If clientID and successful update operation
        if (clientId && resultContactCardId) {
          const authorization = assetsGQLAuthorization(clientId);

          // Current cache data
          const existingData = cache.readQuery({
            query: listContactCardsQuery,
            variables: { ...authorization, includePrimary: true },
          }) as ListContactCardsQuery;

          // Check if we've got the items and if the specific item exists
          if (existingData && existingData.listContactCards?.payload) {
            // Find the index of the item with the given ID within the array
            const index = existingData.listContactCards?.payload.findIndex(
              (item) => item?.id === resultContactCardId
            );
            // Clone contact card items
            const clonedContactCardItems = [
              ...existingData.listContactCards?.payload,
            ];
            const contactCardAtIndex = clonedContactCardItems?.[index];

            // Contact Card has been found, so we need to update list
            if (index !== -1 && contactCardAtIndex) {
              // Update Cache reference Contact Card Entry e.g. ContactCard:{id} entry
              const currentCache = cache.extract();
              const contactCardCacheKey = `ContactCard:${resultContactCardId}`;
              if (currentCache[contactCardCacheKey]) {
                currentCache[contactCardCacheKey] = {
                  ...contactCardAtIndex,
                  trustInformation: variables?.trustInformation,
                };
                cache.restore(currentCache);
              }

              // Update the CC at index
              clonedContactCardItems[index] = {
                ...contactCardAtIndex,
                trustInformation: variables?.trustInformation,
              };

              // Write the updated items array back into the cache
              cache.writeQuery({
                query: listContactCardsQuery,
                variables: { ...authorization, includePrimary: true },
                data: {
                  listContactCards: {
                    __typename: 'ListContactCardsResponse',
                    payload: clonedContactCardItems,
                    error: existingData?.listContactCards?.error,
                    success: existingData?.listContactCards?.success,
                  },
                },
              });

              if (
                variables?.authorization?.asAdvisor?.clientId === topAccountId
              ) {
                setTopAccountContactCards(
                  clonedContactCardItems as ContactCard[]
                );
              }
              if (variables?.authorization?.asAdvisor?.clientId) {
                updateClientSettingsFromContactCards(
                  clonedContactCardItems as ContactCard[]
                );
              }
            }
          }
        }
      },
      refetchQueries: ['ListContactCards'],
    });

  // Delete Contact Card
  const [
    deleteContactCards,
    {
      loading: isDeletingContactCards,
      error: deleteContactCardsError,
      reset: resetDeleteContactCards,
    },
  ] = useMutation<
    DeleteContactCardsMutation,
    DeleteContactCardsMutationVariables
  >(deleteContactCardsMutation, {
    client: vaultPrivateClient,
    onError: (error) => {
      logger.error(error);
      ajsTrack(HeapCustomActionsEnum.DeleteContactFailure, {
        ErrorMessage: error.message || 'Unable to Delete',
      });
      resetDeleteContactCards();
    },
    refetchQueries: ['ListContactCards'],
  });

  return {
    getListContactCards,
    getListContactCardsV2,
    isListingContactCards,
    listContactCardsError,
    contactCards,
    getPrimaryContactCard,
    getContactCard,
    getContactCardV2,
    contactCardsV2,
    isGettingPrimaryContactCard,
    isGettingContactCard,
    isGettingContactCardV2,
    getPrimaryContactCardError,
    getContactCardError,
    getContactCardV2Error,
    saveContactCard,
    isSavingContactCard,
    saveTrustInformation,
    isSavingTrustInformation,
    saveContactCardError,
    deleteContactCards,
    isDeletingContactCards,
    deleteContactCardsError,
    refetchListContactCards,
  };
}
